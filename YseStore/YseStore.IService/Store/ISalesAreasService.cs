using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.VM;
using YseStore.Model.VM.Setting;

namespace YseStore.IService.Store
{
    /// <summary>
    /// 销售分区服务接口
    /// </summary>
    public interface ISalesAreasService : IBaseServices<Entitys.sales_areas>
    {

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="salesArea"></param>
        /// <returns></returns>
        Task<WebApiCallBack> OnSaveSalesArea(sales_areas salesArea);


        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<WebApiCallBack> OnDeleteSalesArea(List<int> id);

        /// <summary>
        /// 获取销售分区列表
        /// </summary>
        /// <returns></returns>
        Task<List<sales_areas>> GetSalesAreasCache();


        /// <summary>
        /// 获取销售分区详情
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<sales_areas> GetSalesAreasInfoCache(string code);

        /// <summary>
        /// 获取销售分区详情
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        Task<sales_areas> GetSalesAreasInfoCache(int Id);


        /// <summary>
        /// 清空缓存
        /// </summary>
        /// <returns></returns>
        Task ClearCache();

    }
}
