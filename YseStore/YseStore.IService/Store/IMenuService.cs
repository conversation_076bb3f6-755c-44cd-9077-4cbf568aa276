using Entitys;
using System.Collections.Generic;
using System.Threading.Tasks;
using YseStore.Model;
using YseStore.Model.Response.Store;

namespace YseStore.IService.Store
{
    /// <summary>
    /// 菜单服务接口
    /// </summary>
    public interface IMenuService : IBaseServices<menu>
    {
        /// <summary>
        /// 获取导航菜单
        /// </summary>
        /// <param name="language">语言代码，默认为"en"</param>
        /// <returns>导航菜单列表</returns>
        Task<List<MenuResponse>> GetNavMenusAsync(string language = "en");

        /// <summary>
        /// 获取底部导航菜单
        /// </summary>
        /// <param name="language">语言代码，默认为"en"</param>
        /// <returns>底部导航菜单列表</returns>
        Task<List<MenuResponse>> GetFooterNavMenusAsync(string language = "en");

        /// <summary>
        /// 获取菜单详情
        /// </summary>
        /// <param name="mId">菜单ID</param>
        /// <returns>菜单详情</returns>
        Task<menu> GetMenuByIdAsync(int mId);

        /// <summary>
        /// 获取子菜单列表
        /// </summary>
        /// <param name="parentId">父级菜单ID</param>
        /// <returns>子菜单列表</returns>
        Task<List<menu>> GetChildMenusAsync(int parentId);

        /// <summary>
        /// 添加或更新菜单
        /// </summary>
        /// <param name="menuEntity">菜单实体</param>
        /// <returns>是否成功</returns>
        Task<bool> SaveMenuAsync(menu menuEntity);

        /// <summary>
        /// 删除菜单
        /// </summary>
        /// <param name="mId">菜单ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteMenuAsync(int mId);

        /// <summary>
        /// 更新菜单排序
        /// </summary>
        /// <param name="mId">菜单ID</param>
        /// <param name="order">排序值</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateMenuOrderAsync(int mId, sbyte order);

        /// <summary>
        /// 批量更新菜单排序
        /// </summary>
        /// <param name="sortOrders">排序字典，键为菜单ID，值为排序值</param>
        /// <param name="menuType">菜单类型（nav或footer_nav）</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateMenuOrdersBatchAsync(Dictionary<int, int> sortOrders, string menuType);

        /// <summary>
        /// 清理菜单相关缓存
        /// </summary>
        /// <returns></returns>
        Task ClearMenuCacheAsync();
    }
}