using Entitys;
using YseStore.Model;
using YseStore.Model.RequestModels.Store;
using YseStore.Model.Response.Store;

namespace YseStore.IService.Store
{
    /// <summary>
    /// 文章服务接口
    /// </summary>
    public interface IArticleService
    {
        /// <summary>
        /// 获取文章列表
        /// </summary>
        /// <param name="request">文章查询请求参数</param>
        /// <returns>分页文章列表</returns>
        Task<PageModel<article>> GetArticleListAsync(ArticleQueryRequest request);


        /// <summary>
        /// 
        /// </summary>
        /// <param name="aId"></param>
        /// <returns></returns>
        Task<article> GetArticleByIdAsync(int aId);

        /// <summary>
        /// 获取文章标签列表
        /// </summary>
        /// <returns>标签列表</returns>
        Task<List<article_tags>> GetArticleTagsAsync();

        /// <summary>
        /// 获取文章完整详情（使用响应模型）
        /// </summary>
        /// <param name="aId">文章ID</param>
        /// <returns>文章详情响应模型</returns>
        Task<ArticleDetailResponse> GetArticleDetailResponseAsync(int aId);

        /// <summary>
        /// 根据URL获取文章完整详情（使用响应模型）
        /// </summary>
        /// <param name="pageUrl">文章URL</param>
        /// <returns>文章详情响应模型</returns>
        Task<ArticleDetailResponse> GetArticleDetailByUrlAsync(string pageUrl);

        /// <summary>
        /// 更新文章信息（包括内容）
        /// </summary>
        /// <param name="articleDetail">文章详情</param>
        /// <returns>是否更新成功</returns>
        Task<bool> UpdateArticleAsync(ArticleDetailResponse articleDetail);

        /// <summary>
        /// 根据ID数组删除文章
        /// </summary>
        /// <param name="ids">文章ID数组</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteByIds(object[] ids);
    }
}