using YseStore.Model;
using YseStore.Model.RequestModels.Products;
using YseStore.Model.Response.Products;
using Microsoft.AspNetCore.Mvc.Rendering;
using Entitys;

namespace YseStore.IService.Products
{
    /// <summary>
    /// 产品库存服务接口
    /// </summary>
    public interface IProductStockService : IBaseServices<products_selected_attribute_combination>
    {
        /// <summary>
        /// 获取产品库存列表，支持搜索和分页
        /// </summary>
        /// <param name="queryRequest">查询请求参数</param>
        /// <returns>分页后的产品库存列表</returns>
        Task<PageModel<ProductStockResponse>> GetProductStockList(ProductStockQueryRequest queryRequest);

        /// <summary>
        /// 根据组合ID获取产品库存详情
        /// </summary>
        /// <param name="cId">组合ID</param>
        /// <returns>产品库存详情</returns>
        Task<ProductStockResponse> GetProductStockById(int cId);
        
        /// <summary>
        /// 获取仓库列表，用于下拉选择
        /// </summary>
        /// <param name="selectedOvId">当前选中的仓库ID</param>
        /// <returns>仓库下拉列表项</returns>
        Task<List<SelectListItem>> GetWarehouseListAsync(int selectedOvId = 0);
        
        /// <summary>
        /// 批量更新产品库存设置
        /// </summary>
        /// <param name="request">库存更新请求</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateProductStockBatchAsync(ProductStockUpdateRequest request);


        /// <summary>
        /// 全站更新产品库存设置
        /// </summary>
        /// <param name="request">库存更新请求</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateAllProductStockAsync(ProductStockUpdateRequest request);

        /// <summary>
        /// 更新表格中行内编辑的产品库存项
        /// </summary>
        /// <param name="request">产品库存批量调整请求</param>
        /// <returns>是否更新成功</returns>
        Task<bool> UpdateProductStockItemsAsync(ProductStockBatchUpdateRequest request);
    }
} 