using Entitys;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace YseStore.IService.Products
{
    /// <summary>
    /// Product Switch Content 服务接口
    /// </summary>
    public interface IProductsSwitchContentService : IBaseServices<products_switch_content>
    {
        /// <summary>
        /// 获取所有产品切换内容信息
        /// </summary>
        /// <returns>产品切换内容信息列表</returns>
        Task<List<products_switch_content>> GetAllAsync();

        /// <summary>
        /// 根据ID获取产品切换内容信息
        /// </summary>
        /// <param name="cId">产品切换内容ID</param>
        /// <returns>单个产品切换内容信息</returns>
        Task<products_switch_content> GetByIdAsync(int cId);

        /// <summary>
        /// 根据切换卡ID获取产品切换内容信息列表
        /// </summary>
        /// <param name="sId">切换卡ID</param>
        /// <returns>产品切换内容信息列表</returns>
        Task<List<products_switch_content>> GetBySIdAsync(int sId);
    }
} 