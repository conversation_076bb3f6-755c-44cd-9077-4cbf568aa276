namespace YseStore.IService.Products
{
    /// <summary>
    /// 产品SEO服务接口
    /// </summary>
    public interface IProductSeoService
    {
        /// <summary>
        /// 检查产品URL是否存在
        /// </summary>
        /// <param name="productUrl">产品URL</param>
        /// <param name="currentProductId">当前产品ID（更新时使用，新增时为0）</param>
        /// <returns>是否存在</returns>
        Task<bool> IsProductUrlExistAsync(string productUrl, int currentProductId = 0);

        /// <summary>
        /// 根据URL获取产品ID
        /// </summary>
        /// <param name="productUrl">产品URL</param>
        /// <returns>产品ID</returns>
        Task<int> GetProductIdByUrlAsync(string productUrl);

        /// <summary>
        /// 更新产品URL缓存
        /// </summary>
        /// <param name="productUrl">产品URL</param>
        /// <param name="productId">产品ID</param>
        void UpdateProductUrlCache(string productUrl, int productId);

        /// <summary>
        /// 移除产品URL缓存
        /// </summary>
        /// <param name="productUrl">产品URL</param>
        void RemoveProductUrlCache(string productUrl);

        /// <summary>
        /// 清除所有产品URL缓存
        /// </summary>
        void ClearAllProductUrlCache();
    }
}
