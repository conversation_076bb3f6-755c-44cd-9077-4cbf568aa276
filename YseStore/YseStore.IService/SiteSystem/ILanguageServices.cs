using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.IService.SiteSystem
{
    /// <summary>
    /// 语言服务接口
    /// </summary>
    public interface ILanguageServices : IBaseServices<Entitys.language>
    {

        /// <summary>
        /// 获取所有语言缓存
        /// </summary>
        /// <returns></returns>
        Task<List<language>> GetAllLangsCache();

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<List<language>> GetUsedLangsCache();

        Task<language> GetDefaultLang();

        /// <summary>
        /// 获取指定语言的语言信息
        /// </summary>
        /// <param name="lang"></param>
        /// <returns></returns>
        Task<language> GetLang(string lang);

        /// <summary>
        /// 清空缓存
        /// </summary>
        /// <returns></returns>
        Task ClearCache();

    }
}
