using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.Dto;
using YseStore.Model.VM;

namespace YseStore.IService.Order
{
    /// <summary>
    /// 订单时间同步
    /// </summary>
    public interface IOrderSyncTimeService : IBaseServices<orders_sync_time>
    {


        /// <summary>
        /// 获取订单同步时间
        /// </summary>
        /// <returns></returns>
        Task<orders_sync_time> GetOrderSyncTime();


        /// <summary>
        /// 设置订单同步时间
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        Task<WebApiCallBack> SetSiteOrderSyncTime(DateTime startTime, DateTime endTime);
    }
}
