using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.IService
{
    public interface IConfigService : IBaseServices<config>
    {
        /// <summary>
        /// 基础配置--协议显示位置
        /// </summary>
        /// <returns></returns>
        Task<config> GetConfigAry();
        /// <summary>
        /// 更改基础配置--协议显示位置
        /// </summary>
        /// <param name="IsBottom"></param>
        Task<bool> UpdateConfig(string IsBottom);

        /// <summary>
        /// 获取全部配置
        /// </summary>
        /// <returns></returns>
        Task<List<config>> GetConfig();

        /// <summary>
        /// 获取基础配置
        /// </summary>
        /// <param name="groupId"></param>
        /// <param name="variable"></param>
        /// <returns></returns>
        Task<config> GetConfigByGroup(string groupId, string variable);


        /// <summary>
        /// 获取基础配置
        /// </summary>
        /// <param name="groupId"></param>
        /// <param name="variable"></param>
        /// <returns></returns>
        Task<string> GetConfigValueByGroup(string groupId, string variable);

        /// <summary>
        /// 保存设置
        /// </summary>
        /// <param name="groupId"></param>
        /// <param name="variable"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        Task<bool> SetConfig(string groupId, string variable ,string value);
    }
}
