using Entitys;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.Response.Sales;
using YseStore.Model.VM;

namespace YseStore.IService.Sales
{
    public interface ISalesDiscountService
    {
        Task<PagedList<full_reduction>> QueryAsync(
            string keyword = "", int pageNum = 1, int pageSize = 50, int orderBy = 1, string field = "AccTime");

        Task<ValueTuple<List<DiscountProductSelect>, int>> GetProductSelectList(string keyword, int page = 1, int pageSize = 10, string filterProId = "", int cateId = 0, string filterTagId = "");

        Task<List<DiscountProductSelect>> GetProductSelectedList(List<int> productIds);

        Task<List<full_reduction>> QueryAsyncByCondition(Expressionable<full_reduction> expressionable);

        Task<List<products_category>> GetProductCategorySelectedList(List<int> cateIds);

        Task<(bool, string?, int)> SaveDiscount(full_reduction obj);


        Task<full_reduction> GetFullreductionInfoAsync(int fid);

        Task<DiscountRuleProduct> GetDiscountProductSelected(int productId);

        Task<(bool, string?)> BatchDeleteDiscountAsync(List<int> fIds);
    }
}
