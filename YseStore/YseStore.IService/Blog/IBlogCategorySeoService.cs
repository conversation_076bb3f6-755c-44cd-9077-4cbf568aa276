using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.IService.Blog
{
    /// <summary>
    /// 博客分类SEO服务接口
    /// </summary>
    public interface IBlogCategorySeoService
    {
        /// <summary>
        /// 检查博客分类PageUrl是否存在
        /// </summary>
        /// <param name="pageUrl">分类PageUrl</param>
        /// <param name="currentCategoryId">当前分类ID（更新时使用，新增时为0）</param>
        /// <returns>是否存在</returns>
        Task<bool> IsCategoryPageUrlExistAsync(string pageUrl, short currentCategoryId = 0);

        /// <summary>
        /// 根据PageUrl获取博客分类ID
        /// </summary>
        /// <param name="pageUrl">分类PageUrl</param>
        /// <returns>分类ID</returns>
        Task<short> GetCategoryIdByPageUrlAsync(string pageUrl);

        /// <summary>
        /// 生成唯一的博客分类PageUrl
        /// </summary>
        /// <param name="baseUrl">基础URL</param>
        /// <param name="currentCategoryId">当前分类ID（更新时使用，新增时为0）</param>
        /// <returns>唯一的PageUrl</returns>
        Task<string> GenerateUniqueCategoryPageUrlAsync(string baseUrl, short currentCategoryId = 0);

        /// <summary>
        /// 更新博客分类PageUrl缓存
        /// </summary>
        /// <param name="pageUrl">分类PageUrl</param>
        /// <param name="categoryId">分类ID</param>
        void UpdateCategoryPageUrlCache(string pageUrl, short categoryId);

        /// <summary>
        /// 移除博客分类PageUrl缓存
        /// </summary>
        /// <param name="pageUrl">分类PageUrl</param>
        void RemoveCategoryPageUrlCache(string pageUrl);

        /// <summary>
        /// 清除所有博客分类PageUrl缓存
        /// </summary>
        void ClearAllCategoryPageUrlCache();
    }
} 