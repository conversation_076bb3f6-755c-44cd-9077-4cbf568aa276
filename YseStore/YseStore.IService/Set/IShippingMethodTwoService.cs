using System.Threading.Tasks;
using Entitys;
using YseStore.Model.RequestModels.Set;
using YseStore.Model.Response.Set;

namespace YseStore.IService.Set
{
    /// <summary>
    /// 运费方法服务接口 - 第二版本
    /// 基于OrderListService.GetShippingMethod方法实现
    /// </summary>
    public interface IShippingMethodTwoService : IBaseServices<shipping>
    {
        /// <summary>
        /// 获取运费方法
        /// </summary>
        /// <param name="request">运费方法请求参数</param>
        /// <returns>运费方法响应</returns>
        Task<ShippingMethodResponse> GetShippingMethodsAsync(ShippingMethodRequest request);
    }
}
