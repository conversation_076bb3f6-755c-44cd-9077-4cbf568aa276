using Entitys;

namespace YseStore.IService.Set
{
    /// <summary>
    /// 运费信息服务接口
    /// </summary>
    public interface IShippingPriceService
    {
        /// <summary>
        /// 获取物流分区的运费信息列表
        /// </summary>
        /// <param name="areaId">分区ID</param>
        /// <returns>运费信息列表</returns>
        Task<List<shipping_price>> GetShippingPriceByAreaIdAsync(int areaId);

        /// <summary>
        /// 生成运费信息文本
        /// </summary>
        /// <param name="shippingPriceList">运费信息列表</param>
        /// <returns>格式化的运费信息文本</returns>
        Task<string> GenerateShippingInfoText(List<shipping_price> shippingPriceList);

        /// <summary>
        /// 生成免费额度信息文本
        /// </summary>
        /// <param name="area">物流分区信息</param>
        /// <returns>格式化的免费额度信息文本</returns>
        Task<string> GenerateFreeShippingInfoText(shipping_area area);
    }
} 