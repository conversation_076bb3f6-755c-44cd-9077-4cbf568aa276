using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.VM;

namespace YseStore.IService
{
    public interface ICurrencyService : IBaseServices<currency>
    {
        Task<IList<VM_Currency>> GetAllCurrency();


        /// <summary>
        ///  获取所有货币缓存列表
        /// </summary>
        /// <returns></returns>
        Task<IList<currency>> GetAllCurrencyCache();

        /// <summary>
        /// 获取启用的货币缓存列表
        /// </summary>
        /// <returns></returns>
        Task<IList<currency>> GetUsedCurrencyCache();

        /// <summary>
        /// 获取默认货币
        /// </summary>
        /// <returns></returns>
        Task<currency> GetDefaultCurrency();


        /// <summary>
        /// 获取后台默认币种
        /// </summary>
        /// <returns></returns>
        Task<currency> GetManageDefaultCurrency();

        /// <summary>
        /// 
        /// </summary>
        /// <param name="currency"></param>
        /// <returns></returns>
        Task<currency> GetCurrency(string currency);

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task ClearCurrencyCache();

        /// <summary>
        /// 获取货币转换率
        /// </summary>
        /// <param name="amount"></param>
        /// <param name="currency"></param>
        /// <param name="isRate"></param>
        /// <returns></returns>
        Task<decimal> GetConvertedPrice(decimal amount, string currency, string toCurrency);

        /// <summary>
        /// 货币转换率
        /// </summary>
        /// <param name="amount">金额</param>
        /// <param name="currency">要转换的币种</param>
        /// <param name="deftCurrency">前台默认币种</param>
        /// <param name="isRate"></param>
        /// <returns></returns>
        decimal GetConvertedPrice(decimal amount, currency currency, currency deftCurrency);


        /// <summary>
        /// 获取转换后的价格字符串
        /// </summary>
        /// <param name="amount">金额</param>
        /// <param name="currency">要转换的币种</param>
        /// <param name="deftCurrency">后台默认币种</param>
        /// <param name="isRate"></param>
        /// <returns></returns>
        ValueTuple<decimal, string> ShowPriceFormat(decimal amount, currency currency, currency deftCurrency);


        /// <summary>
        /// 格式化金额字符串，不带币种
        /// </summary>
        /// <param name="prePrice"></param>
        /// <param name="currency"></param>
        /// <returns></returns>
        string GetFormatAmount(decimal prePrice, string currency= "USD");

        /// <summary>
        /// 货币转换
        /// </summary>
        /// <param name="amount"></param>
        /// <param name="fromCurrency"></param>
        /// <returns></returns>
        Task<decimal> ConvertCurrency(decimal amount, string fromCurrency, string defaultCurrency);


        /// <summary>
        /// 货币转换
        /// </summary>
        /// <param name="amount"></param>
        /// <param name="currency"></param>
        /// <returns></returns>
        decimal ConvertCurrency(decimal amount, currency currency, currency defaultCurrency);
    }
}
