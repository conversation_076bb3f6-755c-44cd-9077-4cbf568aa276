using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model;

namespace YseStore.IService.Customer
{
    /// <summary>
    /// 表单工具数据服务接口
    /// </summary>
    public interface IFormToolDataService : IBaseServices<app_form_tool_data>
    {


        /// <summary>
        /// 获取表单工具列表
        /// </summary>
        /// <param name="criteria"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<PageModel<app_form_tool_data>> GetFormToolData(Dictionary<string, string> criteria, int pageIndex, int pageSize);


    }
}
