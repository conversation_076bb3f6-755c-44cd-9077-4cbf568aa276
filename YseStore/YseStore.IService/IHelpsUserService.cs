using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.IService
{
    public interface IHelpsUserService
    {
        /// <summary>
        /// 查询用户标签
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="type"></param>
        /// <param name="label"></param>
        /// <returns></returns>
        int CountCustomerLabel(int userId, string type, string label);
        /// <summary>
        /// 会员权益
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        bool GetUserFreeShipping(int userId);
        /// <summary>
        /// 成功下单获取积分
        /// </summary>
        /// <param name="getTime">获得积分的时间</param>
        /// <param name="ordersRow">订单数据</param>
        void PointsPlaceorder(string getTime, orders ordersRow, decimal amount = 0m);
        /// <summary>
        /// 更新优惠券过期状态
        /// </summary>
        /// <param name="couponRow">优惠券记录</param>
        /// <param name="orderId">订单ID</param>
        /// <param name="tempOrderId">临时订单ID</param>
        void UpdateCouponStatus(dynamic couponRow, int orderId = 0, int tempOrderId = 0);










    }
}
