using Entitys;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.IService;
using YseStore.Service.Order;

namespace YseStore.Service.HelpsOrder
{

    public class HelpsCartService : IHelpsCartService
    {
        //private readonly ISqlSugarClient db;
        private Lazy<ICurrencyService> CurrencyService { get; set; }
        public HelpsCartService(Lazy<ICurrencyService> currencyService)
        {
            CurrencyService = currencyService;

        }

        /**
 * 价格格式显示
 *
 * @param float $Price			价格
 * @param integer $Method		0 符号+价格，1 符号，2 价格
 * @param string $Currency		货币缩写
 * @param float $Rate			来源货币的汇率
 * @param float $ToRate			目标货币的汇率
 * @return string
 */
        /// <summary>
        /// 价格格式显示
        /// </summary>
        /// <param name="price">价格</param>
        /// <param name="method">0 符号+价格，1 符号，2 价格</param>
        /// <param name="currency">货币缩写</param>
        /// <param name="rate">来源货币的汇率</param>
        /// <param name="toRate">目标货币的汇率</param>
        /// <returns></returns>
        public string IconvPrice(decimal price, int method = 0, string currency = "", decimal rate = 0, decimal toRate = 0)
        {
            if (price == 0)
            {
                return "0";
            }
            string result = "";
            currency currencyInfo = null;
            string symbol = "";
            // 1. 货币查询逻辑
            // 优先查询指定货币
            if (!string.IsNullOrEmpty(currency))
            {
                currencyInfo = CurrencyService.Value.QueryByClause(it => it.Currency == currency);

            }
            // 最后查询默认货币
            if (currencyInfo == null)
            {
                currencyInfo = CurrencyService.Value.QueryByClause(c => c.IsUsed == true && c.ManageDefault == true);

            }
            // 2. 汇率计算逻辑
            decimal exchangeRate = 0;
            if (rate > 0 && toRate > 0)
            {
                // 货币间汇率转换
                exchangeRate = 100m / ((100m / toRate / 100m) * rate) / 100m;

                // 处理科学计数法
                if (exchangeRate.ToString().Contains('-'))
                {
                    exchangeRate = decimal.Round(exchangeRate, 6);
                }
                else
                {
                    exchangeRate = decimal.Round(exchangeRate, 4);
                }
            }
            else if (rate > 0)
            {
                exchangeRate = rate;
            }
            else
            {
                exchangeRate = currencyInfo?.Rate ?? 0;
            }

            // 3. 符号处理
            symbol = currencyInfo?.Symbol ?? "";

            // 4. 价格处理
            //price = CeilPrice(price); // 调用向上取整方法

            switch (method)
            {
                case 0: // 符号+价格
                    //price = Math.Floor(price * exchangeRate * 10000);
                    price = price * exchangeRate * 10000;
                    result = $"{symbol}{CurrencyFormatTWO(Math.Round(price / 100m / 100m, 2), currencyInfo.Currency)}";
                    break;

                case 1: // 仅符号
                    result = symbol;
                    break;

                default: // 仅价格
                         //price = Math.Floor(price * exchangeRate * 10000);
                    price = price * exchangeRate * 10000;
                    result = CurrencyFormatTWO(Math.Round(price / 100m / 100m, 2), currencyInfo.Currency);
                    break;
            }
            return result;
        }


        /// <summary>
        /// 价格格式显示 货币格式显示
        /// </summary>
        /// <param name="price"></param>
        /// <param name="method"></param>
        /// <param name="currency"></param>
        /// <param name="rate"></param>
        /// <param name="toRate"></param>
        /// <returns></returns>
        public async Task<string> IconvPriceFormat(decimal price, int method = 0, string currency = "", decimal rate = 0, decimal toRate = 0)
        {
            if (price == 0)
            {
                return "0";
            }
            string result = "";
            currency currencyInfo = null;
            string symbol = "";
            // 1. 货币查询逻辑
            // 优先查询指定货币
            if (!string.IsNullOrEmpty(currency))
            {
                currencyInfo = await CurrencyService.Value.GetCurrency(currency);

            }
            //var currList = await _CurrencyService.Value.GetAllCurrencyCache();
            // 最后查询默认货币
            if (currencyInfo == null)
            {
                currencyInfo = await CurrencyService.Value.GetManageDefaultCurrency();
            }

            // 2. 汇率计算逻辑
            decimal exchangeRate = 0;
            if (rate > 0 && toRate > 0)
            {
                // 货币间汇率转换
                exchangeRate = 100m / ((100m / toRate / 100m) * rate) / 100m;

                // 处理科学计数法
                if (exchangeRate.ToString().Contains('-'))
                {
                    exchangeRate = decimal.Round(exchangeRate, 6);
                }
                else
                {
                    exchangeRate = decimal.Round(exchangeRate, 4);
                }
            }
            else if (rate > 0)
            {
                exchangeRate = rate;
            }
            else
            {
                exchangeRate = currencyInfo?.Rate ?? 0;
            }


            // 3. 符号处理
            symbol = currencyInfo?.Symbol ?? "";


            // 4. 价格处理
            //price = CeilPrice(price); // 调用向上取整方法

            switch (method)
            {
                case 0: // 符号+价格
                    //price = Math.Floor(price * exchangeRate * 10000);
                    price = price * exchangeRate * 10000;
                    result = $"{symbol}{CurrencyFormatTWO(Math.Round(price / 100m / 100m, 2), currencyInfo.Currency)}";
                    break;

                case 1: // 仅符号
                    result = symbol;
                    break;

                default: // 仅价格
                         //price = Math.Floor(price * exchangeRate * 10000);
                    price = price * exchangeRate * 10000;
                    result = CurrencyFormatTWO(Math.Round(price / 100m / 100m, 2), currencyInfo.Currency);
                    break;
            }
            return result;
        }



        /// <summary>
        /// 查询后台默认货币
        /// </summary>
        /// <returns></returns>
        public currency GetManageDefaultCurrency()
        {
            var currencyInfo = CurrencyService.Value.QueryByClause(c => c.IsUsed == true && c.ManageDefault == true);
            return currencyInfo;
        }

        /// <summary>
        /// 价格保留两位小数并向上取整（PHP转C#实现）
        /// </summary>
        public decimal CeilPrice(decimal price)
        {
            if (price == 0)
            {
                return 0;
            }
            // 将价格放大10000倍并向下取整（处理浮点精度）
            decimal scaled = price * 10000m;
            decimal truncated = Math.Floor(scaled);

            // 缩小100倍后向上取整，再缩小100倍得到两位小数
            decimal divided = truncated / 100m;
            decimal ceiled = Math.Ceiling(divided);
            decimal final = ceiled / 100m;

            // 格式化为两位小数并返回
            return Convert.ToDecimal(final.ToString("0.00"));
        }

        /// <summary>
        /// 根据货币类型格式化价格（含向上取整逻辑）
        /// </summary>
        public decimal CurrencyFloatPrice(decimal price, string currency = "")
        {
            // 定义需要整数显示的货币列表
            List<string> integerCurrencies = new List<string>
        {
            "CLP", "NOK", "DKK", "COP", "JPY",
            "SEK", "KRW", "INR", "TWD", "VND"
        };

            // 货币在整数列表中时使用天花板取整
            if (!string.IsNullOrEmpty(currency) && integerCurrencies.Contains(currency))
            {
                return Math.Ceiling(price);
            }

            // 其他货币使用自定义的ceilPrice逻辑
            if (!string.IsNullOrEmpty(currency))
            {
                return CeilPrice(price);
            }

            return price;
        }


        public string CurrencyFormatTWO(decimal price, string currency = "")
        {
            var format = (NumberFormatInfo)CultureInfo.InvariantCulture.NumberFormat.Clone();
            var currencyCode = currency.ToUpperInvariant();
            price = CurrencyFloatPrice(price, currency);
            switch (currencyCode)
            {
                case "USD": /* 其他标准货币 */
                    ConfigureStandardFormat(format, 2, ".", ",");
                    break;

                case "RUB":
                    format.NumberDecimalDigits = 2;
                    format.NumberDecimalSeparator = ",";
                    format.NumberGroupSeparator = " ";
                    break;

                case "EUR": /* 欧洲货币 */
                    format.NumberDecimalDigits = 2;
                    format.NumberDecimalSeparator = ",";
                    format.NumberGroupSeparator = ".";
                    break;

                // 零小数货币的修复方案
                case "CLP":
                case "NOK":
                case "DKK":
                case "COP":
                case "VND":
                    ConfigureZeroDecimalFormat(format, ".", 0); // 添加默认分隔符
                    break;

                case "JPY":
                case "SEK":
                case "KRW":
                case "INR":
                case "TWD":
                    ConfigureZeroDecimalFormat(format, ",", 0); // 添加默认分隔符
                    break;

                default:
                    ConfigureStandardFormat(format, 2, ".", ",");
                    break;
            }

            format.NumberGroupSizes = new[] { 3 };
            //return price.ToString("N", format);
            return price.ToString("N2");
        }



        public string CurrencyFormat(decimal price, string currency = "", bool isFormat = true)
        {
            var format = (NumberFormatInfo)CultureInfo.InvariantCulture.NumberFormat.Clone();
            var currencyCode = currency.ToUpperInvariant();
            price = CurrencyFloatPrice(price, currency);
            switch (currencyCode)
            {
                case "USD": /* 其他标准货币 */
                    ConfigureStandardFormat(format, 2, ".", ",");
                    break;

                case "RUB":
                    format.NumberDecimalDigits = 2;
                    format.NumberDecimalSeparator = ",";
                    format.NumberGroupSeparator = " ";
                    break;

                case "EUR": /* 欧洲货币 */
                    format.NumberDecimalDigits = 2;
                    format.NumberDecimalSeparator = ",";
                    format.NumberGroupSeparator = ".";
                    break;

                // 零小数货币的修复方案
                case "CLP":
                case "NOK":
                case "DKK":
                case "COP":
                case "VND":
                    ConfigureZeroDecimalFormat(format, ".", 0); // 添加默认分隔符
                    break;

                case "JPY":
                case "SEK":
                case "KRW":
                case "INR":
                case "TWD":
                    ConfigureZeroDecimalFormat(format, ",", 0); // 添加默认分隔符
                    break;

                default:
                    ConfigureStandardFormat(format, 2, ".", ",");
                    break;
            }

            format.NumberGroupSizes = new[] { 3 };
            if (isFormat)
            {
                return price.ToString("N", format);
            }
            return price.ToString("N2");
        }

        private static void ConfigureStandardFormat(
            NumberFormatInfo format,
            int decimalPlaces,
            string decimalSeparator,
            string groupSeparator)
        {
            format.NumberDecimalDigits = decimalPlaces;
            format.NumberDecimalSeparator = decimalSeparator;
            format.NumberGroupSeparator = groupSeparator;
        }

        // 修复后的零小数配置方法
        private static void ConfigureZeroDecimalFormat(
            NumberFormatInfo format,
            string defaultSeparator,
            int decimalPlaces = 0)
        {
            format.NumberDecimalDigits = decimalPlaces;
            format.NumberDecimalSeparator = defaultSeparator; // 必须设置非空值
            format.NumberGroupSeparator = defaultSeparator;    // 复用分隔符逻辑
        }


        /**
         * 不同货币的显示方式
         *
         * @param: $price[float]		产品价格
         * @param: $method[int]			【已弃用】
         * @param: $currency[string]	货币代号
         * @return string
         */
        public decimal CurrencyFormat(decimal price, int method = 0, string currency = "")
        {
            price = CurrencyFloatPrice(price, currency);

            switch (currency)
            {
                case "USD":
                case "GBP":
                case "CAD":
                case "AUD":
                case "CHF":
                case "HKD":
                case "ILS":
                case "MXN":
                case "CNY":
                case "SAR":
                case "SGD":
                case "NZD":
                case "AED":
                    return decimal.Parse(price.ToString("N2", CultureInfo.InvariantCulture));

                case "RUB":
                    return decimal.Parse(price.ToString("N2", new CultureInfo("ru-RU")));

                case "EUR":
                case "BRL":
                case "ARS":
                    var europeanFormat = new CultureInfo("fr-FR");
                    europeanFormat.NumberFormat.NumberGroupSeparator = ".";
                    europeanFormat.NumberFormat.NumberDecimalSeparator = ",";
                    var sdfsf = decimal.Parse(price.ToString("N2", europeanFormat), europeanFormat);
                    return decimal.Parse(price.ToString("N2", europeanFormat), europeanFormat);

                case "CLP":
                case "NOK":
                case "DKK":
                case "COP":
                case "VND":
                    var noDecimalFormat = new CultureInfo("en-US");
                    noDecimalFormat.NumberFormat.NumberGroupSeparator = ".";
                    noDecimalFormat.NumberFormat.NumberDecimalSeparator = "";
                    return decimal.Parse(price.ToString("N0", noDecimalFormat));

                case "JPY":
                case "SEK":
                case "KRW":
                case "INR":
                case "TWD":
                    var asianFormat = new CultureInfo("en-US");
                    asianFormat.NumberFormat.NumberGroupSeparator = ",";
                    asianFormat.NumberFormat.NumberDecimalSeparator = "";
                    return decimal.Parse(price.ToString("N0", asianFormat), asianFormat);

                default:
                    return decimal.Parse(price.ToString("N2", CultureInfo.InvariantCulture));
            }
        }









    }
}
