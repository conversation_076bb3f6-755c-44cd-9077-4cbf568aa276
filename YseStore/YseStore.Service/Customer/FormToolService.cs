using Fluid;
using Fluid.Values;
using Fluid.ViewEngine;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Text.Json;
using System.Threading.Tasks;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.Common.Const;
using YseStore.IService.Customer;
using YseStore.Model;
using YseStore.Model.VM.Common;
using Microsoft.Extensions.Localization;

namespace YseStore.Service.Customer
{
    /// <summary>
    /// 表单工具服务实现类
    /// </summary>
    public class FormToolService : BaseServices<app_form_tool>, IFormToolService
    {
        private readonly ILogger<FormToolService> _logger;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly IFormToolFieldService _formToolFieldService;
        public FormToolService(ILogger<FormToolService> logger, IConfiguration configuration, ISqlSugarClient db, ICaching caching, IFormToolFieldService formToolFieldService)
        {
            _logger = logger;
            _configuration = configuration;
            this.db = db;
            _caching = caching;
            _formToolFieldService = formToolFieldService;
        }

        /// <summary>
        /// 获取表单工具列表
        /// </summary>
        /// <param name="criteria"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PageModel<app_form_tool>> GetFormTool(Dictionary<string, string> criteria, int pageIndex, int pageSize)
        {
            try
            {

                var childq = db.Queryable<app_form_tool>();
                RefAsync<int> pageCount = 0;

                if (criteria != null && criteria.Count > 0)
                {
                    //搜索参数
                    if (criteria.ContainsKey("keywords"))
                    {
                        var kw = criteria["keywords"].ToString().Trim();
                        if (int.TryParse(kw, out int id))
                        {
                            childq = childq.Where(it => it.FId == id);
                        }
                        else
                        {
                            childq = childq.Where(it => it.Name.Contains(kw));
                        }

                    }
                    //ID
                    if (criteria.ContainsKey("ID"))
                    {
                        var Id = Convert.ToInt32(criteria["ID"]);
                        childq = childq.Where(it => it.FId == Id);
                    }

                }

                //查看到数据
                var list = await childq.Select(it => new app_form_tool
                {
                    //未读数
                    noReadCount = SqlFunc.Subqueryable<app_form_tool_data>()
                        .Where(field => field.FId == it.FId && field.IsRead == false)
                        .Count(),

                    //所有数据
                    dataCount = SqlFunc.Subqueryable<app_form_tool_data>()
                        .Where(field => field.FId == it.FId)
                        .Count(),
                    //字段
                    fields = SqlFunc.Subqueryable<app_form_tool_field>()
                        .Where(field => field.FId == it.FId)
                        .ToList(),
                }, isAutoFill: true)
                    .OrderBy(it => it.FId, OrderByType.Desc)
                    .ToPageListAsync(pageIndex, pageSize, pageCount);

                // 构建并返回分页模型
                return new PageModel<app_form_tool>
                {
                    page = pageIndex,
                    PageSize = pageSize,
                    dataCount = pageCount,
                    data = list
                };


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                // 出错时返回空结果
                return new PageModel<app_form_tool>();
            }
        }



        /// <summary>
        /// 获取所有的表单缓存
        /// </summary>
        /// <returns></returns>
        public async Task<List<app_form_tool>> GetAllFormCache()
        {
            var ret = await _caching.GetFromCacheAsync(GlobalConstVars.DBForm, async () =>
            {
                var list = await db.Queryable<app_form_tool>().OrderBy(it => it.FId, OrderByType.Asc)
                .ToListAsync();

                return list;

            });
            return ret;
        }



        /// <summary>
        /// 根据id获取详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<app_form_tool> GetFormByIdCache(int id)
        {
            var formList = await GetAllFormCache();
            if (formList != null)
            {
                var r = formList.Where(it => it.FId == id).FirstOrDefault();
                return r;
            }

            return new app_form_tool();
        }


        /// <summary>
        /// 获取表单字段详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<app_form_tool> GetFormFieldDetailsCache(int id)
        {

            //获取缓存中的表单详情
            var formData = _caching.HashGet<app_form_tool>(GlobalConstVars.Data_CustomFormDetailsKey, GlobalConstVars.Data_CustomFormDetailsHashKey.FormatWith(id));

            if (formData != null)
            {
                return formData;
            }

            //获取数据库表单
            var r = await GetFormByIdCache(id);
            if (r == null)
            {
                return new app_form_tool();
            }


            if (!r.MethodExpansion.IsNullOrEmpty())
            {
                r.FormBtn = r.MethodExpansion.JsonToObj<FormBtn>();
            }
            //获取数据库表单字段
            var fieldList = await _formToolFieldService.GetAllFormFieldCache();
            fieldList.ForEach(it =>
            {
                if (!it.FieldSetting.IsNullOrEmpty())
                {
                    it.fieldSettingModel = it.FieldSetting.JsonToObj<FieldSetting>();
                }
            });

            r.fields = fieldList.Where(it => it.FId == id)
                .OrderBy(it => it.MyOrder).ToList();

            //生成HTML内容
            var html = await GetFormHtml(r);
            r.FormHtml = html;

            //设置缓存
            _caching.HashSet<app_form_tool>(GlobalConstVars.Data_CustomFormDetailsKey, GlobalConstVars.Data_CustomFormDetailsHashKey.FormatWith(id), r);

            return r;
        }


        /// <summary>
        /// 获取表单HTML内容  
        /// </summary>
        /// <param name="form"></param>
        /// <returns></returns>
        public async Task<string> GetFormHtml(app_form_tool form)
        {
            var templateHtml = System.IO.File.ReadAllText(System.IO.Path.Combine(System.Environment.CurrentDirectory, "wwwroot/template/form/siteform.html"));
            //替换内容
            FluidViewParser _parser = new FluidViewParser();
            _parser.TryParse(templateHtml, out var template, out var error);

            var options = new TemplateOptions();
            options.MemberAccessStrategy.Register(typeof(app_form_tool));
            options.MemberAccessStrategy.Register(typeof(app_form_tool_field));
            options.MemberAccessStrategy.Register(typeof(FormBtn));
            options.MemberAccessStrategy.Register(typeof(FieldSetting));
            options.MemberAccessStrategy.Register(typeof(Dictionary<string, string>));
            //options.CultureInfo = new System.Globalization.CultureInfo("en");
            options.Filters.AddFilter("jsonparse", JsonHelper.JsonParseFilter.JsonParse);
            options.Filters.AddFilter("translate", (input, arguments, context) =>
            {
                // 直接使用IStringLocalizer本地化字符串
                if (context.AmbientValues.TryGetValue("StringLocalizer", out var localizerObj) &&
                    localizerObj is IStringLocalizer localizer)
                {
                    var text = input.ToStringValue();
                    return new ValueTask<FluidValue>(new StringValue(localizer[text]));
                }

                return new ValueTask<FluidValue>(input);
            });
            var context = new TemplateContext(options);
            context.SetValue("form", form);

            var result = await template.RenderAsync(context);

            return result;
        }
    }


}
