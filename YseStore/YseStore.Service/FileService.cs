using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Service
{
  
    public class FileService : IFileService
    {
        private readonly ILogger<manage_operation_log> _logger;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarClient db;
        public FileService(ILogger<manage_operation_log> logger, IConfiguration configuration, ISqlSugarClient db)
        {
            _logger = logger;
            _configuration = configuration;
            this.db = db;
        }


        public async Task<IList<photo>> GetPhotoByIdAry(IList<uint> idary)
        {
            var photoList = await db.Queryable<photo>().Where(u => idary.Contains( u.PId)).ToListAsync();
            return photoList;
        }
        public async Task<IList<Entitys.File>> GetFileByIdAry(IList<uint> idary)
        {
            var photoList = await db.Queryable<Entitys.File>().Where(u => idary.Contains(u.FId)).ToListAsync();
            return photoList;
        }
    }
}
