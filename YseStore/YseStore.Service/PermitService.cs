using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Service
{
    public class PermitService : IPermitService
    {
        private readonly ISqlSugarClient db;

        public PermitService(ISqlSugarClient db)
        {
            this.db = db;
        }

        public async Task<JObject> GetUserPermit()
        {
            var permit = await db.Queryable<config>().FirstAsync(u => u.GroupId == "app" && u.Variable == "Permit");
            var per = JObject.Parse(permit.Value);
            return per;
        }



    }
}
