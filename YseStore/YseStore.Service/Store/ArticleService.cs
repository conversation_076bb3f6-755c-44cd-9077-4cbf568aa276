using YseStore.IService.Store;
using YseStore.Model;
using YseStore.Model.RequestModels.Store;
using YseStore.Model.Response.Store;
using YseStore.Repo;

namespace YseStore.Service.Store
{
    /// <summary>
    /// 文章服务实现类
    /// </summary>
    public class ArticleService : BaseServices<article>, IArticleService
    {
        private readonly IArticleSeoService _articleSeoService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="baseDal">基础仓储</param>
        /// <param name="articleSeoService">文章SEO服务</param>
        public ArticleService(IBaseRepository<article> baseDal, IArticleSeoService articleSeoService) : base(baseDal)
        {
            _articleSeoService = articleSeoService;
        }

        /// <summary>
        /// 获取文章列表
        /// </summary>
        /// <param name="request">文章查询请求参数</param>
        /// <returns>分页文章列表</returns>
        public async Task<PageModel<article>> GetArticleListAsync(ArticleQueryRequest request)
        {
            // 确保页码从1开始
            request.PageIndex = request.PageIndex < 1 ? 1 : request.PageIndex;

            // 构建查询条件
            var query = Db.Queryable<article>();

            // 如果有关键词，添加搜索条件
            if (!string.IsNullOrWhiteSpace(request.Keyword))
            {
                query = query.Where(a => a.Title_en.Contains(request.Keyword) ||
                                         a.SeoTitle_en.Contains(request.Keyword) ||
                                         a.SeoKeyword_en.Contains(request.Keyword) ||
                                         a.SeoDescription_en.Contains(request.Keyword));
            }


            // 如果有标签ID，添加筛选条件
            if (!string.IsNullOrWhiteSpace(request.TagsId))
            {
                query = query.Where(a => a.TagsId.Contains(request.TagsId));
            }

            // 获取总记录数
            var totalCount = await query.CountAsync();

            // 获取分页数据
            var articles = await query
                .OrderByDescending(a => a.AId)
                .Skip((request.PageIndex - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            // 创建并返回PageModel对象
            return new PageModel<article>
            {
                page = request.PageIndex,
                PageSize = request.PageSize,
                dataCount = totalCount,
                data = articles
            };
        }

        /// <summary>
        /// 根据ID获取文章详情
        /// </summary>
        /// <param name="aId">文章ID</param>
        /// <returns>文章详情</returns>
        public async Task<article> GetArticleByIdAsync(int aId)
        {
            return await Db.Queryable<article>()
                .Where(a => a.AId == aId)
                .FirstAsync();
        }

        /// <summary>
        /// 获取文章内容
        /// </summary>
        /// <param name="aId">文章ID</param>
        /// <returns>文章内容</returns>
        public async Task<article_content> GetArticleContentAsync(int aId)
        {
            return await Db.Queryable<article_content>()
                .Where(c => c.AId == aId)
                .FirstAsync();
        }

        /// <summary>
        /// 获取所有文章标签
        /// </summary>
        /// <returns>标签列表</returns>
        public async Task<List<article_tags>> GetArticleTagsAsync()
        {
            return await Db.Queryable<article_tags>()
                .OrderBy(t => t.Id)
                .ToListAsync();
        }

        /// <summary>
        /// 根据文章ID获取关联的标签列表
        /// </summary>
        /// <param name="aId">文章ID</param>
        /// <returns>标签列表</returns>
        public async Task<List<article_tags>> GetArticleTagsByArticleIdAsync(int aId)
        {
            // 先获取文章信息，提取TagsId
            var article = await GetArticleByIdAsync(aId);
            if (article == null || string.IsNullOrEmpty(article.TagsId))
            {
                return new List<article_tags>();
            }

            // 解析TagsId字符串，格式可能是逗号分隔的ID列表
            var tagIds = article.TagsId.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(id => Convert.ToInt32(id))
                .ToList();

            if (tagIds.Count == 0)
            {
                return new List<article_tags>();
            }

            // 查询标签信息
            return await Db.Queryable<article_tags>()
                .Where(t => tagIds.Contains(t.Id))
                .ToListAsync();
        }

        /// <summary>
        /// 根据URL获取文章完整详情（使用响应模型）
        /// </summary>
        /// <param name="pageUrl">文章URL</param>
        /// <returns>文章详情响应模型</returns>
        public async Task<ArticleDetailResponse> GetArticleDetailByUrlAsync(string pageUrl)
        {
            var result = new ArticleDetailResponse();

            // 根据URL获取文章基本信息
            var article = await Db.Queryable<article>()
                .Where(a => a.PageUrl == pageUrl)
                .FirstAsync();

            if (article == null)
            {
                // 如果找不到文章，返回空结果
                return result;
            }

            result.Article = article;

            // 获取文章内容
            var content = await GetArticleContentAsync(article.AId);
            if (content != null)
            {
                result.Content = content;
            }

            // 获取文章标签
            var tags = await GetArticleTagsByArticleIdAsync(article.AId);
            if (tags != null && tags.Count > 0)
            {
                result.Tags = tags;
            }

            return result;
        }

        /// <summary>
        /// 获取文章完整详情（使用响应模型）
        /// </summary>
        /// <param name="aId">文章ID</param>
        /// <returns>文章详情响应模型</returns>
        public async Task<ArticleDetailResponse> GetArticleDetailResponseAsync(int aId)
        {
            var result = new ArticleDetailResponse();

            // 获取文章基本信息
            var article = await GetArticleByIdAsync(aId);
            if (article != null)
            {
                result.Article = article;
            }

            // 获取文章内容
            var content = await GetArticleContentAsync(aId);
            if (content != null)
            {
                result.Content = content;
            }

            // 获取文章标签
            var tags = await GetArticleTagsByArticleIdAsync(aId);
            if (tags != null && tags.Count > 0)
            {
                result.Tags = tags;
            }

            return result;
        }

        /// <summary>
        /// 更新或新增文章信息（包括内容）
        /// </summary>
        /// <param name="articleDetail">文章详情</param>
        /// <returns>是否操作成功</returns>
        public async Task<bool> UpdateArticleAsync(ArticleDetailResponse articleDetail)
        {
            try
            {
                if (articleDetail?.Article == null)
                {
                    return false;
                }

                // 处理PageUrl唯一性
                if (!string.IsNullOrEmpty(articleDetail.Article.PageUrl))
                {
                    // 生成唯一的PageUrl
                    articleDetail.Article.PageUrl = await _articleSeoService.GenerateUniqueArticlePageUrlAsync(
                        articleDetail.Article.PageUrl, articleDetail.Article.AId);
                }
                else if (!string.IsNullOrEmpty(articleDetail.Article.Title_en))
                {
                    // 如果PageUrl为空，使用文章标题生成
                    articleDetail.Article.PageUrl = await _articleSeoService.GenerateUniqueArticlePageUrlAsync(
                        articleDetail.Article.Title_en, articleDetail.Article.AId);
                }

                // 开启事务
                var result = await Db.Ado.UseTranAsync(async () =>
                {
                    articleDetail.Article.GoogleStructuredData = "";
                    articleDetail.Article.FacebookTwitterMetaCode = "";
                    articleDetail.Article.ParentID = 0; //暂时为0，后续可能会用到
                    // 判断是新增还是更新文章
                    if (articleDetail.Article.AId > 0)
                    {
                        // 更新文章基本信息
                        await Db.Updateable(articleDetail.Article).ExecuteCommandAsync();
                    }
                    else
                    {
                        // 新增文章基本信息
                        var insertResult = await Db.Insertable(articleDetail.Article).ExecuteReturnEntityAsync();
                        articleDetail.Article = insertResult; // 更新对象，获取新生成的ID
                    }

                    // 处理文章内容
                    if (articleDetail.Content != null)
                    {
                        // 确保内容的AId与文章一致
                        articleDetail.Content.AId = articleDetail.Article.AId;

                        // 检查文章内容是否存在
                        var existingContent = await Db.Queryable<article_content>()
                            .Where(c => c.AId == articleDetail.Article.AId)
                            .FirstAsync();
                        if (existingContent != null)
                        {
                            // 设置正确的主键值
                            articleDetail.Content.CId = existingContent.CId;

                            // 更新现有内容
                            await Db.Updateable(articleDetail.Content)
                                .IgnoreColumns(ignoreAllNullColumns: true) // 忽略空值字段
                                .ExecuteCommandAsync();
                        }
                        else
                        {
                            // 插入新内容
                            await Db.Insertable(articleDetail.Content).ExecuteCommandAsync();
                        }
                    }
                });

                // 如果操作成功，更新文章PageUrl缓存
                if (result.IsSuccess && !string.IsNullOrEmpty(articleDetail.Article.PageUrl) &&
                    articleDetail.Article.AId > 0)
                {
                    _articleSeoService.UpdateArticlePageUrlCache(articleDetail.Article.PageUrl,
                        articleDetail.Article.AId);
                }

                return result.IsSuccess;
            }
            catch (Exception ex)
            {
                // 记录异常信息
                Console.WriteLine($"更新或新增文章时发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 根据ID数组删除文章
        /// </summary>
        /// <param name="ids">文章ID数组</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> DeleteByIds(object[] ids)
        {
            return await base.DeleteByIds(ids);
        }
    }
}