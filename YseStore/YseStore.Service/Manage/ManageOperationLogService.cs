using Microsoft.Extensions.Logging;
using YseStore.IService.manage;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using SqlSugar;
using System.Linq.Dynamic.Core;

namespace YseStore.Service.manage;

public class ManageOperationLogService : BaseServices<manage_operation_log>, IManageOperationLogService
{
    private readonly ILogger<manage_operation_log> _logger;

    public ManageOperationLogService(ILogger<manage_operation_log> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 获取所有操作日志
    /// </summary>
    public async Task<List<manage_operation_log>> GetAllOperationLogsAsync()
    {
        var list = await Query(x => true);
        return list;
    }

    /// <summary>
    /// 根据条件查询操作日志
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <param name="orderByExpression">排序字段</param>
    /// <param name="orderByType">排序方式</param>
    /// <param name="skip">跳过数量</param>
    /// <param name="take">获取数量</param>
    /// <returns>操作日志列表</returns>
    public async Task<PagedList<VM_OperationLog>> QueryAsync(
        int pageNum = 1,
        int pageSize = 50)
    {

        try
        {
            RefAsync<int> totalNum = 0;
            // 创建查询
            var query = Db.Queryable<manage_operation_log>();//.Where(predicate);

            //// 添加排序
            //if (orderByExpression != null)
            //{
            //    query = orderByType == OrderByType.Asc
            //        ? query.OrderBy(orderByExpression)
            //        : query.OrderByDescending(orderByExpression);
            //}
            query = query.OrderByDescending(u => u.AccTime);
            // 分页查询
            var result = await query.Select(u => new VM_OperationLog()
            {
                ModuleName = u.Module,
                Log = u.Log,
                LogDesc = u.LogDesc,
                IP = u.Ip,
                UserName = u.UserName,
                AccDate = u.AccTime
            }).ToPageListAsync(pageNum, pageSize, totalNum);
            var pl = new PagedList<VM_OperationLog>(result, pageNum-1, pageSize, totalNum);
            return pl;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查询操作日志失败");

        }
        return null;
    }

    /// <summary>
    /// 根据条件获取操作日志总数
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>操作日志总数</returns>
    public async Task<int> CountAsync(Expression<Func<manage_operation_log, bool>> predicate)
    {
        try
        {
            // 查询符合条件的记录数量
            var count = await Db.Queryable<manage_operation_log>().CountAsync(predicate);
            return (int)count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取操作日志总数失败");
            return 0;
        }
    }
}