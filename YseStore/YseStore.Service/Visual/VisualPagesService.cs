using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService.Visual;

namespace YseStore.Service.Visual
{
    /// <summary>
    /// 店铺装修页面
    /// </summary>
    public class VisualPagesService : BaseServices<visual_pages>, IVisualPagesService
    {
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly ILogger<visual_pages> _logger;
        public VisualPagesService(ISqlSugarClient db, ICaching caching, ILogger<visual_pages> logger)
        {
            this.db = db;
            _caching = caching;
            _logger = logger;
        }
        #region
        public async Task<visual_pages> QueryVisualPagesAsync(int pagesId,int draftsId,string pages)
        {
            try
            {

                return await db.Queryable<visual_pages>().Where(it => it.PagesId == pagesId && it.DraftsId==draftsId && it.Pages==pages).FirstAsync();

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "QueryVisualPagesAsync失败");
                return new visual_pages();
            }

        }

        /// <summary>
        /// 根据模板ID获取页面配置
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns></returns>
        public async Task<visual_pages> GetPagesByTemplateIdAsync(int templateId)
        {
            try
            {
                return await db.Queryable<visual_pages>()
                    .Where(it => it.TemplateId == templateId)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据模板ID获取页面配置失败，TemplateId: {TemplateId}", templateId);
                return null;
            }
        }
        #endregion
    }
}
