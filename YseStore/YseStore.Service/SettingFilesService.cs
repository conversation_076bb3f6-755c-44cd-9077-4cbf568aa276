using Entitys;
using Flurl.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.IService;
using YseStore.Model;
using YseStore.Model.VM.Aliyun;

namespace YseStore.Service
{
    public class SettingFilesService : BaseServices<manage_operation_log>, ISettingFilesService
    {
        private readonly ILogger<manage_operation_log> _logger;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarClient db;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ISettingBasisService _settingBasisService;
        public SettingFilesService(ILogger<manage_operation_log> logger, IConfiguration configuration, ISqlSugarClient db
            , IHttpContextAccessor httpContextAccessor
             , ISettingBasisService settingBasisService)
        {
            _logger = logger;
            _configuration = configuration;
            this.db = db;
            _httpContextAccessor = httpContextAccessor;
            _settingBasisService = settingBasisService;
        }

        public async Task<string> GetWebSiteUrl()
        {
            var filesStorageOptions = await _settingBasisService.GetFilesStorageOptions();
            var WebSiteUrl = string.Empty;
            switch (filesStorageOptions.StorageType)
            {
                case Consts.FilesStorageType_AliYunOSS:
                    WebSiteUrl = filesStorageOptions.BucketBindUrl;
                    break;
                default:
                    WebSiteUrl = $"{_httpContextAccessor.HttpContext!.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host.Value}";
                    break;
            }
            return WebSiteUrl;
        }



        /// <summary>
        /// 根据条件查询文件管理
        /// </summary>
        /// <param name="pageNum"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PagedList<FilesResponse>> QueryAsync(
            string keyword = "",
            string TagsId = "",
            int pageNum = 1,
            int pageSize = 50)
        {

            try
            {
                RefAsync<int> totalNum = 0;
                var query = db.Queryable<Entitys.File>();

                // 搜索条件：标题包含关键词
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(p => p.Name.Contains(keyword));
                }
                // 标签过滤
                if (!string.IsNullOrEmpty(TagsId))
                {
                    // 将传入的 tagId 按逗号分隔为单个标签 ID 列表
                    var tagIdList = TagsId.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(t => t.Trim())
                        .Where(t => !string.IsNullOrEmpty(t))
                        .ToList();
                    query = query.Where(p => p.TagsId != null && tagIdList.All(tag => p.TagsId.Contains(tag)));
                }

                var fileList = query.ToList();
                var paged = fileList.Skip((pageNum - 1) * pageSize).Take(pageSize).ToList();
                var grouped = paged
                  .Select(o => (FilesResponse)o)
                  .OrderByDescending(o => o.FId)
                  .ToList();
                var TagsIds = grouped.Where(x => x.TagsId != "").Select(p => p.TagsId).ToList();
                var allTagIds = grouped.Where(x => x.TagsId != "")
                    .SelectMany(p => p.TagsId.Split(','))  // 拆分逗号分隔的字符串
                    .Select(int.Parse)                     // 转为整数（假设 Id 是 int 类型）
                    .Distinct()                            // 去重
                    .ToList();
                var TagsNames = db.Queryable<file_tags>()
                    .Where(pt => allTagIds.Contains(pt.TagId)) // 精确匹配数字 ID
                    .ToList();
                foreach (var p in grouped)
                {
                    p.fileSizeMB = p.Size.ToFileSize();
                    p.Suffix = GetSuffix(p.Path);
                    if (!string.IsNullOrEmpty(p.TagsId))
                    {
                        var tagIds = p.TagsId.Split(',')
                                        .Select(int.Parse) // 转为整数列表
                                        .ToList();

                        // 用精确匹配代替模糊匹配
                        var Names = TagsNames.Where(t => tagIds.Contains(t.TagId)) // 假设 t.Id 是 int 类型
                                            .Select(t => t.Name)
                                            .ToList();

                        p.TagsName = string.Join(",", Names);
                    }


                }



                //var res = query.Select((p, pt) => new
                //{
                //    Photo = p,
                //    TagName = pt.Name
                //})
                // .ToList();

                //var grouped = res.GroupBy(item => item.Photo.FId)
                //    .Select(g => new FilesResponse()
                //    {
                //        FId = g.Key,
                //        Name = g.First().Photo.Name,
                //        Path = g.First().Photo.Path,
                //        fileSizeMB = GetFileInfo(g.First().Photo.Path).fileSizeMB.ToString(),
                //        //Width = g.First().Photo.Width,
                //        //Height = g.First().Photo.Height,
                //        Suffix = GetSuffix(g.First().Photo.Path),
                //        //IsSystem = g.First().Photo.IsSystem,
                //        TagsName = string.Join(",", g.Select(x => x.TagName).Distinct()),
                //        WebSiteUrl = g.First().Photo.Host
                //    })
                //    .OrderByDescending(p => p.FId)
                //    .ToList();

                //// 分页处理
                //var paged = grouped.Skip((pageNum - 1) * pageSize).Take(pageSize).ToList();
                var pl = new PagedList<FilesResponse>(grouped, pageNum - 1, pageSize, grouped.Count);
                return pl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询文件管理失败");

            }
            return null;
        }
        /// <summary>
        /// 获取图片标签
        /// </summary>
        /// <returns></returns>
        public async Task<List<file_tags>> GetPhoto_Tags()
        {
            List<file_tags> photo_TagsList = new List<file_tags>();
            photo_TagsList = await db.Queryable<file_tags>()
           .GroupBy(it => new { it.Name })
           .Select(it => new file_tags
           {
               TagId = SqlFunc.AggregateMax(it.TagId),
               Name = it.Name
           })
           .ToListAsync();
            return photo_TagsList;
        }

        public async Task<List<file_tags>> GetPhoto_Tags_Tags(string idString)
        {
            var idList = idString.Split('-').Select(int.Parse).ToList();
            var photoQuery = db.Queryable<Entitys.File>()
                .Where(it => idList.Contains(Convert.ToInt32(it.FId)))
                .ToList();

            var tagIds = new List<int>();
            foreach (var photo in photoQuery)
            {
                if (!string.IsNullOrEmpty(photo.TagsId))
                {
                    var ids = photo.TagsId.Split(',')
                                         .Select(int.Parse)
                                         .ToList();
                    tagIds.AddRange(ids);
                }
            }

            tagIds = tagIds.Distinct().ToList();
            var tagQuery = db.Queryable<file_tags>()
                .Where(it => tagIds.Contains(it.TagId))
                .ToList();
            return tagQuery;
        }
        /// <summary>
        /// 获取图片标签数组
        /// </summary>
        /// <returns></returns>
        public async Task<List<TagDtoResponse>> GetFormattedPhotoTags()
        {
            var query = await db.Queryable<file_tags>()
                .GroupBy(it => it.Name)
                .Select(it => new TagDtoResponse
                {
                    Name = it.Name,
                    Value = SqlFunc.AggregateMax(it.TagId),
                    Type = "file_tags"
                }).ToListAsync();

            return query;
        }

        /// <summary>
        /// 新增文件 
        /// </summary>
        /// <param name="FilePath"></param>
        /// <param name="tagsName"></param>
        /// <returns></returns>
        public async Task<bool> AddFileList(List<string> FilePath, List<string> fizeSize, List<string> oriName, List<string> tagsName)
        {
            //var result = await db.Ado.UseTranAsync(async () =>
            //{
            int stopIndex = tagsName.FindIndex(tagName => tagName == "截至-·11");
            if (stopIndex != -1)
            {
                tagsName = tagsName.Take(stopIndex).ToList();
            }

            List<int> tagIds = new List<int>();
            if (tagsName != null && tagsName.Count > 0)
            {
                foreach (var tagName in tagsName.Distinct())
                {
                    var existingTag = await db.Queryable<file_tags>()
                        .Where(t => t.Name == tagName)
                        .Select(t => new { t.TagId })
                        .FirstAsync();

                    if (existingTag != null)
                    {
                        // 存在则直接使用现有ID
                        tagIds.Add(existingTag.TagId);
                    }
                    else
                    {
                        // 不存在则插入新标签
                        var newTag = new file_tags { Name = tagName };
                        var newId = await db.Insertable(newTag).ExecuteReturnIdentityAsync();
                        tagIds.Add((int)newId);
                    }
                }
            }
            var WebSiteUrl = await GetWebSiteUrl();
            var len = FilePath.Count;
            for (var i = 0; i < len; i++)
            {
                var path = FilePath[i];
                var size = fizeSize[i].ObjToLong();
                var oriname = oriName[i];
                
                var file = new Entitys.File
                {
                    Name = Path.GetFileNameWithoutExtension(oriname),
                    Path = path,
                    Type = GetSuffix(path),
                    TagsId = string.Join(",", tagIds),
                    Host = WebSiteUrl,
                    Size = size,
                    OriName = Path.GetFileNameWithoutExtension(oriname)
                };

                //var filesStorageOptions = await _settingBasisService.GetFilesStorageOptions();
                //switch (filesStorageOptions.StorageType)
                //{
                //    case Consts.FilesStorageType_AliYunOSS:
                //        var fileInfo = await GetAliyunImageInfo(path);
                //        if (fileInfo.Item1)
                //        {
                //            file.Size=fileInfo.Item2.FileSize.value.ObjToLong();  

                //        }
                //        break;
                //}

                var fileId = await db.Insertable(file).ExecuteReturnIdentityAsync();
            }

            return true;
            //});
            //return Convert.ToBoolean(result);

        }


        /// <summary>
        /// 通过pId更新图片表中的标签id
        /// </summary>
        /// <param name="pIdString"></param>
        /// <param name="tagsNameList"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public async Task<bool> Updatephoto_tagsId(string pIdString, List<string> tagsNameList)
        {
            bool b = true;
            List<int> pIds;
            try
            {
                pIds = pIdString.Split('-').Select(int.Parse).ToList();
            }
            catch (FormatException)
            {
                throw new ArgumentException("PId格式无效，必须为数字");
            }
            int stopIndex = tagsNameList.FindIndex(tagName => tagName == "截至-·11");
            if (stopIndex != -1)
            {
                tagsNameList = tagsNameList.Take(stopIndex).ToList();
            }
            List<int> tagIds = new List<int>();

            if (tagsNameList != null && tagsNameList.Count > 0)
            {
                foreach (var tagName in tagsNameList.Distinct())
                {
                    var existingTag = await db.Queryable<file_tags>()
                        .Where(t => t.Name == tagName)
                        .Select(t => new { t.TagId })
                        .FirstAsync();
                    if (existingTag != null)
                    {
                        // 存在则直接使用现有ID
                        tagIds.Add(existingTag.TagId);
                    }
                    else
                    {
                        // 不存在则插入新标签
                        var newTag = new file_tags { Name = tagName };
                        var newId = await db.Insertable(newTag).ExecuteReturnIdentityAsync();
                        tagIds.Add((int)newId);
                    }
                    //tagIds.Add(existingTag.TagId);
                }
            }


            string tagsNameString = string.Join(",", tagIds);

            //string tagsNameString = string.Join(",", tagsNameList);
            var entities = db.Queryable<Entitys.File>()
                .Where(it => pIds.Contains(Convert.ToInt32(it.FId)))
                .ToList();

            foreach (var entity in entities)
            {
                entity.TagsId = tagsNameString;
                bool bt = await db.Updateable(entity).ExecuteCommandHasChangeAsync();
                if (!bt)
                {
                    b = false;
                }
            }
            return b;
        }
        /// <summary>
        /// 删除图片
        /// </summary>
        /// <param name="pIdString"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public async Task<bool> Delphoto(string pIdString)
        {
            List<int> pIds;
            try
            {
                pIds = pIdString.Split('-').Select(int.Parse).ToList();
            }
            catch (FormatException)
            {
                throw new ArgumentException("PId格式无效，必须为数字");
            }

            var result = await db.Deleteable<Entitys.File>()
                .In(p => p.FId, pIds)
                .ExecuteCommandAsync();
            return result > 0;
        }



        //public static (string fileName, double fileSizeMB) GetFileInfo(string filePath)
        //{
        //    // 获取不带后缀的文件名
        //    string fileName = Path.GetFileNameWithoutExtension(filePath);
        //    double fileSizeMB = 0;
        //    if (System.IO.File.Exists(filePath))
        //    {
        //        // 获取文件大小（字节）
        //        FileInfo fileInfo = new FileInfo(filePath);
        //        long fileSizeBytes = fileInfo.Length;

        //        // 转换为 MB（保留两位小数）
        //        fileSizeMB = Math.Round(fileSizeBytes / (1024.0 * 1024.0), 2);
        //    }



        //    return (fileName, fileSizeMB);
        //}

        public static string GetSuffix(string filePath)
        {
            var str = "";
            // 从后往前查找第一个点号的位置
            int dotIndex = filePath.LastIndexOf('.');
            // 检查是否找到点号且不在开头位置
            if (dotIndex > 0 && dotIndex < filePath.Length - 1)
            {
                str = filePath.Substring(dotIndex);
            }
            return str;
        }



    }
}
