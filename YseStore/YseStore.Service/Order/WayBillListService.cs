using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.IService.Order;
using YseStore.Model.Enums;
using YseStore.Model;
using Entitys;
using System.Collections;
using Microsoft.AspNetCore.Components.Forms;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Threading;
using StackExchange.Redis;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using YseStore.Common;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using System.Text.RegularExpressions;
using System.Security.Policy;
using System.Security.Cryptography;
using static System.Runtime.InteropServices.JavaScript.JSType;
using YseStore.Model.Dto;
using YseStore.IService;
using FluentEmail.Core;

namespace YseStore.Service.Order
{
    public class WayBillListService : BaseServices<manage_operation_log>, IWayBillListService
    {
        private readonly ILogger<manage_operation_log> _logger;
        private readonly IConfiguration _configuration;
        private readonly IOrderListService _orderListService;
        private readonly IHelpsUserService _helpsUserService;
        private readonly IHelpsManageService _helpsManageService;
        public ICurrencyService _currencyService;

        private readonly ISqlSugarClient db;
        public WayBillListService(ILogger<manage_operation_log> logger, IConfiguration configuration, IOrderListService orderListService,
            IHelpsUserService helpsUserService, IHelpsManageService helpsManageService, ISqlSugarClient db, ICurrencyService currencyService)
        {
            _logger = logger;
            _configuration = configuration;
            _orderListService = orderListService;
            _helpsUserService = helpsUserService;
            _helpsManageService = helpsManageService;
            this.db = db;
            _currencyService = currencyService;
        }
        /// <summary>
        /// 查询运单列表
        /// </summary>
        /// <param name="keywords"></param>
        /// <param name="OrderStatus"></param>
        /// <param name="pageNum"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PagedList<WayBillListResponse>> QueryAsync(string keywords = "", string OrderStatus = "", int pageNum = 1, int pageSize = 50)
        {
            try
            {
                // 主查询构建
                var query = db.Queryable<orders>()
                    .LeftJoin<orders_products_list>((o, opl) => o.OrderId == opl.OrderId)
                   .WhereIF(!string.IsNullOrWhiteSpace(keywords), (o, opl) => o.OId.Contains(keywords) || opl.SKU.Contains(keywords) || opl.Name.Contains(keywords))
                  .WhereIF(!string.IsNullOrWhiteSpace(OrderStatus) && OrderStatus == "4", (o, opl) => o.OrderStatus == (int)OrderStatusEnum.已付款)
                  .WhereIF(!string.IsNullOrWhiteSpace(OrderStatus) && OrderStatus == "5", (o, opl) => o.OrderStatus == (int)OrderStatusEnum.已发货)
                   .WhereIF(string.IsNullOrWhiteSpace(OrderStatus), (o, opl) => o.OrderStatus == (int)OrderStatusEnum.已付款 || o.OrderStatus == (int)OrderStatusEnum.已发货)
                .Select(o => o);
                var orderList = query.Distinct().OrderByDescending(o => o.OrderId).ToList();

                // 执行分页查询
                var paged = orderList.Skip((pageNum - 1) * pageSize).Take(pageSize).ToList();
                //var res = new PagedList<user>(results, pageNum - 1, pageSize, results.Count);

                var grouped = paged
                    .Select(o => (WayBillListResponse)o)
                    .OrderByDescending(o => o.OrderId)
                    .ToList();
                //var userIds = grouped.Select(p => p.UserId).ToList();
                var orderIds = grouped.Select(p => p.OrderId).ToList();
                var OrdersPackageList = db.Queryable<orders_package>()
                     .Where(p => orderIds.Contains(Convert.ToInt32(p.OrderId)))
                   .ToList();
                var ordersProductsList = db.Queryable<orders_products_list>()
                     .Where(p => orderIds.Contains(Convert.ToInt32(p.OrderId)))
                   .ToList();

                var countryList = db.Queryable<country>()
                   .ToList();
                var currencyList = await _currencyService.GetAllCurrencyCache();
                //var currencyList = db.Queryable<currency>().ToList();
                foreach (var p in grouped)
                {
                    p.Orders_Package_List = OrdersPackageList.Where(x => x.OrderId == p.OrderId).ToList();
                    var productsList = ordersProductsList.Where(x => x.OrderId == p.OrderId).ToList();
                    p.Orders_Products_List = productsList;
                    //var tt = records.Where(x => x.UserId == p.UserId)
                    //                .ToList();
                    //var tags = userLabelCollection.OrderByDescending(x => x.UserId)
                    //               .Where(x => x.UserId == p.UserId)
                    //               .ToList();
                    //p.tagsList = tags;
                    //p.BuyCount = tt.Count;
                    p.ProductCount = productsList.Sum(x => x.Qty) ?? 0;
                    decimal OrderSum = Math.Round(((p.ProductPrice + p.ShippingPrice - p.PointsPrice) + ((p.ProductPrice + p.ShippingPrice - p.PointsPrice) * p.PayAdditionalFee)).Value, 2, MidpointRounding.AwayFromZero);
                    p.OrderSum = OrderSum;
                    p.OrderSymbol = currencyList.FirstOrDefault(c => c.Currency == p.Currency)?.Symbol;
                    p.countrys = countryList.Where(x => x.Country == p.ShippingCountry).FirstOrDefault();


                }

                var resss = new PagedList<WayBillListResponse>(grouped, pageNum - 1, pageSize, orderList.Count);
                return resss;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询运单列表失败");
                return new PagedList<WayBillListResponse>(new List<WayBillListResponse>(), pageNum, pageSize, 0);
            }
        }



        /// <summary>
        /// 获取物流商string
        /// </summary>
        /// <returns></returns>
        public async Task<List<CarrierResultResponse>> GetShippingCarrierData()
        {
            var carriers = await db.Queryable<shipping_carrier>().ToListAsync();

            //转换数据
            var result = new List<CarrierResultResponse>();
            foreach (var carrier in carriers)
            {
                // 解析JSON数据
                var trackData = JsonConvert.DeserializeObject<CarrierTrackData>(carrier.data);

                if (trackData?.TrackInfo != null)
                {
                    result.Add(new CarrierResultResponse
                    {
                        Name = carrier.name,
                        Value = "{'17track':" + trackData.TrackInfo.Key + "}"
                    });
                }
            }
            return result;
            // 序列化为JSON字符串

        }

        /// <summary>
        /// 运单里得一个包裹发货
        /// </summary>
        /// <param name="WId"></param>
        /// <param name="TrackingNumber"></param>
        /// <param name="Carrier"></param>
        /// <param name="CarrierKey"></param>
        /// <param name="CarrierKeyType"></param>
        /// <param name="_DoubleOption"></param>
        /// <param name="ShippingTime"></param>
        /// <param name="Remarks"></param>
        /// <param name="OrderId"></param>
        /// <param name="OrderStatus"></param>
        /// <returns></returns>
        public async Task<bool> WaybillOrdersModStatus(int WId, string TrackingNumber, string Carrier, string CarrierKey, string CarrierKeyType
                    , string _DoubleOption, string ShippingTime, string Remarks, int OrderId, string OrderStatus)
        {
            var ordersPackage = await db.Queryable<orders_package>()
                .Where(x => x.WId == WId).FirstAsync();
            //var orderss = await db.Queryable<orders>()
            //    .Where(x => x.OrderId == OrderId).FirstAsync();
            ordersPackage.TrackingNumber = TrackingNumber;
            var Carrierss = "{\"key\":" + CarrierKey + ",\"type\":\"" + CarrierKeyType + "\",\"name\":\"" + Carrier + "\"}";

            ordersPackage.Carrier = Carrierss;
            ordersPackage.ShippingTime = DateTimeHelper.ConvertToUnixTimestamp(Convert.ToDateTime(ShippingTime));
            ordersPackage.Remarks = Remarks;
            ordersPackage.Status = 1;
            //orderss.ShippingStatus = ShippingStatusEnum.已发货.GetDescription();

            //await db.Updateable(orderss).ExecuteCommandHasChangeAsync();
            var b = await db.Updateable(ordersPackage).ExecuteCommandHasChangeAsync();

            //TrackingNumber[54]  "111"
            //Carrier "17EXP"
            //CarrierKey  " {'17track':190524}"
            //CarrierKeyType  "carrier"
            //_DoubleOption[] "3"
            //ShippingTime[54]    "2025-04-17"
            //Remarks[54] "222222"
            //OrderId "47"
            //WId "54"
            //OrderStatus "5"
            //do_action   "/manage/orders/waybill/orders-mod-status"
            //{"key":{"17track":3013},"type":"carrier","name":"China EMS"}

            try
            {
                // 物流信息处理
                //var carrierKey = HelpsJson.Decode<Dictionary<string, object>>(carrierKeyJson) ?? new Dictionary<string, object>();
                //int carrierId = 0;
                //var carrierData = new
                //{
                //    key = carrierKey,
                //    type = carrierKeyType,
                //    name = carrier
                //};

                // 订单基础信息查询
                var order = db.Queryable<orders>()
                    .Where(o => o.OrderId == OrderId)
                    .First();

                if (order == null) return false;
                OrderStatus = order.OrderStatus.ToString();
                // 状态处理核心逻辑
                var updateData = new orders { UpdateTime =DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now) };

                // 计算订单金额
                var amount = await _orderListService.OrdersPrice(order, 1, 1);

                switch (OrderStatus)
                {
                    case "2": // 待付款
                        updateData.PaymentStatus = "pending";
                        break;
                    case "4": // 已付款
                        HandlePaymentStatus(order, updateData, WId, amount);
                        break;
                    case "5": // 已发货
                        HandleShippingStatus(order, updateData, WId, TrackingNumber, DateTimeHelper.ConvertToUnixTimestamp(Convert.ToDateTime(ShippingTime))
                            , Remarks, Carrierss, amount);
                        break;
                    case "6": // 已完成
                        HandleCompletedStatus(order, updateData);
                        break;
                    case "7": // 已作废
                        HandleCancelledStatus(order, updateData);
                        break;
                }
                if (string.IsNullOrEmpty(updateData.PaymentStatus))
                {
                    updateData.PaymentStatus=order.PaymentStatus;
                }
                if (updateData.OrderStatus==0)
                {
                    updateData.OrderStatus = order.OrderStatus;
                }
                if (string.IsNullOrEmpty(updateData.ShippingStatus))
                {
                    updateData.ShippingStatus = order.ShippingStatus;
                }
                if (string.IsNullOrEmpty(updateData.CancelReason))
                {
                    updateData.CancelReason = order.CancelReason;
                }
                // 通用更新逻辑
                if (updateData.OrderStatus.ToString() != OrderStatus)
                {
                    db.Updateable(updateData)
                        .UpdateColumns(u => new { u.OrderStatus, u.PaymentStatus, u.ShippingStatus, u.CancelReason, u.UpdateTime })
                        .Where(o => o.OrderId == OrderId)
                        .ExecuteCommand();
                }

                //// 消息队列处理
                //HandleQueueOperations(OrderStatus, order.OrderId);

                //// 邮件通知
                //SendOrderEmails(OrderStatus, order);

                // 操作日志
                _helpsManageService.OperationLog("更新订单状态", "更新订单状态");

                //HelpsManage::operationLog('更新订单状态', Orders::getValue(['OrderId' => $OrderId], 'OId'));
                

                return true;
            }
            catch (Exception ex)
            {
                // 错误处理
                return false;
            }

        }
        private void HandleCancelledStatus(orders order, orders updateData)
        {
            updateData.PaymentStatus = "voided";
            //updateData.CancelReason = request.Form["CancelReason"];
            //// 库存回滚
            //HelpsOrders.OrdersProductsUpdate(db, 7, order, 1);
        }
        private void HandleCompletedStatus(orders order, orders updateData)
        {
            updateData.PaymentStatus = "paid";
            updateData.ShippingStatus = "shipped";

            // 优惠券发放
            var user = db.Queryable<user>()
                .Where(u => u.UserId == order.UserId)
                .First();

            //HelpsUser.GetUserCoupons(db, new List<int>(), user, 4);
            //HelpsOrders.OrdersUserUpdate(db, order.OrderId);
        }


        private void HandleShippingStatus(orders order, orders updateData, int wId,
     string trackingNumbers,
    int shippingTimes, string remarks,
    string carrierData, decimal amount = 0m)
        {
            var ordersPackage =  db.Queryable<orders_package>()
                 .Where(x => x.WId == wId).First();
            ordersPackage.TrackingNumber = trackingNumbers;
            ordersPackage.ShippingTime = shippingTimes;
            ordersPackage.Remarks = remarks;
            ordersPackage.Carrier = carrierData;
            ordersPackage.Status = 1;
            db.Updateable(ordersPackage)
                .UpdateColumns(p => new { p.TrackingNumber, p.ShippingTime, p.Remarks, p.Carrier, p.Status })
                .ExecuteCommand();

            // 发货状态判断
            var unshippedCount = db.Queryable<orders_package>()
                .Where(p => p.OrderId == order.OrderId && p.Status == 0)
                .Count();

            updateData.ShippingStatus = unshippedCount > 0 ? "partial" : "shipped";

            // 积分处理
            _helpsUserService.PointsPlaceorder(updateData.ShippingStatus, order, amount);
        }


        private void HandlePaymentStatus( orders order, orders updateData, int wId,decimal amount=0m)
        {
            updateData.PaymentStatus = "paid";
            updateData.OrderStatus=5;
            updateData.ShippingStatus = "shipped";

            // 新客户检查
            var newCustomerCount = db.Queryable<orders>()
                .Where(o => o.Email == order.Email && new[] { 4, 5, 6 }.Contains(o.OrderStatus))
                .Count();

            if (newCustomerCount == 0)
            {
                db.Updateable(new orders { IsNewCustom = true })
                    .Where(o => o.OrderId == order.OrderId)
                    .ExecuteCommand();
            }

            // 积分处理
            _helpsUserService.PointsPlaceorder("paid", order, amount);
        }





        /// <summary>
        /// 合单列表信息
        /// </summary>
        /// <param name="WId"></param>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<List<WaybillPackageListResponse>> WaybillPackageList(int WId, int OrderId)
        {
            List<WaybillPackageListResponse> WaybillPackageList = new List<WaybillPackageListResponse>();
            var ordersPackage = await db.Queryable<orders_package>()
                .Where(x => x.WId == WId).FirstAsync();
            //var orderss = await db.Queryable<orders>()
            //    .Where(x => x.OrderId == OrderId).FirstAsync();
            var ordersPackageList = await db.Queryable<orders_package>()
                .Where(x => x.OrderId == OrderId && x.ParentId == 0 && x.ProInfo != "[]").ToListAsync();
            if (ordersPackageList != null)
            {
                int i = 1;
                foreach (var item in ordersPackageList)
                {
                    if (item.WId != WId)
                    {
                        WaybillPackageList.Add(new WaybillPackageListResponse()
                        {
                            IsMerge = ordersPackage.Warehouse==item.Warehouse?1:0,
                            Key = "包裹" + i,
                            WId = item.WId
                        });
                    }

                    i++;
                }
            }
            //{"msg":[{"IsMerge":1,"Key":"包裹2","WId":32}],"ret":1}

            return WaybillPackageList;
        }
        /// <summary>
        /// 合单操作
        /// </summary>
        /// <param name="selectList"></param>
        /// <param name="OrderId"></param>
        /// <param name="WId"></param>
        /// <returns></returns>
        public async Task<bool> WaybillConsolidation(List<int> selectList, int OrderId, int WId)
        {
            var ordersPackageList = db.Queryable<orders_package>()
                .Where(x => selectList.Contains(x.WId)||x.WId==WId)
                .ToList();
            var ordersPackageProInfo = MergeJsonStrings(ordersPackageList);

            var ordersPackageFirst = db.Queryable<orders_package>()
                .Where(x => x.WId == WId).First();
            ordersPackageFirst.ProInfo = ordersPackageProInfo;
            var b = db.Updateable(ordersPackageFirst).ExecuteCommandHasChange();
            if (b)
            {
                db.Deleteable<orders_package>().Where(x => selectList.Contains(x.WId)).ExecuteCommandHasChange();
            }
            //select[](2)[…]
            //0   "54"
            //1   "56"
            //OrderId "47"
            //WId "49"

            return true;
        }




        public static string MergeJsonStrings(List<orders_package> orders_Packages)
        {
            var merged = new JObject();

            foreach (var input in orders_Packages)
            {
                if (string.IsNullOrWhiteSpace(input.ProInfo)) continue;

                try
                {
                    var obj = JObject.Parse(input.ProInfo);
                    foreach (var prop in obj.Properties())
                    {
                        HandleProperty(merged, prop);
                    }
                }
                catch (JsonReaderException)
                {
                    // 忽略无效的JSON输入
                }
            }

            return merged.ToString(Formatting.None);
        }

        private static void HandleProperty(JObject merged, JProperty prop)
        {
            string key = prop.Name;
            JToken value = prop.Value;

            if (merged.TryGetValue(key, out JToken existingValue))
            {
                // 尝试数值累加
                if (existingValue.Type == JTokenType.Integer &&
                    value.Type == JTokenType.String &&
                    int.TryParse(value.ToString(), out int numericValue))
                {
                    merged[key] = (int)existingValue + numericValue;
                }
                else if (existingValue.Type == JTokenType.Integer &&
                         value.Type == JTokenType.Integer)
                {
                    merged[key] = (int)existingValue + (int)value;
                }
                else if (existingValue.Type == JTokenType.Float &&
                         value.Type == JTokenType.Float)
                {
                    merged[key] = (double)existingValue + (double)value;
                }
                // 其他类型保持最后出现的值（可根据需要修改）
                else
                {
                    merged[key] = value;
                }
            }
            else
            {
                // 首次出现的键
                merged.Add(key, value);
            }
        }

        /// <summary>
        /// 获取拆单列表信息
        /// </summary>
        /// <param name="WId"></param>
        /// <returns></returns>
        public async Task<List<WaybillOrdersProductsResponse>> WaybillOrdersProductsList(int WId)
        {
            List<WaybillOrdersProductsResponse> WaybillOrdersProductsList = new List<WaybillOrdersProductsResponse>();
            var ProInfoFirst = db.Queryable<orders_package>()
                .Where(x => x.WId == WId).First();
            if (ProInfoFirst != null && ProInfoFirst.ProInfo != "[]")
            {
                var ordersProductsList = db.Queryable<orders_products_list>()
               .Where(x => x.OrderId == ProInfoFirst.OrderId)
               .ToList();
                var dict = JsonConvert.DeserializeObject<Dictionary<string, int>>(ProInfoFirst.ProInfo);
                List<int> productIds = dict.Keys.Select(int.Parse).ToList();
                WaybillOrdersProductsList = db.Queryable<orders_products_list>()
                    .Where(x => productIds.Contains(x.LId) && x.OrderId == ProInfoFirst.OrderId)
                    .Select(x => (WaybillOrdersProductsResponse)x)
                    .ToList();
                if (WaybillOrdersProductsList != null)
                {
                    WaybillOrdersProductsList.ForEach(p =>
                    {
                        p.AttributesShowHtml = "<div class=\"custom_attr\"></div>";
                        p.PropertyAry = p.Property;
                        p.Url = "";
                        p.PicPath_0 = p.PicPath;
                        p.WeightHtml = "单件重量: " + p.Weight + "kg，总重量: <span class=\"subtotal\"> " + p.Weight + "</span>kg ";
                        p.Qty = dict[p.LId.ToString()];

                    }
                    );
                }

            }
            return WaybillOrdersProductsList;
        }
        /// <summary>
        /// 拆单
        /// </summary>
        /// <param name="WId"></param>
        /// <param name="OrderId"></param>
        /// <param name="OvId"></param>
        /// <param name="waybillDict"></param>
        /// <returns></returns>
        public async Task<bool> OrdersWaybillSeparate(int WId, int OrderId, int OvId, SortedDictionary<string, string> waybillDict)
        {
            var ProInfoFirst = db.Queryable<orders_package>()
               .Where(x => x.WId == WId && x.OrderId == OrderId).First();
            // 过滤出值不为 "0" 的键值对
            var filteredDict = waybillDict
            .Where(kvp => kvp.Value != "0")
            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            // 从字符串解析 JSON 到字典
            var jsonDict = JsonConvert.DeserializeObject<Dictionary<string, int>>(ProInfoFirst.ProInfo);

            // 将 SortedDictionary 转换为 Dictionary
            var dictFromSorted = filteredDict.ToDictionary(kvp => kvp.Key, kvp => int.Parse(kvp.Value));

            // 进行比较
            foreach (var kvp in dictFromSorted)
            {
                if (jsonDict.ContainsKey(kvp.Key))
                {
                    jsonDict[kvp.Key] -= kvp.Value; // 减去已存在的值
                    if (jsonDict[kvp.Key] <= 0)   // 如果结果为零或负数，移除该键
                    {
                        jsonDict.Remove(kvp.Key);
                    }
                }
            }

            if (jsonDict != null&& jsonDict.Count>0)
            {
                ProInfoFirst.ProInfo = JsonConvert.SerializeObject(jsonDict);
                var b = db.Updateable(ProInfoFirst).ExecuteCommandHasChange();
                ProInfoFirst.ProInfo = JsonConvert.SerializeObject(filteredDict);
                var id = db.Insertable(ProInfoFirst).ExecuteReturnIdentity();
            }



            return true;
        }




    }
}
