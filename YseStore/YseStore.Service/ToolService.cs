using Aliyun.OSS;
using Aliyun.OSS.Util;
using Aop.Api.Domain;
using Flurl;
using Flurl.Http;
using Microsoft.AspNetCore.Http;
using MiniExcelLibs.Csv;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using YseStore.Model.Response;
using YseStore.Model.VM.Aliyun;
using YseStore.Model.VM.Setting;

namespace YseStore.Service
{
    public class ToolService : IToolService
    {
        #region 阿里云上传方法（File）
        /// <summary>
        /// 阿里云上传方法（File）
        /// </summary>
        /// <param name="options"></param>
        /// <param name="fileExt"></param>
        /// <param name="file"></param>
        /// <returns></returns>
        public async Task<VM_UploadObject> UpLoadFileForAliYunOSS(FilesStorageOptions options, string fileExt, IFormFile file)
        {


            var newFileName = DateTime.Now.ToString("yyyyMMddHHmmss_ffff", DateTimeFormatInfo.InvariantInfo) + fileExt;
            var today = DateTime.Now.ToString("yyyyMMdd");

            //上传到阿里云
            await using var fileStream = file.OpenReadStream();
            var md5 = OssUtils.ComputeContentMd5(fileStream, file.Length);
            //string originalFileName = file.FileName;

            // 修改3：文件名处理逻辑
            string originalName = Path.GetFileNameWithoutExtension(file.FileName);
            string processedName = Regex.Replace(originalName?.ToLower() ?? "", @"[^a-z0-9-_]", "-")
                                        .Substring(0, Math.Min(originalName?.Length ?? 0, 100));
            processedName = string.IsNullOrWhiteSpace(processedName)
                   ? Guid.NewGuid().ToString("n").Substring(0, 8)
                   : processedName;
            //string safeFileName = $"{processedName}{fileExt}";
            var originalFileName = processedName;

            // 移除后缀名
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            newFileName = fileNameWithoutExtension+ "_" + newFileName;
            var filePath = options.Path + today + "/" + newFileName; //云文件保存路径
            //初始化阿里云配置--外网Endpoint、访问ID、访问password
            var aliYun = new OssClient(options.Endpoint, options.AccessKeyId, options.AccessKeySecret);
            //将文件md5值赋值给meat头信息，服务器验证文件MD5
            var objectMeta = new ObjectMetadata
            {
                ContentMd5 = md5
            };
            //文件上传--空间名、文件保存路径、文件流、meta头信息(文件md5) //返回meta头信息(文件md5)
            var putResult = aliYun.PutObject(options.BucketName, filePath, fileStream, objectMeta);


            //返回给UEditor的插入编辑器的图片的src

            var fullUrl = $"{options.BucketBindUrl}/{filePath}";

            var fileObj = new VM_UploadObject() { filePath = fullUrl, surplus = "", name = newFileName, oriname = file.FileName, size = file.Length };
            return fileObj;
        }


        public async Task<VM_UploadObject> UpLoadFileForLocalStorage(FilesStorageOptions options, string fileExt, IFormFile file)
        {

            // 生成保存路径
            var uploadFolder = Path.Combine(
                "wwwroot",
                options.Path,
                DateTime.Now.ToString("yyyyMM"),
                DateTime.Now.ToString("dd"),
                "photo"
            );

            // 创建目录（如果不存在）
            Directory.CreateDirectory(uploadFolder);

            // 生成唯一文件名


            var newFileName =  DateTime.Now.ToString("yyyyMMddHHmmss_ffff", DateTimeFormatInfo.InvariantInfo) + fileExt;
            //string originalFileName = file.FileName;


            // 修改3：文件名处理逻辑
            string originalName = Path.GetFileNameWithoutExtension(file.FileName);
            string processedName = Regex.Replace(originalName?.ToLower() ?? "", @"[^a-z0-9-_]", "-")
                                        .Substring(0, Math.Min(originalName?.Length ?? 0, 100));
            processedName = string.IsNullOrWhiteSpace(processedName)
                   ? Guid.NewGuid().ToString("n").Substring(0, 8)
                   : processedName;
            //string safeFileName = $"{processedName}{fileExt}";
            var originalFileName = processedName;


            // 移除后缀名
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            newFileName = fileNameWithoutExtension + "_" + newFileName;
            var filePath = Path.Combine(uploadFolder, newFileName);

            // 保存文件
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }
            var relativePath = filePath
                     .Replace("wwwroot", "")
                     .Replace(Directory.GetCurrentDirectory(), "")
                     .Replace("\\", "/")
                     .TrimStart('/');
            relativePath = $"/{relativePath}";
            var fullUrl = $"{options.BucketBindUrl}/{filePath}";
            var fileObj = new VM_UploadObject() { filePath = fullUrl, surplus = "", name = newFileName, oriname = file.FileName };
            return fileObj;
        }



        /// <summary>
        /// 保存用户导出csv数据到OSS
        /// </summary>
        public async Task<VM_UploadObject> ExportToOss(string jsonString, DataTable dt, FilesStorageOptions ossOptions)
        {
            // 1. 生成CSV内容到内存流（手动管理流生命周期）
            MemoryStream csvStream;
            using (var tempStream = new MemoryStream())
            {
                var config = new CsvConfiguration
                {
                    Seperator = ',',
                    NewLine = Environment.NewLine,
                    Culture = CultureInfo.InvariantCulture,
                    StreamWriterFunc = stream => new StreamWriter(stream, Encoding.UTF8)
                };

                // 写入CSV内容
                using (var writer = new CsvWriter(tempStream, dt, config, printHeader: true))
                {
                    writer.SaveAs();
                }

                // 2. 复制到新流（避免CsvWriter关闭原始流）
                csvStream = new MemoryStream(tempStream.ToArray());
            } // 此处tempStream被释放，但数据已复制到csvStream

            // 3. 重置流位置
            csvStream.Position = 0;

            try
            {
                // 4. 创建IFormFile
                var formFile = new FormFile(
                    baseStream: csvStream,
                    baseStreamOffset: 0,
                    length: csvStream.Length,
                    name: "file",
                    fileName: $"export_{DateTime.Now.ToString("yyyyMMddHHmmss_ffff", DateTimeFormatInfo.InvariantInfo)}.csv"
                )
                {
                    Headers = new HeaderDictionary(),
                    ContentType = "text/csv"
                };

                // 5. 调用OSS上传方法
                return await UpLoadFileForAliYunOSSCycle(
                    options: ossOptions,
                    fileExt: ".csv",
                    file: formFile
                );
            }
            finally
            {
                // 6. 确保最终释放内存流
                csvStream.Dispose();
            }
        }

        public async Task<VM_UploadObject> UpLoadFileForAliYunOSSCycle(FilesStorageOptions options, string fileExt, IFormFile file)
        {


            var newFileName = DateTime.Now.ToString("yyyyMMddHHmmss_ffff", DateTimeFormatInfo.InvariantInfo) + fileExt;
            var today = DateTime.Now.ToString("yyyyMMdd");

            //上传到阿里云
            await using var fileStream = file.OpenReadStream();
            var md5 = OssUtils.ComputeContentMd5(fileStream, file.Length);
            string originalFileName = file.FileName;

            //// 移除后缀名
            //string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            //newFileName = fileNameWithoutExtension + newFileName;
            //var filePath = options.Path + today + "/" + newFileName; //云文件保存路径
            var filePath = $"{options.Path}temp/{today}/{newFileName}";  // 修改路径

            //初始化阿里云配置--外网Endpoint、访问ID、访问password
            var aliYun = new OssClient(options.Endpoint, options.AccessKeyId, options.AccessKeySecret);
            //将文件md5值赋值给meat头信息，服务器验证文件MD5
            var objectMeta = new ObjectMetadata
            {
                ContentMd5 = md5,
                // 可选：添加删除标记元数据
                UserMetadata = {
                    { "auto-delete", "true" },
                    { "delete-time", DateTimeOffset.Now.AddDays(1).ToUnixTimeSeconds().ToString() }
                }
            };
            //文件上传--空间名、文件保存路径、文件流、meta头信息(文件md5) //返回meta头信息(文件md5)
            var putResult = aliYun.PutObject(options.BucketName, filePath, fileStream, objectMeta);


            //返回给UEditor的插入编辑器的图片的src

            var fullUrl = $"{options.BucketBindUrl}/{filePath}";

            var fileObj = new VM_UploadObject() { filePath = fullUrl, surplus = "", name = newFileName, oriname = file.FileName, size = file.Length };
            return fileObj;
        }



        #endregion
    }
}
