using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using YseStore.Common.Helper;
using YseStore.Model.Entities;
using YseStore.Repo;
using File = System.IO.File;

namespace YseStore.Service
{
    /// <summary>
    /// 代码编辑服务实现
    /// </summary>
    public class CodeEditService : BaseServices<CodeFile>, ICodeEditService
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IConfiguration _configuration;
        private readonly ILogger<CodeEditService> _logger;
        private readonly IBaseRepository<CodeFileHistory> _historyRepository;
        private string _baseViewsPath;

        /// <summary>
        /// 构造函数
        /// </summary>
        public CodeEditService(
            IWebHostEnvironment webHostEnvironment,
            IConfiguration configuration,
            ILogger<CodeEditService> logger,
            IBaseRepository<CodeFile> baseRepository,
            IBaseRepository<CodeFileHistory> historyRepository) : base(baseRepository)
        {
            _webHostEnvironment = webHostEnvironment;
            _logger = logger;
            _historyRepository = historyRepository;
            _configuration = configuration;

            // 获取当前项目的根目录
            var currentDir = _webHostEnvironment.ContentRootPath;

            // 从 appsettings.json 中读取 Views 目录路径
            var viewsBasePathFromConfig = _configuration["ViewsBasePath"];

            if (string.IsNullOrWhiteSpace(viewsBasePathFromConfig))
            {
                _logger.LogError("未能从配置文件中读取 ViewsBasePath，请检查 appsettings.json 配置。");
                throw new InvalidOperationException("未能从配置文件中读取 ViewsBasePath，请检查 appsettings.json 配置。");
            }
            // 构建完整路径
            _baseViewsPath = Path.GetFullPath(Path.Combine(currentDir, viewsBasePathFromConfig));

            // 确保目录存在
            if (!Directory.Exists(_baseViewsPath))
            {
                _logger.LogWarning($"Views 目录不存在: {_baseViewsPath}");
            }
            
            // 记录路径以便调试
            // _logger.LogInformation($"Views 目录路径设置为: {_baseViewsPath}");

            // 确保目录存在
            if (!Directory.Exists(_baseViewsPath))
            {
                _logger.LogWarning($"Views 目录不存在: {_baseViewsPath}");
            }
        }

        /// <summary>
        /// 获取文件树结构
        /// </summary>
        public async Task<List<CodeFileTreeVM>> GetFileTree()
        {
            try
            {
                // 确保目录存在
                if (!Directory.Exists(_baseViewsPath))
                {
                    _logger.LogError($"Views目录不存在: {_baseViewsPath}");
                    return new List<CodeFileTreeVM>();
                }

                // 获取根目录下的所有文件和目录
                var rootEntries = new DirectoryInfo(_baseViewsPath).GetFileSystemInfos();
                var result = new List<CodeFileTreeVM>();

                foreach (var entry in rootEntries)
                {
                    // 创建视图模型
                    var treeNode = await CreateTreeNodeFromFileSystemInfo(entry, "");
                    result.Add(treeNode);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文件树结构时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 根据文件系统信息创建树节点
        /// </summary>
        private async Task<CodeFileTreeVM> CreateTreeNodeFromFileSystemInfo(FileSystemInfo info, string relativePath)
        {
            bool isDirectory = info is DirectoryInfo;
            string name = info.Name;
            string fullRelativePath = Path.Combine(relativePath, name).Replace("\\", "/");

            // 查询数据库中是否存在该文件记录
            var dbFile = await BaseDal.Query(f => f.FilePath == fullRelativePath);

            var node = new CodeFileTreeVM
            {
                Id = dbFile.Any() ? dbFile.First().Id : null,
                Name = name,
                Path = fullRelativePath,
                IsDirectory = isDirectory,
                FileType = Path.GetExtension(name),
                LastModified = info.LastWriteTime,
                FileSize = isDirectory ? 0 : ((FileInfo)info).Length
            };

            // 如果是目录，则递归获取子文件和目录
            if (isDirectory)
            {
                var directoryInfo = (DirectoryInfo)info;
                var childEntries = directoryInfo.GetFileSystemInfos();

                foreach (var childEntry in childEntries)
                {
                    var childNode = await CreateTreeNodeFromFileSystemInfo(childEntry, fullRelativePath);
                    node.Children.Add(childNode);
                }

                // 目录按名称排序
                node.Children = node.Children.OrderBy(c => !c.IsDirectory).ThenBy(c => c.Name).ToList();
            }

            return node;
        }

        /// <summary>
        /// 获取文件内容
        /// </summary>
        public async Task<CodeFileContentVM> GetFileContent(string path)
        {
            try
            {
                // 构建完整路径
                string fullPath = Path.Combine(_baseViewsPath, path);

                // 检查文件是否存在
                if (!File.Exists(fullPath))
                {
                    _logger.LogError($"文件不存在: {fullPath}");
                    return null;
                }

                // 查询数据库中是否存在该文件记录
                var dbFile = await BaseDal.Query(f => f.FilePath == path);

                // 使用FileHelper读取文件内容
                string content = FileHelper.ReadFile(fullPath, Encoding.UTF8);

                var fileInfo = new FileInfo(fullPath);

                return new CodeFileContentVM
                {
                    Id = dbFile.Any() ? dbFile.First().Id : null,
                    Name = Path.GetFileName(path),
                    Path = path,
                    Content = content,
                    FileType = Path.GetExtension(path),
                    LastModified = fileInfo.LastWriteTime,
                    FileSize = fileInfo.Length
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取文件内容时发生错误: {path}");
                throw;
            }
        }

        /// <summary>
        /// 保存文件内容
        /// </summary>
        public async Task<bool> SaveFileContent(string path, string content, string comment, string username)
        {
            try
            {
                // 构建完整路径
                string fullPath = Path.Combine(_baseViewsPath, path);

                // 检查文件是否存在
                if (!File.Exists(fullPath))
                {
                    _logger.LogError($"文件不存在: {fullPath}");
                    return false;
                }

                // 使用FileHelper读取原文件内容，用于创建历史记录
                string originalContent = FileHelper.ReadFile(fullPath, Encoding.UTF8);

                // 如果内容没有变化，直接返回成功
                if (originalContent == content)
                {
                    return true;
                }

                // 使用FileHelper保存文件内容
                FileHelper.WriteFile(fullPath, content, Encoding.UTF8);

                // 更新或创建数据库记录
                var fileInfo = new FileInfo(fullPath);
                var dbFile = await BaseDal.Query(f => f.FilePath == path);

                if (dbFile.Any())
                {
                    // 更新现有记录
                    var existingFile = dbFile.First();
                    existingFile.Content = content;
                    existingFile.LastModified = DateTime.Now;
                    existingFile.LastModifiedBy = username;
                    existingFile.FileSize = fileInfo.Length;

                    await BaseDal.Update(existingFile);

                    // 创建历史记录
                    await CreateFileHistory(existingFile.Id, path, originalContent, OperationType.Modify, comment,
                        username);
                }
                else
                {
                    // 创建新记录
                    var newFile = new CodeFile
                    {
                        FilePath = path,
                        FileName = Path.GetFileName(path),
                        Content = content,
                        FileType = Path.GetExtension(path),
                        LastModified = DateTime.Now,
                        LastModifiedBy = username,
                        CreatedAt = DateTime.Now,
                        CreatedBy = username,
                        IsDirectory = false,
                        FileSize = fileInfo.Length
                    };

                    long fileId = await BaseDal.Add(newFile);

                    // 创建历史记录
                    await CreateFileHistory(fileId, path, originalContent, OperationType.Create, comment, username);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存文件内容时发生错误: {path}");
                throw;
            }
        }

        /// <summary>
        /// 创建文件
        /// </summary>
        public async Task<bool> CreateFile(string path, string content, string username)
        {
            try
            {
                // 构建完整路径
                string fullPath = Path.Combine(_baseViewsPath, path);

                // 检查文件是否已存在
                if (File.Exists(fullPath))
                {
                    _logger.LogError($"文件已存在: {fullPath}");
                    return false;
                }

                // 确保目录存在
                string directory = Path.GetDirectoryName(fullPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 使用FileHelper创建文件
                FileHelper.WriteFile(fullPath, content ?? string.Empty, Encoding.UTF8);

                // 创建数据库记录
                var fileInfo = new FileInfo(fullPath);
                var newFile = new CodeFile
                {
                    FilePath = path,
                    FileName = Path.GetFileName(path),
                    Content = content,
                    FileType = Path.GetExtension(path),
                    LastModified = DateTime.Now,
                    LastModifiedBy = username,
                    CreatedAt = DateTime.Now,
                    CreatedBy = username,
                    IsDirectory = false,
                    FileSize = fileInfo.Length
                };

                long fileId = await BaseDal.Add(newFile);

                // 创建历史记录
                await CreateFileHistory(fileId, path, string.Empty, OperationType.Create, "创建文件", username);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"创建文件时发生错误: {path}");
                throw;
            }
        }

        /// <summary>
        /// 创建目录
        /// </summary>
        public async Task<bool> CreateDirectory(string path, string username)
        {
            try
            {
                // 构建完整路径
                string fullPath = Path.Combine(_baseViewsPath, path);

                // 检查目录是否已存在
                if (Directory.Exists(fullPath))
                {
                    _logger.LogError($"目录已存在: {fullPath}");
                    return false;
                }

                // 创建目录
                Directory.CreateDirectory(fullPath);

                // 创建数据库记录
                var newDirectory = new CodeFile
                {
                    FilePath = path,
                    FileName = Path.GetFileName(path),
                    Content = null,
                    FileType = null,
                    LastModified = DateTime.Now,
                    LastModifiedBy = username,
                    CreatedAt = DateTime.Now,
                    CreatedBy = username,
                    IsDirectory = true,
                    FileSize = 0
                };

                await BaseDal.Add(newDirectory);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"创建目录时发生错误: {path}");
                throw;
            }
        }

        /// <summary>
        /// 删除文件或目录
        /// </summary>
        public async Task<bool> DeleteFileOrDirectory(string path, string username)
        {
            try
            {
                // 构建完整路径
                string fullPath = Path.Combine(_baseViewsPath, path);

                // 检查路径是否存在
                bool isDirectory = Directory.Exists(fullPath);
                bool isFile = File.Exists(fullPath);

                if (!isDirectory && !isFile)
                {
                    _logger.LogError($"文件或目录不存在: {fullPath}");
                    return false;
                }

                // 查询数据库中是否存在该文件或目录记录
                var dbFile = await BaseDal.Query(f => f.FilePath == path);

                // 如果是文件，保存内容用于历史记录
                string originalContent = string.Empty;
                if (isFile && dbFile.Any())
                {
                    originalContent = FileHelper.ReadFile(fullPath, Encoding.UTF8);
                }

                // 删除文件或目录
                if (isDirectory)
                {
                    // 使用FileHelper删除目录
                    FileHelper.DeleteFolder(fullPath);

                    // 递归删除数据库中的目录及其子文件记录
                    var allDbFiles = await BaseDal.Query(f => f.FilePath.StartsWith(path));
                    if (allDbFiles.Any())
                    {
                        var longs = allDbFiles.Select(f => f.Id).ToList();
                        await BaseDal.DeleteByIds(longs.Cast<object>().ToArray());
                    }
                }
                else
                {
                    // 使用FileHelper删除文件
                    FileHelper.FileDel(fullPath);

                    // 如果数据库中有记录，创建历史记录并删除
                    if (dbFile.Any())
                    {
                        var file = dbFile.First();
                        await CreateFileHistory(file.Id, path, originalContent, OperationType.Delete, "删除文件", username);
                        await BaseDal.Delete(file);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"删除文件或目录时发生错误: {path}");
                throw;
            }
        }

        /// <summary>
        /// 重命名文件或目录
        /// </summary>
        public async Task<bool> RenameFileOrDirectory(string oldPath, string newPath, string username)
        {
            try
            {
                // 构建完整路径
                string oldFullPath = Path.Combine(_baseViewsPath, oldPath);
                string newFullPath = Path.Combine(_baseViewsPath, newPath);

                // 检查原路径是否存在
                bool isDirectory = Directory.Exists(oldFullPath);
                bool isFile = File.Exists(oldFullPath);

                if (!isDirectory && !isFile)
                {
                    _logger.LogError($"原文件或目录不存在: {oldFullPath}");
                    return false;
                }

                // 检查新路径是否已存在
                if (Directory.Exists(newFullPath) || File.Exists(newFullPath))
                {
                    _logger.LogError($"新文件或目录已存在: {newFullPath}");
                    return false;
                }

                // 确保新路径的目录存在
                string newDirectory = Path.GetDirectoryName(newFullPath);
                if (!Directory.Exists(newDirectory))
                {
                    Directory.CreateDirectory(newDirectory);
                }

                // 重命名文件或目录
                if (isDirectory)
                {
                    Directory.Move(oldFullPath, newFullPath);

                    // 更新数据库中的记录
                    var allDbFiles = await BaseDal.Query(f => f.FilePath.StartsWith(oldPath));
                    foreach (var file in allDbFiles)
                    {
                        string newFilePath = file.FilePath.Replace(oldPath, newPath);
                        file.FilePath = newFilePath;
                        file.FileName = Path.GetFileName(newFilePath);
                        file.LastModified = DateTime.Now;
                        file.LastModifiedBy = username;

                        await BaseDal.Update(file);
                    }
                }
                else
                {
                    // 读取原文件内容，用于创建历史记录
                    string originalContent = FileHelper.ReadFile(oldFullPath, Encoding.UTF8);

                    // 使用FileHelper移动文件
                    FileHelper.FileMove(oldFullPath, newFullPath);

                    // 更新数据库记录
                    var dbFile = await BaseDal.Query(f => f.FilePath == oldPath);
                    if (dbFile.Any())
                    {
                        var file = dbFile.First();

                        // 创建重命名前的历史记录
                        await CreateFileHistory(file.Id, oldPath, originalContent, OperationType.Modify,
                            $"重命名为 {newPath}", username);

                        // 更新文件记录
                        file.FilePath = newPath;
                        file.FileName = Path.GetFileName(newPath);
                        file.LastModified = DateTime.Now;
                        file.LastModifiedBy = username;

                        await BaseDal.Update(file);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"重命名文件或目录时发生错误: {oldPath} -> {newPath}");
                throw;
            }
        }

        /// <summary>
        /// 获取文件历史记录
        /// </summary>
        public async Task<List<CodeFileHistoryVM>> GetFileHistory(string path)
        {
            try
            {
                // 查询数据库中的文件记录
                var dbFile = await BaseDal.Query(f => f.FilePath == path);
                if (!dbFile.Any())
                {
                    return new List<CodeFileHistoryVM>();
                }

                var fileId = dbFile.First().Id;

                // 查询历史记录
                var histories = await _historyRepository.Query(h => h.CodeFileId == fileId);

                return histories.Select(h => new CodeFileHistoryVM
                {
                    Id = h.Id,
                    CodeFileId = h.CodeFileId,
                    FilePath = h.FilePath,
                    OperationType = h.OperationType.ToString(),
                    OperationTypeText = GetOperationTypeText(h.OperationType),
                    Comment = h.Comment,
                    CreatedAt = h.CreatedAt,
                    CreatedBy = h.CreatedBy
                }).OrderByDescending(h => h.CreatedAt).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取文件历史记录时发生错误: {path}");
                throw;
            }
        }

        /// <summary>
        /// 获取操作类型的文本描述
        /// </summary>
        private string GetOperationTypeText(int operationType)
        {
            return operationType switch
            {
                1 => "创建",
                2 => "修改",
                3 => "删除",
                _ => "未知操作"
            };
        }

        /// <summary>
        /// 获取历史版本内容
        /// </summary>
        public async Task<string> GetHistoryContent(long historyId)
        {
            try
            {
                var history = await _historyRepository.QueryById(historyId);
                if (history == null)
                {
                    _logger.LogError($"历史记录不存在: {historyId}");
                    return null;
                }

                return history.Content;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取历史版本内容时发生错误: {historyId}");
                throw;
            }
        }

        /// <summary>
        /// 恢复到历史版本
        /// </summary>
        public async Task<bool> RestoreHistoryVersion(long historyId, string username)
        {
            try
            {
                // 获取历史记录
                var history = await _historyRepository.QueryById(historyId);
                if (history == null)
                {
                    _logger.LogError($"历史记录不存在: {historyId}");
                    return false;
                }

                // 获取关联的文件
                var file = await BaseDal.QueryById(history.CodeFileId);
                if (file == null)
                {
                    _logger.LogError($"文件记录不存在: {history.CodeFileId}");
                    return false;
                }

                // 保存文件内容
                return await SaveFileContent(
                    file.FilePath,
                    history.Content,
                    $"恢复到 {history.CreatedAt:yyyy-MM-dd HH:mm:ss} 的版本",
                    username);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"恢复到历史版本时发生错误: {historyId}");
                throw;
            }
        }

        /// <summary>
        /// 同步文件系统到数据库
        /// </summary>
        public async Task<string> SyncFilesToDatabase(string username)
        {
            try
            {
                // 确保目录存在
                if (!Directory.Exists(_baseViewsPath))
                {
                    return $"Views目录不存在: {_baseViewsPath}";
                }

                int newFiles = 0;
                int updatedFiles = 0;
                int skippedFiles = 0;
                int errorFiles = 0;

                // 获取所有文件
                var allFiles = GetAllFiles(_baseViewsPath);

                foreach (var filePath in allFiles)
                {
                    try
                    {
                        // 计算相对路径
                        string relativePath = filePath.Replace(_baseViewsPath, "").TrimStart('\\', '/')
                            .Replace("\\", "/");

                        // 检查数据库中是否已存在
                        var dbFile = await BaseDal.Query(f => f.FilePath == relativePath);

                        // 获取文件信息
                        var fileInfo = new FileInfo(filePath);
                        string content = string.Empty;

                        // 如果是文件，读取其内容
                        if (fileInfo.Exists)
                        {
                            try
                            {
                                content = FileHelper.ReadFile(filePath, Encoding.UTF8);
                            }
                            catch
                            {
                                // 对于无法读取的文件，跳过
                                skippedFiles++;
                                continue;
                            }
                        }

                        if (!dbFile.Any())
                        {
                            // 创建新记录
                            var newFile = new CodeFile
                            {
                                FilePath = relativePath,
                                FileName = Path.GetFileName(filePath),
                                Content = content,
                                FileType = Path.GetExtension(filePath),
                                LastModified = fileInfo.LastWriteTime,
                                LastModifiedBy = username,
                                CreatedAt = DateTime.Now,
                                CreatedBy = username,
                                IsDirectory = false,
                                FileSize = fileInfo.Length
                            };

                            await BaseDal.Add(newFile);
                            newFiles++;
                        }
                        else
                        {
                            // 更新现有记录
                            var existingFile = dbFile.First();

                            // 检查内容是否变化
                            if (existingFile.Content != content || existingFile.LastModified < fileInfo.LastWriteTime)
                            {
                                existingFile.Content = content;
                                existingFile.LastModified = fileInfo.LastWriteTime;
                                existingFile.FileSize = fileInfo.Length;

                                await BaseDal.Update(existingFile);
                                updatedFiles++;
                            }
                            else
                            {
                                skippedFiles++;
                            }
                        }
                    }
                    catch
                    {
                        errorFiles++;
                    }
                }

                // 同步目录
                var allDirectories = Directory.GetDirectories(_baseViewsPath, "*", SearchOption.AllDirectories);
                foreach (var dirPath in allDirectories)
                {
                    string relativePath = dirPath.Replace(_baseViewsPath, "").TrimStart('\\', '/').Replace("\\", "/");

                    // 检查数据库中是否已存在
                    var dbDir = await BaseDal.Query(f => f.FilePath == relativePath && f.IsDirectory);

                    if (!dbDir.Any())
                    {
                        // 创建新目录记录
                        var newDir = new CodeFile
                        {
                            FilePath = relativePath,
                            FileName = Path.GetFileName(dirPath),
                            Content = null,
                            FileType = null,
                            LastModified = Directory.GetLastWriteTime(dirPath),
                            LastModifiedBy = username,
                            CreatedAt = DateTime.Now,
                            CreatedBy = username,
                            IsDirectory = true,
                            FileSize = 0
                        };

                        await BaseDal.Add(newDir);
                        newFiles++;
                    }
                }

                return $"同步完成。新增: {newFiles}, 更新: {updatedFiles}, 跳过: {skippedFiles}, 错误: {errorFiles}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步文件系统到数据库时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取目录下的所有文件
        /// </summary>
        private List<string> GetAllFiles(string directoryPath)
        {
            var result = new List<string>();

            try
            {
                // 获取所有文件
                var files = Directory.GetFiles(directoryPath, "*.*", SearchOption.AllDirectories);
                result.AddRange(files);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取目录下的所有文件时发生错误: {directoryPath}");
            }

            return result;
        }

        /// <summary>
        /// 创建文件历史记录
        /// </summary>
        private async Task<long> CreateFileHistory(long fileId, string filePath, string content,
            OperationType operationType, string comment, string username)
        {
            var history = new CodeFileHistory
            {
                CodeFileId = fileId,
                FilePath = filePath,
                Content = content,
                OperationType = (int)operationType,
                Comment = comment,
                CreatedAt = DateTime.Now,
                CreatedBy = username
            };

            return await _historyRepository.Add(history);
        }
    }
}