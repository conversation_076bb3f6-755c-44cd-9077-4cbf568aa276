using SqlSugar;
using YseStore.IService.Products;
using YseStore.Model;
using YseStore.Model.RequestModels.Products;
using YseStore.Repo;
using Newtonsoft.Json;

namespace YseStore.Service.Products
{
    /// <summary>
    /// 产品价格历史记录服务实现
    /// </summary>
    public class ProductPriceHistoryService : BaseServices<products_price_history>, IProductPriceHistoryService
    {
        public ProductPriceHistoryService(IBaseRepository<products_price_history> baseRepository) : base(baseRepository)
        {
        }

        /// <summary>
        /// 记录批量改价历史
        /// </summary>
        public async Task<bool> RecordBatchPriceHistoryAsync(BatchPriceHistoryRequest request)
        {
            try
            {
                var historyRecords = new List<products_price_history>();
                int currentTimestamp = (int)((DateTimeOffset)DateTime.Now).ToUnixTimeSeconds();

                // 构建批量改价参数JSON
                var batchPriceParams = JsonConvert.SerializeObject(new
                {
                    priceType = request.PriceType,
                    calcMethod = request.CalcMethod,
                    calcSymbol = request.CalcSymbol,
                    calcValue = request.CalcValue
                });

                foreach (var productId in request.ProductIds)
                {
                    // 获取产品信息
                    var product = await Db.Queryable<products>()
                        .Where(p => p.ProId == productId)
                        .FirstAsync();

                    if (product == null) continue;

                    // 计算新价格
                    decimal oldPrice_Before = 0, oldPrice_After = 0;
                    decimal price_Before = 0, price_After = 0;
                    decimal costPrice_Before = 0, costPrice_After = 0;

                    switch (request.PriceType)
                    {
                        case "origin_price":
                            oldPrice_Before = product.Price_0 ?? 0;
                            oldPrice_After = CalculateNewPrice(oldPrice_Before, request.CalcMethod, request.CalcSymbol, request.CalcValue);
                            break;
                        case "shop_price":
                            price_Before = product.Price_1 ?? 0;
                            price_After = CalculateNewPrice(price_Before, request.CalcMethod, request.CalcSymbol, request.CalcValue);
                            break;
                        case "cost_price":
                            costPrice_Before = product.CostPrice;
                            costPrice_After = CalculateNewPrice(costPrice_Before, request.CalcMethod, request.CalcSymbol, request.CalcValue);
                            break;
                    }

                    // 记录产品主表的价格变更
                    var mainRecord = new products_price_history
                    {
                        ProId = productId,
                        CombinationId = null,
                        VariantsId = "",
                        IsCombination = product.IsCombination,
                        OldPrice_Before = request.PriceType == "origin_price" ? oldPrice_Before : null,
                        OldPrice_After = request.PriceType == "origin_price" ? oldPrice_After : null,
                        Price_Before = request.PriceType == "shop_price" ? price_Before : null,
                        Price_After = request.PriceType == "shop_price" ? price_After : null,
                        CostPrice_Before = request.PriceType == "cost_price" ? costPrice_Before : null,
                        CostPrice_After = request.PriceType == "cost_price" ? costPrice_After : null,
                        ChangeSource = "batch_price",
                        BatchPriceParams = batchPriceParams,
                        ProductName = product.Name_en,
                        VariantName = "主产品",
                        SKU = product.SKU,
                        OvId = null, // 批量改价不涉及具体仓库
                        WarehouseName = null,
                        PriorityShippingOvId = null,
                        CreatedTime = currentTimestamp,
                        UserId = request.UserId,
                        UserName = request.UserName,
                        Remark = $"批量改价：{request.PriceType}"
                    };

                    historyRecords.Add(mainRecord);
                    
                }

                // 批量插入历史记录
                if (historyRecords.Any())
                {
                    await Db.Insertable(historyRecords).ExecuteCommandAsync();
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"记录批量改价历史时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 计算新价格
        /// </summary>
        private decimal CalculateNewPrice(decimal currentPrice, string calcMethod, string calcSymbol, decimal calcValue)
        {
            decimal newPrice = currentPrice;

            if (calcMethod == "percent") // 百分比方式
            {
                if (calcSymbol == "add") // 加
                {
                    newPrice = currentPrice * (1 + calcValue / 100);
                }
                else if (calcSymbol == "min") // 减
                {
                    newPrice = currentPrice * (1 - calcValue / 100);
                }
            }
            else if (calcMethod == "value") // 固定值方式
            {
                if (calcSymbol == "add") // 加
                {
                    newPrice = currentPrice + calcValue;
                }
                else if (calcSymbol == "min") // 减
                {
                    newPrice = currentPrice - calcValue;
                }
            }

            return Math.Round(newPrice, 2);
        }

        /// <summary>
        /// 记录规格处理价格变更历史
        /// </summary>
        public async Task<bool> RecordProcessVariantsHistoryAsync(ProcessVariantsHistoryRequest request)
        {
            try
            {
                var historyRecords = new List<products_price_history>();
                int currentTimestamp = (int)((DateTimeOffset)DateTime.Now).ToUnixTimeSeconds();

                // 获取产品信息
                var product = await Db.Queryable<products>()
                    .Where(p => p.ProId == request.ProductId)
                    .FirstAsync();

                if (product == null) return false;

                // 比较新旧规格数据，只有价格相关字段变更时才记录历史
                foreach (var newVariant in request.NewVariants)
                {
                    // 查找对应的旧规格数据
                    var oldVariant = request.OldVariants?.FirstOrDefault(o =>
                        o.VariantsId == newVariant.VariantsId ||
                        (string.IsNullOrEmpty(o.VariantsId) && string.IsNullOrEmpty(newVariant.VariantsId)));

                    // 检查是否有价格相关字段变更
                    bool hasPriceChange = false;
                    decimal? oldPrice_Before = oldVariant?.OldPrice;
                    decimal? oldPrice_After = newVariant.OldPrice;
                    decimal? price_Before = oldVariant?.Price;
                    decimal? price_After = newVariant.Price;
                    decimal? costPrice_Before = oldVariant?.CostPrice;
                    decimal? costPrice_After = newVariant.CostPrice;

                    // 如果旧规格不存在，说明是新增的规格，跳过不记录
                    if (oldVariant == null)
                    {
                        // 新增规格，跳过记录
                        continue;
                    }
                    else
                    {
                        // 比较价格相关字段是否发生变化
                        if (oldPrice_Before != oldPrice_After || price_Before != price_After || costPrice_Before != costPrice_After)
                        {
                            hasPriceChange = true;
                        }
                        // 如果价格相关字段没有变化，跳过记录此条数据
                    }

                    // 只有价格相关字段发生变化时才记录历史
                    if (hasPriceChange)
                    {
                        // 获取仓库名称
                        string warehouseName = null;
                        if (newVariant.OvId > 0)
                        {
                            var warehouse = await Db.Queryable<shipping_overseas>()
                                .Where(w => w.OvId == newVariant.OvId)
                                .FirstAsync();
                            warehouseName = warehouse?.Name;
                        }

                        var record = new products_price_history
                        {
                            ProId = request.ProductId,
                            CombinationId = newVariant.CId,
                            VariantsId = newVariant.VariantsId,
                            IsCombination = request.IsCombination,
                            OldPrice_Before = oldPrice_Before,
                            OldPrice_After = oldPrice_After,
                            Price_Before = price_Before,
                            Price_After = price_After,
                            CostPrice_Before = costPrice_Before,
                            CostPrice_After = costPrice_After,
                            ChangeSource = "process_variants",
                            BatchPriceParams = null,
                            ProductName = product.Name_en,
                            VariantName = newVariant.Title,
                            SKU = newVariant.SKU,
                            OvId = newVariant.OvId,
                            WarehouseName = warehouseName,
                            PriorityShippingOvId = newVariant.PriorityShippingOvId,
                            CreatedTime = currentTimestamp,
                            UserId = request.UserId,
                            UserName = request.UserName,
                            Remark = $"规格处理价格变更 - IsCombination: {request.IsCombination}"
                        };

                        historyRecords.Add(record);
                    }
                    // 如果价格相关字段没有变化，跳过记录此条数据
                }

                // 批量插入历史记录
                if (historyRecords.Any())
                {
                    await Db.Insertable(historyRecords).ExecuteCommandAsync();
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"记录规格处理历史时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 记录手动编辑价格历史
        /// </summary>
        public async Task<bool> RecordManualEditHistoryAsync(ManualEditHistoryRequest request)
        {
            try
            {
                int currentTimestamp = (int)((DateTimeOffset)DateTime.Now).ToUnixTimeSeconds();

                var record = new products_price_history
                {
                    ProId = request.ProductId,
                    CombinationId = request.CombinationId,
                    VariantsId = request.VariantsId,
                    IsCombination = null, // 手动编辑时可能不需要这个字段
                    OldPrice_Before = request.OldPriceBefore,
                    OldPrice_After = request.OldPriceAfter,
                    Price_Before = request.PriceBefore,
                    Price_After = request.PriceAfter,
                    CostPrice_Before = request.CostPriceBefore,
                    CostPrice_After = request.CostPriceAfter,
                    ChangeSource = "manual_edit",
                    BatchPriceParams = null,
                    ProductName = request.ProductName,
                    VariantName = request.VariantName,
                    SKU = request.SKU,
                    OvId = request.OvId,
                    WarehouseName = request.WarehouseName,
                    PriorityShippingOvId = request.PriorityShippingOvId,
                    CreatedTime = currentTimestamp,
                    UserId = request.UserId,
                    UserName = request.UserName,
                    Remark = request.Remark ?? "手动编辑价格"
                };

                await Db.Insertable(record).ExecuteCommandAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"记录手动编辑历史时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取产品价格历史记录
        /// </summary>
        public async Task<PageModel<products_price_history>> GetProductPriceHistoryAsync(GetProductPriceHistoryRequest request)
        {
            try
            {
                var query = Db.Queryable<products_price_history>()
                    .Where(h => h.ProId == request.ProductId);

                // 添加修改来源筛选
                if (!string.IsNullOrEmpty(request.ChangeSource))
                {
                    query = query.Where(h => h.ChangeSource == request.ChangeSource);
                }

                query = query.OrderBy(h => h.CreatedTime, OrderByType.Desc);

                var totalCount = await query.CountAsync();
                var records = await query.ToPageListAsync(request.PageIndex, request.PageSize);

                return new PageModel<products_price_history>
                {
                    dataCount = totalCount,
                    page = request.PageIndex,
                    PageSize = request.PageSize,
                    data = records
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取产品价格历史时出错: {ex.Message}");
                return new PageModel<products_price_history>
                {
                    dataCount = 0,
                    page = request.PageIndex,
                    PageSize = request.PageSize,
                    data = new List<products_price_history>()
                };
            }
        }

        /// <summary>
        /// 获取所有价格历史记录
        /// </summary>
        public async Task<PageModel<products_price_history>> GetAllPriceHistoryAsync(GetAllPriceHistoryRequest request)
        {
            try
            {
                var query = Db.Queryable<products_price_history>();

                // 添加筛选条件
                if (!string.IsNullOrEmpty(request.ChangeSource))
                {
                    query = query.Where(h => h.ChangeSource == request.ChangeSource);
                }

                if (request.StartTime.HasValue)
                {
                    query = query.Where(h => h.CreatedTime >= request.StartTime.Value);
                }

                if (request.EndTime.HasValue)
                {
                    query = query.Where(h => h.CreatedTime <= request.EndTime.Value);
                }

                query = query.OrderBy(h => h.CreatedTime, OrderByType.Desc);

                var totalCount = await query.CountAsync();
                var records = await query.ToPageListAsync(request.PageIndex, request.PageSize);

                return new PageModel<products_price_history>
                {
                    dataCount = totalCount,
                    page = request.PageIndex,
                    PageSize = request.PageSize,
                    data = records
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取所有价格历史时出错: {ex.Message}");
                return new PageModel<products_price_history>
                {
                    dataCount = 0,
                    page = request.PageIndex,
                    PageSize = request.PageSize,
                    data = new List<products_price_history>()
                };
            }
        }
    }
}
