using Microsoft.Extensions.Logging;
using YseStore.Common.Cache;
using YseStore.IService.Products;
using YseStore.Model;
using YseStore.Model.RequestModels.Products;
using YseStore.Repo;

namespace YseStore.Service.Products
{
    /// <summary>
    /// 产品分类服务实现类
    /// </summary>
    public class ProductCategoryService : BaseServices<products_category>, IProductCategoryService
    {
        private readonly IBaseRepository<products_category_description> _descriptionRepository;
        private readonly ICaching _caching;
        private readonly ICategorySeoService _categorySeoService;
        private readonly ILogger<ProductCategoryService> _logger;

        /// <summary>
        /// 构造函数，通过依赖注入获取仓储
        /// </summary>
        /// <param name="baseDal">产品分类仓储</param>
        /// <param name="descriptionDal">产品分类描述仓储</param>
        /// <param name="caching">缓存服务</param>
        /// <param name="categorySeoService">分类SEO服务</param>
        /// <param name="logger">日志服务</param>
        public ProductCategoryService(
            IBaseRepository<products_category> baseDal,
            ICaching caching,
            ICategorySeoService categorySeoService,
            IBaseRepository<products_category_description> descriptionDal,
            ILogger<ProductCategoryService> logger) : base(baseDal)
        {
            _descriptionRepository = descriptionDal;
            _caching = caching;
            _categorySeoService = categorySeoService;
            _logger = logger;
        }

        /// <summary>
        /// 保存产品分类
        /// </summary>
        /// <param name="category"></param>
        /// <param name="descriptions"></param>
        /// <returns></returns>
        public async Task<int> SaveProductCategory(products_category category,
            List<products_category_description> descriptions)
        {
            // 开启事务
            var result = await Db.Ado.UseTranAsync(async () =>
            {
                // 设置修改时间
                category.EditTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);

                // 处理PageUrl唯一性
                if (!string.IsNullOrEmpty(category.PageUrl))
                {
                    // 生成唯一的PageUrl
                    category.PageUrl =
                        await _categorySeoService.GenerateUniqueCategoryPageUrlAsync(category.PageUrl, category.CateId);
                }
                else if (!string.IsNullOrEmpty(category.Category_en))
                {
                    // 如果PageUrl为空，使用分类名称生成
                    category.PageUrl =
                        await _categorySeoService.GenerateUniqueCategoryPageUrlAsync(category.Category_en,
                            category.CateId);
                }

                // 如果是新增操作
                if (category.CateId == 0)
                {
                    category.IsSoldOut = false;
                    // 为新增分类设置排序值
                    if (category.MyOrder == null || category.MyOrder == 0)
                    {
                        // 获取同级分类中最大的排序值
                        var maxOrder = await GetMaxOrderForSameLevel(category.UId);
                        category.MyOrder = (short)(maxOrder + 1);
                    }

                    // 插入分类
                    var categoryId = await Db.Insertable(category).ExecuteReturnIdentityAsync();
                    category.CateId = categoryId;

                    // 设置描述的分类ID并插入多条描述记录
                    foreach (var desc in descriptions)
                    {
                        desc.CateId = categoryId;
                        await Db.Insertable(desc).ExecuteCommandAsync();
                    }

                    return categoryId;
                }
                else // 更新操作
                {
                    // 为更新分类设置排序值（如果没有设置的话）
                    if (category.MyOrder == null || category.MyOrder == 0)
                    {
                        // 获取原有分类的排序值
                        var existingCategory = await Db.Queryable<products_category>()
                            .Where(c => c.CateId == category.CateId)
                            .FirstAsync();

                        if (existingCategory != null && existingCategory.MyOrder.HasValue && existingCategory.MyOrder.Value > 0)
                        {
                            // 保持原有的排序值
                            category.MyOrder = existingCategory.MyOrder;
                        }
                        else
                        {
                            // 如果原有分类也没有排序值，则获取同级分类中最大的排序值
                            var maxOrder = await GetMaxOrderForSameLevel(category.UId);
                            category.MyOrder = (short)(maxOrder + 1);
                        }
                    }

                    // 更新分类
                    await Db.Updateable(category).ExecuteCommandAsync();

                    // 先删除该分类的所有描述记录
                    await Db.Deleteable<products_category_description>()
                        .Where(d => d.CateId == category.CateId)
                        .ExecuteCommandAsync();

                    // 插入新的描述记录
                    foreach (var desc in descriptions)
                    {
                        desc.CateId = category.CateId;
                        await Db.Insertable(desc).ExecuteCommandAsync();
                    }

                    return category.CateId;
                }
            });

            // 如果操作成功，清除/更新缓存
            if (result.IsSuccess)
            {
                // 清除产品分类相关的所有Hash缓存
                await ClearProductCategoryCache();

                // 更新分类PageUrl缓存
                if (!string.IsNullOrEmpty(category.PageUrl) && result.Data > 0)
                {
                    _categorySeoService.UpdateCategoryPageUrlCache(category.PageUrl, result.Data);
                }
            }

            // 直接返回数据库操作结果
            return result.IsSuccess ? result.Data : 0;
        }

        public async Task<(products_category category, List<products_category_description> descriptions)>
            GetProductCategoryById(int cateId)
        {
            if (cateId <= 0)
            {
                return (new products_category(), new List<products_category_description>());
            }

            try
            {
                // 使用Hash缓存获取单个分类数据
                string hashKey = Consts.KEY_ProductCateList;
                string field = $"category_{cateId}";

                // 尝试从Hash缓存中获取数据
                var cacheResult = await SafeHashGetAsync<CategoryWithDescriptions>(hashKey, field);

                if (cacheResult != null)
                {
                    return (cacheResult.Category ?? new products_category(),
                        cacheResult.Descriptions ?? new List<products_category_description>());
                }

                // 缓存中没有数据，从数据库查询
                // 查询分类
                var category = await Db.Queryable<products_category>()
                    .FirstAsync(c => c.CateId == cateId);

                // 如果分类不存在，返回空数据
                if (category == null)
                {
                    return (new products_category(), new List<products_category_description>());
                }

                // 查询所有描述记录
                var descriptions = await Db.Queryable<products_category_description>()
                    .Where(d => d.CateId == cateId)
                    .OrderBy(d => d.PositionType)
                    .ToListAsync();

                // 如果没有描述记录，创建默认的四条记录
                if (descriptions == null || !descriptions.Any())
                {
                    descriptions = new List<products_category_description>
                    {
                        new products_category_description { CateId = cateId, PositionType = 0, Description_en = "" },
                        new products_category_description { CateId = cateId, PositionType = 1, Core_top = "" },
                        new products_category_description { CateId = cateId, PositionType = 2, Core_bottom = "" },
                        new products_category_description
                            { CateId = cateId, PositionType = 3, Description_en_bottom = "" }
                    };
                }

                // 创建缓存数据并存储到Hash中
                var categoryData = new CategoryWithDescriptions
                {
                    Category = category,
                    Descriptions = descriptions
                };

                _caching.HashSet(hashKey, field, categoryData);

                return (category, descriptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetProductCategoryById error: {Message}", ex.Message);
                return (new products_category(), new List<products_category_description>());
            }
        }

        /// <summary>
        /// 包装类，用于在缓存中存储分类和描述
        /// </summary>
        private class CategoryWithDescriptions
        {
            public products_category Category { get; set; }
            public List<products_category_description> Descriptions { get; set; }
        }

        /// <summary>
        /// 查询所有分类列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<PageModel<products_category>> GetAllProductCategories(ProductCategoryQueryRequest model)
        {
            try
            {
                string hashKey = Consts.KEY_ProductCateList;
                // 根据是否包含下架分类使用不同的字段名
                string field = model.IncludeSoldOut ? "all_categories" : "all_categories_active";

                // 尝试从Hash缓存中获取所有分类数据
                var allCategories = await SafeHashGetAsync<List<products_category>>(hashKey, field);

                if (allCategories == null)
                {
                    // 如果缓存中没有数据，则从数据库中加载所有分类数据
                    var queryable = Db.Queryable<products_category>();

                    // 根据参数决定是否过滤下架分类
                    if (!model.IncludeSoldOut)
                    {
                        // 只显示上架的分类（IsSoldOut为false或null）
                        queryable = queryable.Where(c => c.IsSoldOut == false || c.IsSoldOut == null);
                    }

                    // 始终按排序值排序
                    queryable = queryable.OrderBy(c => c.MyOrder);

                    // 查询所有分类并按排序值排序
                    allCategories = await queryable.ToListAsync();

                    // 存储到Hash缓存中
                    _caching.HashSet(hashKey, field, allCategories);
                }

                // 在内存中根据条件筛选数据
                var filteredCategories = allCategories.AsQueryable();

                if (model.Keyword.IsNotEmptyOrNull())
                {
                    filteredCategories = filteredCategories.Where(c => c.Category_en.Contains(model.Keyword));
                }

                // 计算总记录数
                int totalDataCount = filteredCategories.Count();

                // 分页处理
                // var result = filteredCategories
                //     .Skip((model.PageIndex - 1) * model.PageSize)
                //     .Take(model.PageSize)
                //     .ToList();
                var result = filteredCategories
                    .ToList();

                // 构建分类层次结构
                foreach (var category in result)
                {
                    // 计算子分类数量
                    int subCateCount = allCategories.Count(c =>
                        (c.UId.EndsWith(category.CateId.ToString()) && c.UId != category.UId) ||
                        c.UId.Contains($"{category.CateId},"));

                    category.SubCateCount = subCateCount;
                }

                return new PageModel<products_category>
                {
                    data = result,
                    dataCount = totalDataCount,
                    page = model.PageIndex,
                    PageSize = model.PageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetAllProductCategories error: {Message}", ex.Message);
                return new PageModel<products_category>
                {
                    data = new List<products_category>(),
                    dataCount = 0,
                    page = model.PageIndex,
                    PageSize = model.PageSize
                };
            }
        }


        public async Task<bool> BatchDeleteProductCategories(List<int> cateIds)
        {
            if (cateIds == null || !cateIds.Any())
            {
                return false;
            }

            // 获取所有分类数据
            var allCategories = await Db.Queryable<products_category>().ToListAsync();

            // 要删除的所有分类ID列表
            var allCateIdsToDelete = new HashSet<int>(cateIds);

            // 对于每个要删除的分类ID，查找并添加其所有子分类
            foreach (var cateId in cateIds)
            {
                // 查找直接子分类：UId 包含当前分类ID的分类
                // 例如，当 cateId = 1 时，查找 UId 为 "0,1" 或 "1" 或 "xxx,1" 等的分类
                var childCategories = allCategories.Where(c =>
                    !string.IsNullOrEmpty(c.UId) &&
                    c.UId.Split(',').Contains(cateId.ToString())).ToList();

                // 递归查找所有后代分类
                var descendantIds = new HashSet<int>();
                foreach (var child in childCategories)
                {
                    if (!allCateIdsToDelete.Contains(child.CateId))
                    {
                        descendantIds.Add(child.CateId);
                        // 找到此子分类的所有后代
                        FindAllDescendants(allCategories, child.CateId, descendantIds, allCateIdsToDelete);
                    }
                }

                // 添加所有后代分类到删除列表
                allCateIdsToDelete.UnionWith(descendantIds);
            }

            var result = await Db.Ado.UseTranAsync(async () =>
            {
                // 删除分类描述
                await Db.Deleteable<products_category_description>()
                    .Where(d => allCateIdsToDelete.Contains(d.CateId ?? 0))
                    .ExecuteCommandAsync();

                // 删除分类
                await Db.Deleteable<products_category>()
                    .Where(c => allCateIdsToDelete.Contains(c.CateId))
                    .ExecuteCommandAsync();

                return true;
            });

            // 如果删除成功，清除所有与产品分类相关的缓存
            if (result.IsSuccess && result.Data)
            {
                // 清除产品分类相关的所有Hash缓存
                await ClearProductCategoryCache();
            }

            return result.IsSuccess && result.Data;
        }

        // 递归查找所有后代分类
        private void FindAllDescendants(List<products_category> allCategories, int parentId, HashSet<int> descendantIds,
            HashSet<int> alreadyIncluded)
        {
            var children = allCategories.Where(c =>
                !string.IsNullOrEmpty(c.UId) &&
                c.UId.Split(',').Contains(parentId.ToString()) &&
                !alreadyIncluded.Contains(c.CateId) &&
                !descendantIds.Contains(c.CateId)).ToList();

            foreach (var child in children)
            {
                descendantIds.Add(child.CateId);
                FindAllDescendants(allCategories, child.CateId, descendantIds, alreadyIncluded);
            }
        }

        public async Task<bool> DeleteProductCategory(int cateId)
        {
            if (cateId <= 0)
            {
                return false;
            }

            // 简化逻辑，复用批量删除方法
            return await BatchDeleteProductCategories(new List<int> { cateId });
        }

        /// <summary>
        /// 更新产品分类的排序值
        /// </summary>
        /// <param name="categoryOrders"></param>
        /// <returns></returns>
        public async Task<bool> UpdateProductCategoryOrder(List<CategoryOrderInfo> categoryOrders)
        {
            if (categoryOrders == null || !categoryOrders.Any())
            {
                return false;
            }

            var result = await Db.Ado.UseTranAsync(async () =>
            {
                // 获取所有需要更新的分类ID
                var categoryIds = categoryOrders.Select(c => c.CategoryId).ToList();

                // 查询这些分类
                var categories = await Db.Queryable<products_category>()
                    .Where(c => categoryIds.Contains(c.CateId))
                    .ToListAsync();

                // 更新每个分类的排序值
                foreach (var orderInfo in categoryOrders)
                {
                    var category = categories.FirstOrDefault(c => c.CateId == orderInfo.CategoryId);
                    if (category != null)
                    {
                        category.MyOrder = (short)orderInfo.TargetOrder;
                        category.EditTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);

                        // 如果是子分类且指定了父分类ID，可以更新UId
                        if (orderInfo.ParentId.HasValue && orderInfo.ParentId.Value > 0)
                        {
                            // 这里可以根据需求更新UId，例如将子分类移动到不同的父分类下
                            // 这需要根据你的业务规则来实现
                            // category.UId = $"{orderInfo.ParentId.Value},";
                        }
                    }
                }

                // 批量更新分类
                await Db.Updateable(categories).ExecuteCommandAsync();

                return true;
            });

            // 如果更新成功，清除相关缓存
            if (result.IsSuccess && result.Data)
            {
                // 清除产品分类相关的所有Hash缓存
                await ClearProductCategoryCache();
            }

            return result.IsSuccess && result.Data;
        }

        /// <summary>
        /// 批量设置产品分类上下架状态
        /// </summary>
        /// <param name="request">产品分类上下架请求</param>
        /// <returns>设置结果</returns>
        public async Task<bool> BatchSoldOutCategory(ProductCategorySoldOutRequest request)
        {
            if (request == null || request.Id <= 0)
            {
                return false;
            }

            try
            {
                // 使用事务确保所有操作一致性
                var result = await Db.Ado.UseTranAsync(async () =>
                {
                    // 获取要处理的分类
                    var category = await Db.Queryable<products_category>()
                        .Where(c => c.CateId == request.Id)
                        .FirstAsync();

                    if (category == null)
                    {
                        return false;
                    }

                    // 查找所有子分类
                    var allDescendants = new HashSet<int>();
                    var allCategories = await Db.Queryable<products_category>().ToListAsync();
                    FindAllDescendants(allCategories, request.Id, allDescendants, new HashSet<int>());

                    // 更新当前分类的IsSoldOut状态
                    category.IsSoldOut = request.SoldOut;
                    category.EditTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                    await Db.Updateable(category).ExecuteCommandAsync();

                    // 处理子分类
                    if (allDescendants.Any())
                    {
                        await Db.Updateable<products_category>()
                            .SetColumns(c => new products_category
                            {
                                IsSoldOut = request.SoldOut,
                                EditTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
                            })
                            .Where(c => allDescendants.Contains(c.CateId))
                            .ExecuteCommandAsync();
                    }

                    // 处理关联产品
                    if (request.SoldProduct)
                    {
                        var categoryIds = new List<int> { request.Id };
                        categoryIds.AddRange(allDescendants);

                        // 更新仅属于这些分类的产品
                        var relatedProIds = await Db.Queryable<products_category_relate>()
                            .Where(r => categoryIds.Contains(r.CateId))
                            .Select(r => r.ProId)
                            .ToListAsync();

                        if (relatedProIds.Any())
                        {
                            await Db.Updateable<products>()
                                .SetColumns(p => new products
                                {
                                    SoldOut = request.SoldOut,
                                    EditTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
                                })
                                .Where(p => relatedProIds.Contains(p.ProId))
                                .ExecuteCommandAsync();
                        }
                    }

                    // 处理导航
                    if (request.SoldNav)
                    {
                    }

                    // 清除产品分类相关的所有Hash缓存
                    await ClearProductCategoryCache();

                    return true;
                });

                return result.Data;
            }
            catch (Exception ex)
            {
                // 记录异常
                _logger.LogError(ex, "BatchSoldOutCategory error: {Message}", ex.Message);
                return false;
            }
        }


        #region 获取某个分类下的所有子分类

        public async Task<List<products_category>> GetAllChildProductCategories(List<int> cateIds)
        {
            try
            {
                string hashKey = Consts.KEY_ProductCateList;
                string field = "all_categories";

                // 从Hash缓存中获取所有分类数据
                var allCategories = await SafeHashGetAsync<List<products_category>>(hashKey, field);

                if (allCategories == null)
                {
                    // 如果缓存中没有数据，则从数据库中加载所有分类数据
                    var queryable = Db.Queryable<products_category>();

                    // 始终按排序值排序
                    queryable = queryable.OrderBy(c => c.MyOrder);

                    // 查询所有分类并按排序值排序
                    allCategories = await queryable.ToListAsync();

                    // 存储到Hash缓存中
                    _caching.HashSet(hashKey, field, allCategories);
                }

                // 创建结果集合
                var result = new List<products_category>();
                // 创建待处理分类ID队列
                var queue = new Queue<int>(cateIds);
                // 记录已处理分类ID（避免重复）
                var processedIds = new HashSet<int>(cateIds);

                // 广度优先搜索（BFS）获取所有子分类
                while (queue.Count > 0)
                {
                    int currentId = queue.Dequeue();

                    // 查找直接子分类：UId以当前分类路径结尾
                    var children = allCategories
                        .Where(c => c.UId?.EndsWith($"{currentId},") == true)
                        .ToList();

                    foreach (var child in children)
                    {
                        // 跳过已处理分类
                        if (processedIds.Contains(child.CateId)) continue;

                        result.Add(child);
                        processedIds.Add(child.CateId);
                        queue.Enqueue(child.CateId);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                // 捕获异常并记录
                _logger.LogError(ex, "GetAllChildProductCategories error: {Message}", ex.Message);
                return new List<products_category>();
            }
        }

        #endregion

        #region 获取所有分类

        public async Task<List<products_category>> GetAllProductCategories()
        {
            try
            {
                string hashKey = Consts.KEY_ProductCateList;
                string field = "all_categories";

                // 从Hash缓存中获取所有分类数据
                var allCategories = await SafeHashGetAsync<List<products_category>>(hashKey, field);

                if (allCategories == null)
                {
                    // 如果缓存中没有数据，则从数据库中加载所有分类数据
                    var queryable = Db.Queryable<products_category>();

                    // 始终按排序值排序
                    queryable = queryable.OrderBy(c => c.MyOrder);

                    // 查询所有分类并按排序值排序
                    allCategories = await queryable.ToListAsync();

                    // 存储到Hash缓存中
                    _caching.HashSet(hashKey, field, allCategories);
                }

                return allCategories ?? new List<products_category>();
            }
            catch (Exception ex)
            {
                // 捕获异常并记录
                _logger.LogError(ex, "GetAllProductCategories error: {Message}", ex.Message);
                return new List<products_category>();
            }
        }

        #endregion

        #region 产品分类关联管理

        /// <summary>
        /// 保存产品分类关联
        /// </summary>
        /// <param name="cateId">分类ID</param>
        /// <param name="productIds">产品ID列表</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> SaveProductCategoryRelations(int cateId, List<int> productIds)
        {
            try
            {
                var result = await Db.Ado.UseTranAsync(async () =>
                {
                    // 1. 先删除该分类下的所有产品关联
                    await Db.Deleteable<products_category_relate>()
                        .Where(r => r.CateId == cateId)
                        .ExecuteCommandAsync();

                    // 2. 如果有新的产品ID列表，则添加新的关联
                    if (productIds != null && productIds.Any())
                    {
                        // 获取当前分类信息，判断是否为主分类
                        var category = await Db.Queryable<products_category>()
                            .Where(c => c.CateId == cateId)
                            .FirstAsync();

                        // 判断是否为主分类：UId为"0,"的就是主分类
                        bool isMainCategory = category != null && category.UId == "0,";

                        // 准备新的关联数据
                        var newRelations = productIds.Select(productId => new products_category_relate
                        {
                            ProId = productId,
                            CateId = cateId,
                            IsMain = isMainCategory // 根据分类的UId判断是否为主分类
                        }).ToList();

                        // 批量插入新关联
                        await Db.Insertable(newRelations).ExecuteCommandAsync();
                    }

                    return true;
                });

                return result.IsSuccess && result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存产品分类关联时出错: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 获取分类下的所有产品ID
        /// </summary>
        /// <param name="cateId">分类ID</param>
        /// <returns>产品ID列表</returns>
        public async Task<List<int>> GetProductIdsByCategoryId(int cateId)
        {
            try
            {
                return await Db.Queryable<products_category_relate>()
                    .Where(r => r.CateId == cateId)
                    .Select(r => r.ProId)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类产品ID列表时出错: {Message}", ex.Message);
                return new List<int>();
            }
        }

        /// <summary>
        /// 清除分类下的所有产品关联
        /// </summary>
        /// <param name="cateId">分类ID</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> ClearCategoryProductRelations(int cateId)
        {
            try
            {
                await Db.Deleteable<products_category_relate>()
                    .Where(r => r.CateId == cateId)
                    .ExecuteCommandAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除分类产品关联时出错: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 产品转移：将一个分类下的所有产品转移到另一个分类
        /// </summary>
        /// <param name="request">产品转移请求参数</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> TransferProducts(ProductTransferRequest request)
        {
            try
            {
                // 参数验证
                if (request.CateId <= 0 || request.TransferCateId < 0)
                {
                    return false;
                }

                // 如果目标分类和当前分类相同，直接返回成功
                if (request.CateId == request.TransferCateId)
                {
                    return true;
                }

                var result = await Db.Ado.UseTranAsync(async () =>
                {
                    // 1. 获取当前分类下的所有产品ID
                    var productIds = await Db.Queryable<products_category_relate>()
                        .Where(r => r.CateId == request.CateId)
                        .Select(r => r.ProId)
                        .ToListAsync();

                    if (!productIds.Any())
                    {
                        // 如果当前分类下没有产品，直接返回成功
                        return true;
                    }

                    // 2. 删除当前分类下的所有产品关联
                    await Db.Deleteable<products_category_relate>()
                        .Where(r => r.CateId == request.CateId)
                        .ExecuteCommandAsync();

                    // 3. 如果目标分类ID为-1，表示转移到"未分类"，不需要添加新的关联
                    if (request.TransferCateId == -1)
                    {
                        return true;
                    }

                    // 4. 获取目标分类信息，判断是否为主分类
                    var targetCategory = await Db.Queryable<products_category>()
                        .Where(c => c.CateId == request.TransferCateId)
                        .FirstAsync();

                    if (targetCategory == null)
                    {
                        throw new Exception($"目标分类不存在，分类ID: {request.TransferCateId}");
                    }

                    // 判断是否为主分类：UId为"0,"的就是主分类
                    bool isMainCategory = targetCategory.UId == "0,";

                    // 5. 将产品添加到目标分类
                    var newRelations = productIds.Select(productId => new products_category_relate
                    {
                        ProId = productId,
                        CateId = request.TransferCateId,
                        IsMain = isMainCategory
                    }).ToList();

                    await Db.Insertable(newRelations).ExecuteCommandAsync();

                    return true;
                });

                return result.IsSuccess && result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "产品转移时出错: {Message}", ex.Message);
                return false;
            }
        }

        #endregion

        /// <summary>
        /// 安全地从Hash缓存中获取数据，处理WRONGTYPE错误
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="hashKey">Hash键</param>
        /// <param name="field">字段名</param>
        /// <returns>缓存数据或null</returns>
        private async Task<T> SafeHashGetAsync<T>(string hashKey, string field) where T : class
        {
            try
            {
                return _caching.HashGet<T>(hashKey, field);
            }
            catch (Exception ex) when (ex.Message.Contains("WRONGTYPE"))
            {
                // 如果是类型错误，说明存在旧的非Hash缓存，先清除
                _logger.LogWarning("检测到旧的缓存类型，正在清除: {Key}", hashKey);
                await _caching.DelByPatternAsync(hashKey);
                return null;
            }
        }

        /// <summary>
        /// 获取同级分类中最大的排序值
        /// </summary>
        /// <param name="uid">分类的UId，用于确定同级分类</param>
        /// <returns>最大排序值</returns>
        private async Task<int> GetMaxOrderForSameLevel(string uid)
        {
            try
            {
                // 构建查询条件：查找同级分类
                var query = Db.Queryable<products_category>();

                if (string.IsNullOrEmpty(uid) || uid == "0," || uid == "0")
                {
                    // 一级分类：UId为空、"0,"或"0"
                    query = query.Where(c => string.IsNullOrEmpty(c.UId) || c.UId == "0," || c.UId == "0");
                }
                else
                {
                    // 子分类：UId相同的为同级
                    query = query.Where(c => c.UId == uid);
                }

                // 获取最大排序值
                var maxOrder = await query.MaxAsync(c => c.MyOrder);

                // 如果没有找到任何分类或排序值为null，返回0
                return maxOrder ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取同级分类最大排序值时出错: UId={UId}", uid);
                return 0;
            }
        }

        /// <summary>
        /// 清除产品分类相关的所有Hash缓存
        /// </summary>
        private async Task ClearProductCategoryCache()
        {
            try
            {
                string hashKey = Consts.KEY_ProductCateList;

                // 检查缓存键是否存在
                if (await _caching.ExistsAsync(hashKey))
                {
                    try
                    {
                        // 尝试获取Hash中的所有字段名（不反序列化值）
                        var hashFields = _caching.HashGetKeys(hashKey);
                        if (hashFields != null && hashFields.Any())
                        {
                            // 删除Hash中的所有字段
                            _caching.HashDel(hashKey, hashFields);
                            _logger.LogInformation("已清除产品分类Hash缓存字段: {Count}个字段", hashFields.Count);
                        }
                        else
                        {
                            // 如果Hash为空，直接删除整个Hash键
                            await _caching.DelByPatternAsync(hashKey);
                            _logger.LogInformation("已清除产品分类Hash缓存: {Key}", hashKey);
                        }
                    }
                    catch (Exception ex) when (ex.Message.Contains("WRONGTYPE"))
                    {
                        // 如果是类型错误，说明存在旧的非Hash缓存，直接删除
                        _logger.LogWarning("清除缓存时检测到旧的缓存类型，直接删除: {Key}", hashKey);
                        await _caching.DelByPatternAsync(hashKey);
                    }
                    catch (Exception ex) when (ex.Message.Contains("JsonReaderException") ||
                                               ex.Message.Contains("Unexpected character"))
                    {
                        // 如果是JSON反序列化错误，直接删除整个Hash键
                        _logger.LogWarning("清除缓存时检测到JSON反序列化错误，直接删除: {Key}", hashKey);
                        await _caching.DelByPatternAsync(hashKey);
                    }
                }

                // 清除传统的键值对缓存（兼容性处理）
                await _caching.DelByPatternAsync($"{Consts.KEY_ProductCateList}_*");
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，避免影响主要业务逻辑
                _logger.LogError(ex, "清除产品分类Hash缓存时出错: {Message}", ex.Message);
            }
        }
    }
}