using Microsoft.Extensions.Logging;
using SqlSugar;
using YseStore.Common.Cache;
using YseStore.IService.Products;
using YseStore.Model.Response.Products;
using Entitys;
using YseStore.Common.Helper;
using YseStore.IService.Sales;
using YseStore.IService;

namespace YseStore.Service.Products
{
    /// <summary>
    /// 首页产品服务实现
    /// </summary>
    public class HomeProductService : IHomeProductService
    {
        private readonly ILogger<HomeProductService> _logger;
        private readonly ISqlSugarClient _db;
        private readonly ICaching _caching;
        private readonly IProductSeoService _productSeoService;
        private readonly IFlashSaleService _flashSaleService;
        private readonly ICurrencyService _currencyService;

        /// <summary>
        /// 首页产品数据缓存Key
        /// </summary>
        public const string KEY_HomeProducts = "HomeProducts";

        public HomeProductService(ILogger<HomeProductService> logger, ISqlSugarClient db, ICaching caching,
            IProductSeoService productSeoService, IFlashSaleService flashSaleService, ICurrencyService currencyService)
        {
            _logger = logger;
            _db = db;
            _caching = caching;
            _productSeoService = productSeoService;
            _flashSaleService = flashSaleService;
            _currencyService = currencyService;
        }

        /// <summary>
        /// 获取首页产品数据
        /// </summary>
        /// <param name="productUrls">产品URL列表</param>
        /// <param name="currentCurrency">当前用户币种</param>
        /// <param name="userId">当前用户ID</param>
        /// <returns>首页产品数据</returns>
        public async Task<HomeProductsResponse> GetHomeProductsAsync(List<string> productUrls,
            string currentCurrency = "", int userId = 0)
        {
            var response = new HomeProductsResponse();

            try
            {
                if (productUrls == null || !productUrls.Any())
                {
                    return response;
                }

                // // 先尝试从缓存获取
                // var cacheKey = string.Join(",", productUrls.OrderBy(x => x));
                // var cachedData = _caching.HashGet<HomeProductsResponse>(KEY_HomeProducts, cacheKey);
                // if (cachedData != null)
                // {
                //     _logger.LogInformation("从缓存获取首页产品数据");
                //     return cachedData;
                // }

                // 根据URL获取产品ID
                var productIds = new List<int>();
                foreach (var url in productUrls)
                {
                    var productId = await _productSeoService.GetProductIdByUrlAsync(url);
                    if (productId > 0)
                    {
                        productIds.Add(productId);
                    }
                }

                if (!productIds.Any())
                {
                    return response;
                }

                // 获取产品基本信息
                var products = await _db.Queryable<products>()
                    .Where(p => productIds.Contains(p.ProId))
                    .ToListAsync();

                // 获取产品图片
                var productImages = await _db.Queryable<products_images>()
                    .Where(img => productIds.Contains(img.ProId))
                    .OrderBy(img => img.Position)
                    .ToListAsync();

                // 获取用户收藏状态（如果用户已登录）
                var userFavorites = new HashSet<int>();
                if (userId > 0)
                {
                    var favoriteProductIds = await _db.Queryable<user_favorite>()
                        .Where(f => f.UserId == userId && productIds.Contains(f.ProId.Value))
                        .Select(f => f.ProId.Value)
                        .ToListAsync();
                    userFavorites = favoriteProductIds.ToHashSet();
                }

                // 构建响应数据 - 每个区域12个产品，严格按照URL位置分配
                var allProductItems = new List<HomeProductItem>();
                var missingUrls = new List<string>();

                // 按照URL顺序处理每个产品，保持位置对应关系
                for (int i = 0; i < productUrls.Count; i++)
                {
                    var url = productUrls[i];
                    var product = products.FirstOrDefault(p => p.PageUrl == url);

                    if (product != null)
                    {
                        var productImages_single = productImages.Where(img => img.ProId == product.ProId).ToList();
                        
                        var priceInfo = await CalculatePriceWithPromotion(product, currentCurrency);

                        var item = new HomeProductItem
                        {
                            ProductId = product.ProId,
                            ProductName = product.Name_en,
                            ProductUrl = $"/products/{product.PageUrl}",
                            PrimaryImage = productImages_single.FirstOrDefault(img => img.Position == 1)?.PicPath ??
                                           product.PicPath_0,
                            HoverImage = productImages_single.FirstOrDefault(img => img.Position == 2)?.PicPath,
                            Price = priceInfo.Price,
                            OldPrice = priceInfo.OldPrice,
                            PriceFormat = priceInfo.PriceFormat,
                            OldPriceFormat = priceInfo.OldPriceFormat,
                            SKU = product.SKU,
                            IsHot = IsHotProduct(product),
                            IsNew = IsNewProduct(product),
                            Rating = product.Rating ?? 0,
                            IsFavorited = userFavorites.Contains(product.ProId)
                        };
                        allProductItems.Add(item);
                    }
                    else
                    {
                        // 记录未找到的产品URL及其位置信息
                        missingUrls.Add(url);
                        // _logger.LogWarning($"位置 {i + 1}: 未找到产品URL: {url}");
                    }
                }

                // 严格按照位置分配到三个区域，每个区域12个产品
                // 只取找到的产品，但要确保每个区域的产品数量正确
                var tourGuideProducts = new List<HomeProductItem>();
                var pagingSystemProducts = new List<HomeProductItem>();
                var callSystemProducts = new List<HomeProductItem>();

                // 按照原始URL位置分配产品
                for (int i = 0; i < productUrls.Count; i++)
                {
                    var url = productUrls[i];
                    var productItem = allProductItems.FirstOrDefault(p => p.ProductUrl == $"/products/{url}");

                    if (productItem != null)
                    {
                        if (i < 12)
                        {
                            tourGuideProducts.Add(productItem);
                        }
                        else if (i < 24)
                        {
                            pagingSystemProducts.Add(productItem);
                        }
                        else if (i < 36)
                        {
                            callSystemProducts.Add(productItem);
                        }
                    }
                }

                response.TourGuideProducts = tourGuideProducts;
                response.PagingSystemProducts = pagingSystemProducts;
                response.CallSystemProducts = callSystemProducts;


                // 按模块分类显示未找到的产品URL
                if (missingUrls.Any())
                {
                    // _logger.LogWarning($"总共未找到的产品URL ({missingUrls.Count}个):");

                    // 按模块分类显示未找到的产品 - 根据URL在原始列表中的位置
                    var tourGuideMissing = new List<string>();
                    var pagingSystemMissing = new List<string>();
                    var callSystemMissing = new List<string>();

                    for (int i = 0; i < productUrls.Count; i++)
                    {
                        var url = productUrls[i];
                        if (missingUrls.Contains(url))
                        {
                            if (i < 12)
                            {
                                tourGuideMissing.Add(url);
                            }
                            else if (i < 24)
                            {
                                pagingSystemMissing.Add(url);
                            }
                            else if (i < 36)
                            {
                                callSystemMissing.Add(url);
                            }
                        }
                    }

                    if (tourGuideMissing.Any())
                    {
                        // _logger.LogWarning($"导游系统模块 - 未找到的产品URL ({tourGuideMissing.Count}个):");
                        foreach (var url in tourGuideMissing)
                        {
                            // _logger.LogWarning($"  - {url}");
                        }
                    }

                    if (pagingSystemMissing.Any())
                    {
                        // _logger.LogWarning($"寻呼系统模块 - 未找到的产品URL ({pagingSystemMissing.Count}个):");
                        foreach (var url in pagingSystemMissing)
                        {
                            _logger.LogWarning($"  - {url}");
                        }
                    }

                    if (callSystemMissing.Any())
                    {
                        // _logger.LogWarning($"呼叫系统模块 - 未找到的产品URL ({callSystemMissing.Count}个):");
                        foreach (var url in callSystemMissing)
                        {
                            // _logger.LogWarning($"  - {url}");
                        }
                    }
                }

                // 输出每个模块实际获取到的产品数量
                // _logger.LogInformation($"导游系统模块: 获取到 {response.TourGuideProducts.Count} 个产品");
                // _logger.LogInformation($"寻呼系统模块: 获取到 {response.PagingSystemProducts.Count} 个产品");
                // _logger.LogInformation($"呼叫系统模块: 获取到 {response.CallSystemProducts.Count} 个产品");

                // 缓存结果
                // _caching.HashSet(KEY_HomeProducts, cacheKey, response);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取首页产品数据时出错");
                return response;
            }
        }

        /// <summary>
        /// 构建产品列表（已弃用，现在在主方法中直接处理）
        /// </summary>
        private List<HomeProductItem> BuildProductList(List<products> allProducts, List<products_images> allImages,
            List<products_selected_attribute_combination> allVariants, List<string> urls)
        {
            // 这个方法现在不再使用，保留是为了向后兼容
            return new List<HomeProductItem>();
        }

        /// <summary>
        /// 计算产品价格
        /// </summary>
        private decimal CalculatePrice(products product, List<products_selected_attribute_combination> variants)
        {
            try
            {
                // 如果有变体，取最低价格
                if (variants.Any())
                {
                    var minPrice = variants.Where(v => v.Price > 0).Min(v => v.Price);
                    return minPrice ?? product.Price_1 ?? 0;
                }

                return product.Price_1 ?? 0;
            }
            catch
            {
                return product.Price_1 ?? 0;
            }
        }

        /// <summary>
        /// 计算产品原价
        /// </summary>
        private decimal? CalculateOldPrice(products product, List<products_selected_attribute_combination> variants)
        {
            try
            {
                var currentPrice = CalculatePrice(product, variants);
                var originalPrice = product.Price_0;

                if (originalPrice.HasValue && originalPrice > currentPrice)
                {
                    return originalPrice;
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 计算包含促销的价格信息 - 完全按照ProductFrontService.cs第1218行逻辑实现
        /// </summary>
        private async Task<(decimal Price, decimal? OldPrice, string PriceFormat, string OldPriceFormat)>
            CalculatePriceWithPromotion(
                products product, string currentCurrency = "")
        {
            try
            {
                // 获取币种信息 - 完全按照ProductFrontService.cs第1218行逻辑
                var userCurrency = await _currencyService.GetCurrency(currentCurrency);
                var manageCurrency = await _currencyService.GetManageDefaultCurrency();

                // 处理价格信息
                decimal promotionPrice = 0;
                string priceFormat = null;
                string originalPriceFormat = null;
                string promotionPriceFormat = null;

                // 1. 获取促销价格信息
                try
                {
                    var flashSaleResult = await _flashSaleService.FlashSaleCalculation(
                        product,
                        product.Price_1.HasValue ? product.Price_1.Value : 0,
                        product.Price_0.HasValue ? product.Price_0.Value : 0,
                        "", // variantsId
                        0 // userId
                    );

                    if (flashSaleResult != null && flashSaleResult.IsUsed == 1 && flashSaleResult.Price > 0)
                    {
                        promotionPrice = flashSaleResult.Price;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取产品{product.ProId}促销价格失败: {ex.Message}");
                    // 出错时促销价格保持为0
                    // promotionPrice = 0;
                }

                // 2. 格式化促销价格（只有当促销价格大于0时才显示）
                if (promotionPrice > 0)
                {
                    var promotionPriceResult =
                        _currencyService.ShowPriceFormat(promotionPrice, userCurrency, manageCurrency);
                    promotionPriceFormat = promotionPriceResult.Item2;
                }

                // 3. 正确的价格显示逻辑：原价始终不变，促销价作用在售价上
                decimal finalPrice;
                decimal? oldPrice = null;

                if (promotionPrice > 0)
                {
                    // 有促销价时：现价位置显示促销价，原价保持不变
                    priceFormat = promotionPriceFormat; // 现价位置显示促销价
                    finalPrice = promotionPrice;

                    // 原价位置显示真正的原价（Price_0）
                    if (product.Price_0.HasValue && product.Price_0.Value > 0)
                    {
                        var originalPriceResult =
                            _currencyService.ShowPriceFormat(product.Price_0.Value, userCurrency, manageCurrency);
                        originalPriceFormat = originalPriceResult.Item2;
                        oldPrice = product.Price_0.Value;
                    }
                }
                else
                {
                    // 没有促销价时：正常显示现价和原价
                    // 格式化销售价格
                    if (product.Price_1.HasValue)
                    {
                        var priceResult =
                            _currencyService.ShowPriceFormat(product.Price_1.Value, userCurrency, manageCurrency);
                        priceFormat = priceResult.Item2;
                        finalPrice = product.Price_1.Value;
                    }
                    else
                    {
                        finalPrice = 0;
                    }

                    // 如果原价大于售价，显示原价作为划线价
                    if (product.Price_0.HasValue && product.Price_0.Value > 0 &&
                        product.Price_1.HasValue && product.Price_0.Value > product.Price_1.Value)
                    {
                        var originalPriceResult =
                            _currencyService.ShowPriceFormat(product.Price_0.Value, userCurrency, manageCurrency);
                        originalPriceFormat = originalPriceResult.Item2;
                        oldPrice = product.Price_0.Value;
                    }
                }

                return (finalPrice, oldPrice, priceFormat ?? "", originalPriceFormat ?? "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"计算产品{product.ProId}价格时出错");

                // 出错时返回基础价格
                var basicPrice = product.Price_1.HasValue ? product.Price_1.Value : 0;
                return (basicPrice, null, FormatPrice(basicPrice), "");
            }
        }

        /// <summary>
        /// 格式化价格
        /// </summary>
        private string FormatPrice(decimal? price)
        {
            if (!price.HasValue || price <= 0)
            {
                return "";
            }

            return $"EUR{price:F2}";
        }

        /// <summary>
        /// 判断是否为热门产品
        /// </summary>
        private bool IsHotProduct(products product)
        {
            // 可以根据销量、评分等判断
            return product.Rating >= 4.5m || product.Tags?.Contains("Hot") == true;
        }

        /// <summary>
        /// 判断是否为新产品
        /// </summary>
        private bool IsNewProduct(products product)
        {
            // 判断产品是否在30天内创建
            var thirtyDaysAgo = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now.AddDays(-30));
            return product.AccTime >= thirtyDaysAgo || product.Tags?.Contains("New") == true;
        }

        /// <summary>
        /// 清除首页产品缓存
        /// </summary>
        public void ClearHomeProductsCache()
        {
            try
            {
                // 获取Hash中的所有键，然后批量删除
                var allHashKeys = _caching.HashGetKeys(KEY_HomeProducts);
                if (allHashKeys != null && allHashKeys.Any())
                {
                    _caching.HashDel(KEY_HomeProducts, allHashKeys);
                    _logger.LogInformation($"清除首页产品缓存完成，删除了{allHashKeys.Count}个缓存项");
                }
                else
                {
                    // 如果Hash为空或不存在，尝试直接删除整个键
                    _caching.DelByPattern(KEY_HomeProducts);
                    _logger.LogInformation("清除首页产品缓存完成");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除首页产品缓存失败");
            }
        }
    }
}