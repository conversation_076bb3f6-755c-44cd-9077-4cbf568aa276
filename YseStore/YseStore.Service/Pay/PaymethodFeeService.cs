using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.Common.Const;
using YseStore.IService.Pay;
using YseStore.Model.Dto;

namespace YseStore.Service.Pay
{
    /// <summary>
    /// 支付方式费用服务实现类
    /// </summary>
    public class PaymethodFeeService : BaseServices<paymethod_fee>, IPaymethodFeeService
    {

        private readonly ILogger<PaymethodFeeService> _logger;
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;

        public PaymethodFeeService(ILogger<PaymethodFeeService> logger, ISqlSugarClient db, ICaching caching)
        {
            _logger = logger;
            this.db = db;
            _caching = caching;
        }




        /// <summary>
        /// 获取启用的支付方式列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<paymethod_fee>> GetPaymentFeeCache()
        {

            // 从缓存中获取启用的支付方式列表
            var cacheKey = GlobalConstVars.DBPaymethodFee;

            var usedPayments = await _caching.GetAsync<List<paymethod_fee>>(cacheKey);
            if (usedPayments == null)
            {
                // 如果缓存中没有数据，则从数据库查询
                usedPayments = await db.Queryable<paymethod_fee>()
                    .ToListAsync();
                // 将查询结果存入缓存
                await _caching.SetAsync(cacheKey, usedPayments); // 缓存
            }
            return usedPayments;

        }

        /// <summary>
        /// 获取指定类型的支付方式费用
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public async Task<List<paymethod_fee>> GetPaymentFeeCache(string type)
        {
            var payment = await GetPaymentFeeCache();
            if (payment == null || !payment.Any())
            {
                return null;
            }

            var usedPayment = payment.Where(p => p.Type == type).ToList();

            return usedPayment;


        }


    }
}
