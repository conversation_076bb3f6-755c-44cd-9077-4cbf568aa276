using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.Common.Const;
using YseStore.IService;
using YseStore.Repo;

namespace YseStore.Service
{
    public class ConfigService : BaseServices<config>, IConfigService
    {
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;

        public ConfigService(ISqlSugarClient db, ICaching caching)
        {
            this.db = db;
            _caching = caching;
        }
        /// <summary>
        /// 基础配置--协议显示位置
        /// </summary>
        /// <returns></returns>
        public async Task<config> GetConfigAry()
        {
            var result = await db.Queryable<config>()
                .Where(c => c.GroupId == "global" && c.Variable == "policies")
                .FirstAsync();
            return result;
        }
        /// <summary>
        /// 更改基础配置--协议显示位置
        /// </summary>
        /// <param name="IsBottom"></param>
        public async Task<bool> UpdateConfig(string IsBottom)
        {
            var result = await db.Queryable<config>()
                .Where(c => c.CId == 151 && c.GroupId == "global" && c.Variable == "policies")
                .FirstAsync();

            result.Value = "{\"Cookies\":{\"position\":\"" + IsBottom + "\"}}";
            return await db.Updateable(result).ExecuteCommandHasChangeAsync();

        }

        #region 获取基础配置

        /// <summary>
        /// 获取所有的配置
        /// </summary>
        /// <returns></returns>
        public async Task<List<config>> GetConfig()
        {
            //从缓存中获取国家列表
            var cacheKey = GlobalConstVars.config_all_key;
            var configList = await _caching.GetAsync<List<config>>(cacheKey);
            if (configList == null)
            {
                // 如果缓存中没有数据，则从数据库查询
                configList = await db.Queryable<config>()
                    .OrderBy(c => c.GroupId, OrderByType.Asc) // 按 Country 升序排列
                    .ToListAsync();
                // 将查询结果存入缓存
                await _caching.SetAsync(cacheKey, configList); // 
            }
            return configList;
        }

        /// <summary>
        /// 获取单条
        /// </summary>
        /// <param name="groupId"></param>
        /// <param name="variable"></param>
        /// <returns></returns>
        public async Task<config> GetConfigByGroup(string groupId, string variable)
        {
            //获取hashkey
            string hashKey = GlobalConstVars.config_hashkey.FormatWith(groupId, variable);

            var value = _caching.HashGet(GlobalConstVars.config_key, hashKey);
            if (!value.IsNullOrEmpty())
            {
                config r = new config
                {
                    GroupId = groupId,
                    Variable = variable,
                    Value = value
                };
                return r;
            }

            var result = await db.Queryable<config>()
                   .Where(c => c.GroupId == groupId && c.Variable == variable)
                   .FirstAsync();
            if (result != null)
            {
                //设置缓存
                _caching.HashSet(GlobalConstVars.config_key, hashKey, result.Value);
                await _caching.DelByPatternAsync(GlobalConstVars.config_all_key);
            }
            else
            {
                return null;
            }
            return result;
        }


        /// <summary>
        /// 获取基础配置
        /// </summary>
        /// <param name="groupId"></param>
        /// <param name="variable"></param>
        /// <returns></returns>
        public async Task<string> GetConfigValueByGroup(string groupId, string variable)
        {
            string value = "";
            var config = await GetConfigByGroup(groupId, variable);
            if (config != null)
            {
                value = config.Value;
            }
            return value;

        }
        #endregion


        /// <summary>
        /// 保存设置
        /// </summary>
        /// <param name="groupId"></param>
        /// <param name="variable"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public async Task<bool> SetConfig(string groupId, string variable, string value)
        {
            //获取hashkey
            string hashKey = GlobalConstVars.config_hashkey.FormatWith(groupId, variable);

            config conf = await QueryByClauseAsync(it => it.GroupId == groupId && it.Variable == variable);
            if (conf == null)
            {
                conf = new config
                {
                    GroupId = groupId,
                    Variable = variable,
                    Value = value
                };

                //
                var inst = await AddWithIntId(conf);

                //设置缓存

                _caching.HashSet(GlobalConstVars.config_key, hashKey, value);
                await _caching.DelByPatternAsync(GlobalConstVars.config_all_key);

                return inst > 0;
            }
            else
            {
                conf.Value = value;
                var updt = await Update(conf);

                //设置缓存
                _caching.HashSet(GlobalConstVars.config_key, hashKey, value);
                await _caching.DelByPatternAsync(GlobalConstVars.config_all_key);

                return updt;
            }

        }
    }
}
