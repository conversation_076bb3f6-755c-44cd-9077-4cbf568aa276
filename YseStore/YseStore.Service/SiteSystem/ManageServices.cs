using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService;
using YseStore.IService.manage;
using YseStore.IService.SiteSystem;
using YseStore.Repo;
using Microsoft.Extensions.Primitives;
using Aop.Api.Domain;
using Entitys.Shop_V3;

namespace YseStore.Service.SiteSystem
{


    public class ManageServices : BaseServices<Entitys.manage>, IManageServices
    {

        private readonly ILogger<ManageServices> _logger;
        private readonly ICaching _cacheService;
        private readonly IUserService _userService;
        private readonly ICommonService _commonService;
        private readonly ISqlSugarClient db;
        private readonly IManageOperationLogService _manageOperationLogService;
        private readonly SqlSugarScope _sqlSugarClient;

        /// <summary>
        /// 构造函数，通过依赖注入获取数据仓储
        /// </summary>
        /// <param name="baseDal">UrlService</param>
        public ManageServices(IBaseRepository<Entitys.manage> baseDal, ILogger<ManageServices> logger, ICaching cacheService, IUserService userService, ICommonService commonService, ISqlSugarClient db, IManageOperationLogService manageOperationLogService, ISqlSugarClient sqlSugarClient) : base(baseDal)
        {
            _logger = logger;
            this._cacheService = cacheService;
            this._userService = userService;
            _commonService = commonService;
            this.db = db;
            _manageOperationLogService = manageOperationLogService;

            _sqlSugarClient = sqlSugarClient as SqlSugarScope;
        }
        /// <summary>
        /// 管理员修改密码
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="psd"></param>
        /// <param name="cfpsd"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<(bool,string)> ManagerChangePassword(int? userId, StringValues psd, StringValues cfpsd)
        {
            if(psd!= cfpsd)
            {
                var msg="两次输入的密码不一致";
                return (false, msg);
            }
            var pass = _userService.Password(psd);
            var account = await QueryByClauseAsync(it => it.UserId == userId);
            if (account == null)
            {
                var msg = "账号不存在";
                return (false, msg);
            }
            if (account.Password==pass)
            {
                var msg = "新密码不能与旧密码相同";
                return (false, msg);
            }
            var status=await UpdateAsync(it => new Entitys.manage { Password = pass }, it => it.UserId == userId);
            return (status, "密码修改成功");
        }


        /// <summary>
        /// 管理员登录
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="password"></param>
        /// <param name="ip"></param>
        /// <returns></returns>
        public async Task<WebApiCallBack> ManagerLogin(string userName, string password, string ip, string agent)
        {
            //_sqlSugarClient.ChangeDatabase("exchange");
            //var v3userList = db.Queryable<sp_user>().ToList();
            //_sqlSugarClient.ChangeDatabase("main");

            //登录方法
            WebApiCallBack jm = new WebApiCallBack();

            //生成密码
            var pass = _userService.Password(password);

            //获取账号
            var account = await QueryByClauseAsync(it => it.UserName == userName && it.Password == pass);
            if (account == null)
            {
                jm.status = false;
                jm.msg = "账号或密码错误";
                return jm;
            }

            if (account.Locked == 1)
            {
                jm.status = false;
                jm.msg = "账号被锁定";
                return jm;
            }
            string ipCountry = _commonService.GetAliyunCountryCode(ip);

            account.LastLoginIp = $"{ip}|{ipCountry}";
            account.LastLoginTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);

            //更新
            var update = await Update(account);
            if (update)
            {
                //添加操作日志
                manage_operation_log log = new manage_operation_log
                {
                    UserId = account.UserId,
                    UserName = account.UserName,
                    Module = "account",
                    Action = "login",
                    Ip = $"{ip}|{ipCountry}",
                    Terminal = agent,
                    Log = $"{userName}登录，IP：{ip}，国家：{ipCountry}",
                    AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    LogDesc = userName,

                };

                await _manageOperationLogService.AddWithIntId(log);


            }
            jm.data = account.UserId;
            jm.status = true;
            jm.msg = "登录成功";
            return jm;

        }



    }
}
