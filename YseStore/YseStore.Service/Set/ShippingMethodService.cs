using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using YseStore.IService.Order;
using YseStore.IService.Set;
using YseStore.Model.RequestModels.Set;
using YseStore.Model.Response.Set;
using YseStore.Repo;

namespace YseStore.Service.Set
{
    /// <summary>
    /// 运费方法查询服务实现 -  不启用运费模板
    /// 复用OrderListService中的GetShippingMethod逻辑
    /// </summary>
    public class ShippingMethodService : BaseServices<shipping>, IShippingMethodService
    {
        private readonly ILogger<ShippingMethodService> _logger;
        private readonly ISqlSugarClient _db;
        private readonly IHelpsCartService _helpsCartService;
        private readonly IHelpsManageService _helpsManageService;
        private readonly IHelpsAsiaflyService _helpsAsiaflyService;
        private readonly ICurrencyService _currencyService;
        private readonly ICountryService _countryService;

        public ShippingMethodService(
            IBaseRepository<shipping> baseDal,
            ILogger<ShippingMethodService> logger,
            ISqlSugarClient db,
            IHelpsCartService helpsCartService,
            IHelpsManageService helpsManageService,
            IHelpsAsiaflyService helpsAsiaflyService,
            ICurrencyService currencyService,
            ICountryService countryService) : base(baseDal)
        {
            _logger = logger;
            _db = db;
            _helpsCartService = helpsCartService;
            _helpsManageService = helpsManageService;
            _helpsAsiaflyService = helpsAsiaflyService;
            _currencyService = currencyService;
            _countryService = countryService;
        }

        /// <summary>
        /// 查询运费方法 - 使用自定义查询逻辑后调用原有方法
        /// </summary>
        /// <param name="request">查询请求参数</param>
        /// <returns>运费方法查询结果</returns>
        public async Task<ShippingMethodResponse> GetShippingMethodsAsync(ShippingMethodRequest request)
        {
            try
            {
                _logger.LogInformation("开始处理运费方法查询请求: {@Request}", request);

                // 1. 参数验证
                if (request.CId <= 0)
                {
                    _logger.LogWarning("无效的国家ID: {CId}", request.CId);
                    return CreateEmptyResponse();
                }

                // 2. 检查国家是否启用
                var hasUsedCountry = await _db.Queryable<country>()
                    .Where(c => c.CId == request.CId && c.IsUsed == true)
                    .AnyAsync();

                if (!hasUsedCountry)
                {
                    _logger.LogWarning("国家未启用或不存在: {CId}", request.CId);
                    return CreateEmptyResponse();
                }

                // 3. 解析购物车ID
                var cartIdList = string.IsNullOrEmpty(request.OrderCId)
                    ? new List<int>()
                    : request.OrderCId.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(s => int.TryParse(s, out var id) ? id : 0)
                        .Where(id => id > 0)
                        .ToList();

                if (cartIdList.Count == 0)
                {
                    _logger.LogWarning("购物车ID列表为空: {OrderCid}", request.OrderCId);
                    return CreateEmptyResponse();
                }

                // 4. 自定义查询逻辑 - 根据购物车ID获取产品信息
                var productsInfo = await GetProductsInfoFromCart(cartIdList);
                if (productsInfo.Count == 0)
                {
                    _logger.LogWarning("未找到有效的产品信息");
                    return CreateEmptyResponse();
                }

                // 5. 获取产品详细数据
                var productIds = productsInfo.Select(p => Convert.ToInt32(((JObject)p)["ProId"])).Distinct().ToList();
                var productsDataAry = await GetProductsDataDictionary(productIds);

                // 6. 调用复用的GetShippingMethod方法
                var isShippingTemplate = false; // 默认不启用运费模板
                var freeShipping = false; // 默认不免运费，可以根据会员权益设置
                //获取运费方法
                var shippingMethodResult = GetShippingMethod(
                    request.CId,
                    request.StatesSId,
                    productsInfo,
                    productsDataAry,
                    isShippingTemplate,
                    freeShipping);

                // 7. 获取海外仓信息
                var overseasDict = await GetOverseasDictionary();

                // 8. 计算总重量
                var totalWeight = CalculateTotalWeight(productsInfo, productsDataAry);

                // 9. 构建响应
                return await BuildResponse(shippingMethodResult, overseasDict, totalWeight, isShippingTemplate, request.CurrentCurrency);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取运费方法时发生错误: {@Request}", request);
                return CreateEmptyResponse();
            }
        }

        #region 自定义查询逻辑

        /// <summary>
        /// 根据购物车ID获取产品信息
        /// </summary>
        private async Task<List<object>> GetProductsInfoFromCart(List<int> cartIdList)
        {
            var cartItems = await _db.Queryable<shopping_cart>()
                .Where(c => cartIdList.Contains(c.CId))
                .ToListAsync();

            var productsInfo = new List<object>();
            foreach (var cartItem in cartItems)
            {
                // 构建与OrderListService.GetShippingMethod方法兼容的产品信息格式
                var productInfo = new JObject
                {
                    ["ProId"] = cartItem.ProId ?? 0,
                    ["OvId"] = cartItem.OvId,
                    ["Weight"] = cartItem.Weight ?? 0m,
                    ["Volume"] = cartItem.Volume ?? 0m,
                    ["Qty"] = cartItem.Qty ?? 1,
                    ["Price"] = cartItem.Price ?? 0m,
                    ["PropertyPrice"] = cartItem.PropertyPrice ?? 0m,
                    ["CustomPrice"] = cartItem.CustomPrice,
                    ["Discount"] = cartItem.Discount ?? 100,
                    ["BuyType"] = 1, // 默认购买类型
                    ["IsFreeShipping"] = 0, // 默认不免运费，后续会从产品数据中获取
                    ["TId"] = 0, // 默认运费模板ID，后续会从产品数据中获取
                    ["IsCombination"] = false // 默认不是组合产品，后续会从产品数据中获取
                };

                productsInfo.Add(productInfo);
            }

            return productsInfo;
        }

        /// <summary>
        /// 获取产品数据字典
        /// </summary>
        private async Task<Dictionary<int, products>> GetProductsDataDictionary(List<int> productIds)
        {
            if (!productIds.Any())
            {
                return new Dictionary<int, products>();
            }

            var productsRow = await _db.Queryable<products>()
                .Where(p => productIds.Contains(p.ProId))
                .ToListAsync();

            return productsRow.ToDictionary(p => p.ProId);
        }

        /// <summary>
        /// 获取海外仓字典
        /// </summary>
        private async Task<Dictionary<string, string>> GetOverseasDictionary()
        {
            var warehouseList = await _db.Queryable<shipping_overseas>()
                .Where(w => w.OvId != -1) // 排除默认仓库
                .Select(w => new { OvId = (int)w.OvId, w.Name })
                .ToListAsync();

            return warehouseList.ToDictionary(
                w => w.OvId.ToString(),
                w => w.Name);
        }

        /// <summary>
        /// 计算总重量
        /// </summary>
        private decimal CalculateTotalWeight(List<object> productsInfo, Dictionary<int, products> productsDataAry)
        {
            decimal totalWeight = 0;
            foreach (var productInfoObj in productsInfo)
            {
                var productInfo = productInfoObj as JObject;
                if (productInfo != null)
                {
                    var qty = Convert.ToInt32(productInfo["Qty"]);
                    var weight = Convert.ToDecimal(productInfo["Weight"]);

                    totalWeight += weight * qty;
                }
            }

            return totalWeight;
        }

        #endregion

        #region 创建空响应

        /// <summary>
        /// 创建空响应
        /// </summary>
        private ShippingMethodResponse CreateEmptyResponse()
        {
            return new ShippingMethodResponse
            {
                ret = 0,
                msg = new ShippingMethodData
                {
                    Symbol = "$",
                    info = new Dictionary<string, List<ShippingMethodItem>>(),
                    total_weight = "0.000",
                    shipping_template = 0,
                    overseas = new Dictionary<string, string>(),
                    member_free = 0
                }
            };
        }

        #endregion

        // #region 复用OrderListService的GetShippingMethod方法

        /// <summary>
        /// 获取运费方法 - 复用OrderListService.GetShippingMethod方法
        /// </summary>
        public Dictionary<string, object> GetShippingMethod(
            int countryId,
            int statesSId,
            List<object> productsInfo,
            Dictionary<int, products> productsDataAry,
            bool isShippingTemplate,
            bool freeShipping = false)
        {
            var proInfoAry = new Dictionary<string, dynamic>();
            var typeWeightAry = new Dictionary<string, dynamic>();

            if (productsInfo != null && productsInfo.Any())
            {
                foreach (var proDataObj in productsInfo)
                {
                    // 将原始数据转换为dynamic
                    var proData = proDataObj as dynamic;

                    // 获取产品ID并转换为int
                    int proId = Convert.ToInt32(proData["ProId"]);

                    try
                    {
                        // 修改后的合并逻辑
                        if (productsDataAry.TryGetValue(proId, out products productData))
                        {
                            var productJObject = JObject.FromObject(productData);

                            // 合并所有属性（自动覆盖同名属性）
                            foreach (var prop in productJObject.Properties())
                            {
                                if (!((JObject)proData).Property(prop.Name)?.HasValues ?? true)
                                {
                                    ((JObject)proData)[prop.Name] = prop.Value;
                                }
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, "合并产品数据时发生错误: ProId={ProId}", proId);
                        throw;
                    }

                    // var kid = $"{proData["OvId"]}-{(isShippingTemplate ? proData["TId"] : 0)}";
                    var kid = $"{proData["OvId"]}-{(proData["TId"])}";

                    var price = _helpsCartService.CeilPrice(
                                    (Convert.ToDecimal(proData["Price"]) + Convert.ToDecimal(proData["PropertyPrice"]) +
                                     Convert.ToDecimal(proData["CustomPrice"])) *
                                    (proData["Discount"].ToString() != "100"
                                        ? Convert.ToDecimal(proData["Discount"]) / 100
                                        : 1)) *
                                Convert.ToInt32(proData["Qty"]);

                    if (!proInfoAry.ContainsKey(kid))
                    {
                        proInfoAry[kid] = new ShippingInfo
                        {
                            Weight = 0m,
                            Volume = 0m,
                            tWeight = 0m,
                            tVolume = 0m,
                            Price = 0m,
                            IsFreeShipping = 0,
                            OvId = proData["OvId"],
                            tQty = 0,
                            Qty = 0,
                            IsCombination = proData["IsCombination"],
                            IsFreeShippingAry = new List<int>(),
                            TId = proData["TId"],
                            GoodsType = 0
                        };
                    }

                    try
                    {
                        proInfoAry[kid].tWeight +=
                            Convert.ToDecimal(proData["Weight"]) * Convert.ToInt32(proData["Qty"]);
                        proInfoAry[kid].tVolume +=
                            Convert.ToDecimal(proData["Volume"]) * Convert.ToInt32(proData["Qty"]);
                        proInfoAry[kid].tQty += Convert.ToInt32(proData["Qty"]);
                        proInfoAry[kid].Price += price;

                        if (Convert.ToInt32(proData["IsFreeShipping"]) == 1)
                        {
                            proInfoAry[kid].IsFreeShipping = 1;
                        }
                        else
                        {
                            proInfoAry[kid].Weight +=
                                Convert.ToDecimal(proData["Weight"]) * Convert.ToInt32(proData["Qty"]);
                            proInfoAry[kid].Volume +=
                                Convert.ToDecimal(proData["Volume"]) * Convert.ToInt32(proData["Qty"]);
                            if (proData["BuyType"].ToString() != "5")
                            {
                                proInfoAry[kid].Qty += Convert.ToInt32(proData["Qty"]);
                            }
                        }

                        proInfoAry[kid].IsFreeShippingAry.Add(Convert.ToInt32(proData["IsFreeShipping"]));
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, "处理产品运费信息时发生错误: ProId={ProId}", proId);
                        throw;
                    }

                    // 获取当前产品的物流类型
                    var goodsType = Convert.ToInt32(_db.Queryable<products_customs>()
                        .Where(pc => pc.ProId == proId)
                        .Select(pc => pc.GoodsType)
                        .First());

                    var aisiflyWeight = Convert.ToDecimal(proData["Weight"]) * Convert.ToInt32(proData["Qty"]);
                    try
                    {
                        typeWeightAry[kid] = PackageInfo(
                            typeWeightAry.ContainsKey(kid) ? typeWeightAry[kid] : new { },
                            goodsType,
                            proData["IsFreeShipping"].ToString() == "1" ? 0 : aisiflyWeight,
                            aisiflyWeight,
                            Convert.ToInt32(proData["OvId"]),
                            Convert.ToInt32(proData["TId"])
                        );

                        proInfoAry[kid].GoodsType = goodsType;
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, "处理包裹信息时发生错误: ProId={ProId}", proId);
                        throw;
                    }
                }
            }

            return GetShippingMethod(countryId, statesSId, proInfoAry, typeWeightAry, isShippingTemplate);
        }

        /// <summary>
        /// 获取物流方式和运费 - 复用OrderListService.GetShippingMethod方法
        /// </summary>
        /// <param name="countryId">国家ID</param>
        /// <param name="statesSId">省份ID</param>
        /// <param name="proInfoAry">产品信息</param>
        /// <param name="typeWeightAry">产品的物流类型</param>
        /// <param name="isShippingTemplate">是否免运费(会员权益)</param>
        /// <returns>运费相关信息</returns>
        public Dictionary<string, object> GetShippingMethod(
            int countryId,
            int statesSId,
            Dictionary<string, dynamic> proInfoAry,
            Dictionary<string, dynamic> typeWeightAry,
            bool isShippingTemplate)
        {
            // 1. 参数检查
            if (countryId == 0 || proInfoAry == null || proInfoAry.Count == 0)
            {
                return new Dictionary<string, object>();
            }

            // 2. 整理物流类型和产品类型
            var goodsTypeAry = new Dictionary<string, List<int>>();
            try
            {
                foreach (var kid in proInfoAry.Keys)
                {
                    var proData = proInfoAry[kid];
                    goodsTypeAry[kid] = new List<int> { (int)proData.GoodsType };
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "整理物流类型时发生错误");
                throw;
            }

            var maxGoodsTypeAry = new Dictionary<string, int>();
            foreach (var k in goodsTypeAry.Keys)
            {
                var maxType = goodsTypeAry[k].Max();
                maxGoodsTypeAry[k] = maxType > 0 ? maxType : 1;
            }

        

            // 3. 处理地区信息
            if (statesSId > 0)
            {

                var statesCount = _db.Queryable<country_states_iso>()
                    .Where(cs => cs.SId == statesSId)
                    .Count();

                if (statesCount == 0)
                {
                    statesSId = 0;
                }
            }

            // 4. 获取可用快递信息
            var shippingQuery = _db.Queryable<shipping>()
                .Where(s => s.ApiType == "0" && s.IsGet == true && s.IsUsed == 1)
                .Select(s => new
                {
                    s.SId,
                    s.Express,
                    s.Logo,
                    s.IsWeightArea,
                    s.UseCondition,
                    s.MinWeight,
                    s.MaxWeight,
                    s.IsUsed,
                    s.IsAPI,
                    s.WeightType,
                    s.TId,
                    s.GoodsType,
                    s.FreightRate,
                    s.OvId
                })
                .ToList();

            var shippingAry = shippingQuery.ToDictionary(s => s.SId.ToString());

            // 5. 物流分区信息
            var shippingAIdAry = _db.Queryable<shipping_country>()
                .Where(sc => sc.CId == countryId)
                .Select(sc => sc.AId)
                .ToList();

            var shippingAreaQuery = _db.Queryable<shipping_area>()
                .Where(sa => shippingAIdAry.Contains(sa.AId))
                .OrderBy(sa => sa.SId, OrderByType.Asc)
                .OrderBy(sa => sa.AId, OrderByType.Asc)
                .ToList();

            var row = new List<dynamic>();
            foreach (var v in shippingAreaQuery)
            {
                if (shippingAry.TryGetValue(v.SId.ToString(), out var shippingInfo))
                {
                    row.Add(new
                    {
                        shippingInfo.SId,
                        shippingInfo.Express,
                        shippingInfo.Logo,
                        shippingInfo.IsWeightArea,
                        shippingInfo.UseCondition,
                        shippingInfo.MinWeight,
                        shippingInfo.MaxWeight,
                        shippingInfo.IsUsed,
                        shippingInfo.IsAPI,
                        shippingInfo.WeightType,
                        shippingInfo.TId,
                        shippingInfo.GoodsType,
                        shippingInfo.FreightRate,
                        shippingInfo.OvId,
                        v.FreeType,
                        v.AId,
                        v.Brief,
                        v.FreeShippingPrice,
                        v.IsFreeShipping,
                        v.FreeShippingWeight,
                        v.FreeShippingQty,
                        v.AffixPrice
                    });
                }
            }

            // 6. 物流价格数据
            var shippingPriceQuery = _db.Queryable<shipping_price>()
                .Where(sp => shippingAIdAry.Contains((ushort)sp.AId))
                .OrderBy(sp => sp.AId, OrderByType.Asc)
                .OrderBy(sp => sp.StartWeight, OrderByType.Asc)
                .ToList();

            var shippingPriceAry = shippingPriceQuery
                .GroupBy(sp => sp.AId)
                .ToDictionary(g => g.Key, g => g.ToList());

            // 7. 处理产品运费
            var rowAry = new Dictionary<int, dynamic>();
            var usedShippingSidAry = new List<int>();
            var shippingTidAry = new Dictionary<int, List<int>>();

            foreach (var v in shippingQuery)
            {
                usedShippingSidAry.Add(v.SId);
                // if (isShippingTemplate && !string.IsNullOrEmpty(v.TId))
                if (!string.IsNullOrEmpty(v.TId))
                {
                    var tIds = v.TId.Split('|', StringSplitOptions.RemoveEmptyEntries)
                        .Select(int.Parse)
                        .ToList();

                    foreach (var tId in tIds)
                    {
                        if (!shippingTidAry.ContainsKey(tId))
                        {
                            shippingTidAry[tId] = new List<int>();
                        }

                        shippingTidAry[tId].Add(v.SId);
                    }
                }
            }

            // 补全缺失的物流分区与仓库匹配逻辑
            foreach (var v in row)
            {
                int sId = v.SId;
                if (!usedShippingSidAry.Contains(sId)) continue;

                // 初始化快递条目（如果不存在）
                if (!rowAry.ContainsKey(sId))
                {
                    rowAry[sId] = new
                    {
                        info = v,
                        overseas = new Dictionary<int, dynamic>()
                    };
                }

                // 处理OvId仓库映射（包含-1默认仓库逻辑）
                string ovIdStr = v.OvId?.ToString().Trim() ?? "";
                var ovidAry = ovIdStr.Split('|', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => id == "-1" ? "0" : id)
                    .Select(int.Parse)
                    .ToList();

                foreach (int warehouseId in ovidAry)
                {
                    // 确保overseas字典存在（理论上已初始化）
                    if (!rowAry[sId].overseas.ContainsKey(warehouseId))
                    {
                        rowAry[sId].overseas[warehouseId] = v;
                    }
                }
            }

            // 初始化结果结构
            var info = new Dictionary<string, List<Dictionary<string, object>>>();
            foreach (var kid in proInfoAry.Keys)
            {
                info[kid] = new List<Dictionary<string, object>>();
            }

            // 处理每个快递方式
            foreach (var keyVal in rowAry)
            {
                var key = keyVal.Key;
                var val = keyVal.Value;
                var rowTwo = val.info;
                var overseasData = val.overseas;

                // 检查仓库匹配
                var hasMatchingOvId = proInfoAry.Values
                    .Any(p => overseasData.ContainsKey(Convert.ToInt32(p.OvId)));

                if (!hasMatchingOvId) continue;

                foreach (var productItem in proInfoAry)
                {
                    var productKey = productItem.Key;
                    var product = productItem.Value;

                    if (!overseasData.TryGetValue(Convert.ToInt32(product.OvId), out dynamic overseas)) continue;

                    // 检查产品类型
                    var goodsTypesTwo = (string)overseas.GoodsType;
                    var goodsTypes = goodsTypesTwo
                        .Split(new[] { '|' }, StringSplitOptions.RemoveEmptyEntries) // 添加去除空项
                        .Select(int.Parse)
                        .ToList();
                    if (!goodsTypes.Contains(maxGoodsTypeAry[productKey])) continue;

                    // 检查运费模板
                    // if (isShippingTemplate)
                    // {
                    var productTId = Convert.ToInt32(product.TId);
                    if (!shippingTidAry.TryGetValue(productTId, out List<int> shippingIds) ||
                        !shippingIds.Contains(rowTwo.SId))
                    {
                        continue;
                    }
                    // }

                    // 检查使用条件
                    var open = CalculateUseCondition(rowTwo, product);
                    if (!open) continue;

                    // 计算运费
                    var shippingPrice = CalculateBasePrice(overseas, product, shippingPriceAry);

                    // 应用运费调整
                    if (rowTwo.FreightRate != 0)
                    {
                        shippingPrice *= (1 + rowTwo.FreightRate);
                    }

                    var conformFree = false; //是否符合免费条件
                    // 处理免费条件
                    if (ShouldApplyFreeShipping(overseas, product, isShippingTemplate))
                    {
                        shippingPrice = 0;
                        conformFree = true;
                    }

                    // 附加费用
                    if ((!conformFree || (conformFree && !IsFreeType(overseas.FreeType, "surcharge"))) &&
                        overseas.AffixPrice > 0)
                    {
                        shippingPrice += overseas.AffixPrice;
                    }

                    // 构建返回数据
                    var shippingItem = new Dictionary<string, object>
                    {
                        { "SId", rowTwo.SId },
                        { "Name", overseas.Express },
                        { "Brief", overseas.Brief },
                        { "IsAPI", overseas.IsAPI },
                        { "type", "" },
                        { "weight", product.Weight },
                        { "ShippingPrice", _helpsCartService.CeilPrice(shippingPrice) },
                        { "ApiType", "0" }
                    };

                    info[productKey].Add(shippingItem);
                }
            }

            // 8. 集成U闪达物流（示例结构）
            if (typeWeightAry != null)
            {
                var acronym = _db.Queryable<country>()
                    .Where(c => c.CId == countryId)
                    .Select(c => c.Acronym)
                    .First();

                var result =
                    _helpsAsiaflyService.GetShippingExpress(acronym, typeWeightAry, 0, 1, isShippingTemplate,
                        statesSId);
                MergeAsiaflyResults(info, result);
            }

            // 9. 整理最终结果
            var infoAry = new Dictionary<string, List<Dictionary<string, object>>>();
            foreach (var kid in info.Keys)
            {
                var products = info[kid];
                var grouped = products
                    .GroupBy(p => p["ShippingPrice"])
                    .OrderBy(g => g.Key)
                    .SelectMany(g => g)
                    .ToList();

                infoAry[kid] = grouped;
            }

            return new Dictionary<string, object>
            {
                { "info", infoAry },
                // { "shipping_template", isShippingTemplate ? 1 : 0 }
                { "shipping_template", 1 }
            };
        }

        #region 辅助方法 - 复用OrderListService的辅助方法

        /// <summary>
        /// 合并Asiafly结果
        /// </summary>
        private void MergeAsiaflyResults(
            Dictionary<string, List<Dictionary<string, object>>> info,
            Dictionary<string, List<dynamic>> asiaflyResult)
        {
            // 这里可以实现Asiafly结果的合并逻辑
            // 暂时保留空实现，与原方法保持一致
        }

        /// <summary>
        /// 检查是否应用免运费
        /// </summary>
        private bool ShouldApplyFreeShipping(
            dynamic overseas,
            dynamic product,
            bool isFreeShipping)
        {
            try
            {
                var freeTypeStr = overseas.FreeType?.ToString() ?? "[]";
                var freeType = JsonConvert.DeserializeObject<List<string>>(freeTypeStr) ?? new List<string>();

                var res = isFreeShipping ||
                          (overseas.IsWeightArea != 3 &&
                           !freeType.Contains("0") &&
                           product.IsFreeShipping == 1 &&
                           product.Weight == 0) ||
                          (overseas.IsWeightArea == 3 &&
                           !freeType.Contains("0") &&
                           product.IsFreeShipping == 1 &&
                           product.Qty == 0) ||
                          (overseas.IsFreeShipping == true &&
                           overseas.FreeShippingPrice > 0 &&
                           product.Price >= overseas.FreeShippingPrice) ||
                          (overseas.IsFreeShipping == true &&
                           overseas.FreeShippingWeight > 0 &&
                           product.Weight < overseas.FreeShippingWeight) ||
                          (overseas.IsFreeShipping == true &&
                           overseas.FreeShippingQty > 0 &&
                           product.Qty >= overseas.FreeShippingQty) ||
                          (overseas.IsFreeShipping == true &&
                           overseas.FreeShippingPrice == 0 &&
                           overseas.FreeShippingWeight == 0 &&
                           overseas.FreeShippingQty == 0);
                return res;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "检查免运费条件时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 计算基础运费价格
        /// </summary>
        private decimal CalculateBasePrice(
            dynamic overseas,
            dynamic product,
            Dictionary<int, List<shipping_price>> shippingPriceAry)
        {
            if (!shippingPriceAry.TryGetValue(overseas.AId, out List<shipping_price> priceAry))
            {
                return 0;
            }

            decimal shippingPrice = 0;

            switch (Convert.ToInt32(overseas.IsWeightArea))
            {
                case 5: // 固定费用
                    var fixedPrice = GetPriceData(priceAry[0].Data)?.fixedPrice ?? 0;
                    return fixedPrice;

                case 0: // 按重量
                    var weightPrice = priceAry.FirstOrDefault(p => product.Weight <= p.EndWeight);
                    if (weightPrice == null) return 0;

                    var priceData = GetPriceData(weightPrice.Data);
                    if (weightPrice.Calculation == "additional")
                    {
                        var extWeight = product.Weight > priceData.firstWeight
                            ? product.Weight - priceData.firstWeight
                            : 0;

                        var extCountTwo = priceData.extWeight > 0
                            ? (int)Math.Ceiling(extWeight / priceData.extWeight)
                            : 0;

                        shippingPrice = priceData.firstPrice + (extCountTwo * priceData.extPrice);
                    }
                    else if (weightPrice.Calculation == "total")
                    {
                        shippingPrice = priceData.fixedPrice;
                    }
                    else if (weightPrice.Calculation == "each")
                    {
                        shippingPrice = priceData.fixedPrice *
                                        _helpsManageService.ToWeightUnitConversion(product.Weight, "KG");
                    }

                    return shippingPrice;

                case 3: // 按数量
                    var qtyPrice = GetPriceData(priceAry[0].Data);
                    var extQty = product.Qty > qtyPrice.firstMaxQty
                        ? product.Qty - qtyPrice.firstMaxQty
                        : 0;

                    var extCount = qtyPrice.extQty > 0
                        ? (int)Math.Ceiling((decimal)extQty / qtyPrice.extQty)
                        : 0;

                    return qtyPrice.firstQtyPrice + (extCount * qtyPrice.extQtyPrice);

                default:
                    return 0;
            }
        }

        /// <summary>
        /// 解析价格数据
        /// </summary>
        private PriceData GetPriceData(string jsonData)
        {
            return JsonConvert.DeserializeObject<PriceData>(jsonData);
        }

        /// <summary>
        /// 计算使用条件
        /// </summary>
        private bool CalculateUseCondition(dynamic row, dynamic product)
        {
            switch (row.UseCondition)
            {
                case 1: // 重量限制
                    return (row.MaxWeight > 0)
                        ? (product.tWeight >= row.MinWeight && product.tWeight <= row.MaxWeight)
                        : (product.tWeight >= row.MinWeight);

                case 2: // 数量限制
                    return (row.MaxWeight > 0)
                        ? (product.tQty >= row.MinWeight && product.tQty <= row.MaxWeight)
                        : (product.tQty >= row.MinWeight);

                case 3: // 总价限制
                    return (row.MaxWeight > 0)
                        ? (product.Price >= row.MinWeight && product.Price <= row.MaxWeight)
                        : (product.Price >= row.MinWeight);

                default:
                    return true;
            }
        }

        /// <summary>
        /// 检查是否免费类型
        /// </summary>
        private bool IsFreeType(string freeTypeJson, string targetType)
        {
            try
            {
                if (string.IsNullOrEmpty(freeTypeJson))
                    return false;

                var freeTypes = JsonConvert.DeserializeObject<List<string>>(freeTypeJson) ?? new List<string>();
                return freeTypes.Contains(targetType);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 包裹信息处理
        /// </summary>
        public static dynamic PackageInfo(
            dynamic existing,
            int goodsType,
            decimal weight,
            decimal tWeight,
            int ovId,
            int tId)
        {
            decimal WeightTwo = 0m;
            decimal tWeightTwo = 0m;

            // 检测空匿名对象
            bool isEmptyAnonymous = existing?.GetType().GetProperties().Length == 0;

            if (existing != null && !isEmptyAnonymous)
            {
                try
                {
                    WeightTwo = Convert.ToDecimal(existing.Weight);
                    tWeightTwo = Convert.ToDecimal(existing.tWeight);
                }
                catch (Microsoft.CSharp.RuntimeBinder.RuntimeBinderException)
                {
                    // 属性不存在时的处理逻辑
                }
            }

            return new
            {
                GoodsType = goodsType,
                Weight = WeightTwo + weight,
                tWeight = tWeightTwo + tWeight,
                OvId = ovId,
                TId = tId
            };
        }

        /// <summary>
        /// 构建响应
        /// </summary>
        private async Task<ShippingMethodResponse> BuildResponse(
            Dictionary<string, object> shippingMethodResult,
            Dictionary<string, string> overseasDict,
            decimal totalWeight,
            bool shippingTemplate,
            string currentCurrency)
        {
            // 检查是否有有效的运费模版数据
            bool hasValidShippingData = false;

            // 获取币种信息用于格式化价格
            var userCurrency = await _currencyService.GetCurrency(currentCurrency);
            var manageCurrency = await _currencyService.GetManageDefaultCurrency();

            var response = new ShippingMethodResponse
            {
                ret = 0, // 默认设置为0，有有效数据时再设置为1
                msg = new ShippingMethodData
                {
                    Symbol = userCurrency?.Symbol ?? "$", // 使用用户币种符号
                    info = new Dictionary<string, List<ShippingMethodItem>>(),
                    total_weight = totalWeight.ToString("F3"),
                    shipping_template = shippingTemplate ? 1 : 0,
                    overseas = overseasDict,
                    member_free = 0 // 暂时设为0，可以根据实际需求获取会员权益
                }
            };

            // 处理运费信息
            if (shippingMethodResult != null &&
                shippingMethodResult.TryGetValue("info", out var infoObj) &&
                infoObj is Dictionary<string, List<Dictionary<string, object>>> infoDict &&
                infoDict.Count > 0)
            {
                foreach (var key in infoDict.Keys)
                {
                    var methods = infoDict[key];
                    if (methods != null && methods.Count > 0)
                    {
                        var methodItems = new List<ShippingMethodItem>();

                        foreach (var method in methods)
                        {
                            // 获取原始价格
                            decimal originalShippingPrice = Convert.ToDecimal(method["ShippingPrice"]);
                            decimal originalWebShippingPrice = method.ContainsKey("webShippingPrice") 
                                ? Convert.ToDecimal(method["webShippingPrice"]) 
                                : originalShippingPrice;
                            
                            // 获取转换后的价格（按照当前用户的货币进行转换）
                            decimal convertedShippingPrice = _currencyService.GetConvertedPrice(originalShippingPrice, userCurrency,manageCurrency); 
                            //decimal convertedWebShippingPrice = _currencyService.GetConvertedPrice(originalWebShippingPrice, userCurrency,manageCurrency);
                            
                            // 创建运费方法项
                            var item = new ShippingMethodItem
                            {
                                SId = Convert.ToInt32(method["SId"]),
                                Name = method["Name"]?.ToString() ?? string.Empty,
                                Brief = method["Brief"]?.ToString() ?? string.Empty,
                                IsAPI = Convert.ToInt32(method["IsAPI"]),
                                type = method["type"]?.ToString() ?? string.Empty,
                                weight = method["weight"]?.ToString() ?? "0.000",
                                ShippingPrice = convertedShippingPrice,  // 使用转换后的价格值
                                webShippingPrice = originalWebShippingPrice, // 使用转换后的价格值
                                ApiType = method["ApiType"]?.ToString() ?? "0"
                            };

                            methodItems.Add(item);
                        }

                        response.msg.info[key] = methodItems;
                        hasValidShippingData = true; // 标记有有效数据
                    }
                }
            }

            // 只有当有有效的运费数据时，才将ret设置为1
            if (hasValidShippingData)
            {
                response.ret = 1;
            }

            return response;
        }

        #endregion

        #region 数据类 - 复用OrderListService的数据类

        /// <summary>
        /// 价格数据类
        /// </summary>
        public class PriceData
        {
            public decimal firstPrice { get; set; }
            public decimal extPrice { get; set; }
            public decimal fixedPrice { get; set; }
            public decimal firstWeight { get; set; }
            public decimal extWeight { get; set; }
            public decimal firstQtyPrice { get; set; }
            public decimal extQtyPrice { get; set; }
            public int firstMaxQty { get; set; }
            public int extQty { get; set; }
        }

        /// <summary>
        /// 运费信息类
        /// </summary>
        public class ShippingInfo
        {
            public decimal Weight { get; set; }
            public decimal Volume { get; set; }
            public decimal tWeight { get; set; }
            public decimal tVolume { get; set; }
            public decimal Price { get; set; }
            public int IsFreeShipping { get; set; }
            public object OvId { get; set; }
            public int tQty { get; set; }
            public int Qty { get; set; }
            public object IsCombination { get; set; }
            public List<int> IsFreeShippingAry { get; set; }
            public object TId { get; set; }
            public int GoodsType { get; set; }
        }

        #endregion
    }
}