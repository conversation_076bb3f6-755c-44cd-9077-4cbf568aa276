using YseStore.IService.Blog;
using YseStore.Model.Entities.Blog;
using YseStore.Repo;

namespace YseStore.Service.Blog
{
    /// <summary>
    /// 博客内容服务实现
    /// </summary>
    public class BlogNewContentService : BaseServices<BlogNewContent>, IBlogNewContentService
    {
        /// <summary>
        /// 构造函数，通过依赖注入获取数据仓储
        /// </summary>
        /// <param name="baseDal">BlogNewContent仓储</param>
        public BlogNewContentService(IBaseRepository<BlogNewContent> baseDal) : base(baseDal)
        {
        }

        /// <summary>
        /// 根据文章ID获取博客内容
        /// </summary>
        /// <param name="articleId">文章ID</param>
        /// <returns>博客内容实体</returns>
        public async Task<BlogNewContent> GetContentByArticleId(int articleId)
        {
            return await Db.Queryable<BlogNewContent>()
                .Where(c => c.AId == articleId)
                .FirstAsync();
        }

        /// <summary>
        /// 添加博客内容
        /// </summary>
        /// <param name="content">博客内容实体</param>
        /// <returns>新增内容ID</returns>
        public async Task<long> AddContent(BlogNewContent content)
        {
            return await Add(content);
        }

        /// <summary>
        /// 更新博客内容
        /// </summary>
        /// <param name="content">博客内容实体</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateContent(BlogNewContent content)
        {
            return await Update(content);
        }

        /// <summary>
        /// 删除博客内容
        /// </summary>
        /// <param name="id">内容ID</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> DeleteContent(int id)
        {
            return await DeleteById(id);
        }

        /// <summary>
        /// 删除文章相关的所有内容
        /// </summary>
        /// <param name="articleId">文章ID</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> DeleteContentByArticleId(int articleId)
        {
            try
            {
                await Db.Deleteable<BlogNewContent>()
                    .Where(c => c.AId == articleId)
                    .ExecuteCommandAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
} 