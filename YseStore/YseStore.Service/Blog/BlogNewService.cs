using YseStore.IService.Blog;
using YseStore.Model;
using YseStore.Model.Entities.Blog;
using YseStore.Model.RequestModels.Blog;
using YseStore.Repo;
using Microsoft.Extensions.Logging;

namespace YseStore.Service.Blog
{
    /// <summary>
    /// 博客文章服务实现
    /// </summary>
    public class BlogNewService : BaseServices<BlogNew>, IBlogNewService
    {
        private readonly ILogger<BlogNewService> _logger;
        private readonly IBaseRepository<BlogNewContent> _blogNewContentRepository;
        private readonly IBlogNewContentService _blogNewContentService;
        private readonly IBlogNewTagsService _tagService;
        private readonly IBlogSeoService _blogSeoService;
        private readonly IBlogReviewService _blogReviewService;

        /// <summary>
        /// 构造函数，通过依赖注入获取数据仓储
        /// </summary>
        /// <param name="baseDal">BlogNew仓储</param>
        /// <param name="blogNewContentRepository">博客内容仓储</param>
        /// <param name="blogNewContentService">博客内容服务</param>
        /// <param name="tagService">博客标签服务</param>
        /// <param name="blogSeoService">博客SEO服务</param>
        /// <param name="blogReviewService">博客评论服务</param>
        /// <param name="logger">日志记录器</param>
        public BlogNewService(
            IBaseRepository<BlogNew> baseDal,
            IBaseRepository<BlogNewContent> blogNewContentRepository,
            IBlogNewContentService blogNewContentService,
            IBlogNewTagsService tagService,
            IBlogSeoService blogSeoService,
            IBlogReviewService blogReviewService,
            ILogger<BlogNewService> logger) : base(baseDal)
        {
            _logger = logger;
            _blogNewContentRepository = blogNewContentRepository;
            _blogNewContentService = blogNewContentService;
            _tagService = tagService;
            _blogSeoService = blogSeoService;
            _blogReviewService = blogReviewService;
        }

        /// <summary>
        /// 获取博客文章列表，支持搜索、分类过滤和状态过滤
        /// </summary>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="status">文章状态：published(已发布)、timing(定时发布)、draft(草稿)</param>
        /// <param name="cateId">分类ID</param>
        /// <param name="publishTime">发布时间范围</param>
        /// <param name="tagId">标签ID</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页记录数</param>
        /// <param name="orderByField">排序字段，默认为MyOrder</param>
        /// <returns>分页后的博客文章列表</returns>
        public async Task<PageModel<BlogNew>> GetBlogList(
            string keyword = "",
            string status = "",
            string cateId = "",
            string publishTime = "",
            string tagId = "",
            int pageIndex = 1,
            int pageSize = 20,
            string orderByField = "",
            string sortDirection = "DESC")
        {
            try
            {
                var query = Db.Queryable<BlogNew>();

                // 搜索条件：标题或内容包含关键词
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(b => b.Title.Contains(keyword) || b.BriefDescription.Contains(keyword));
                }

                // 状态过滤
                if (!string.IsNullOrEmpty(status))
                {
                    switch (status.ToLower())
                    {
                        case "published": // 已发布
                            query = query.Where(b =>
                                !b.IsDraft && b.AccTime != null &&
                                b.AccTime <= DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now));
                            break;
                        case "timing": // 定时发布
                            query = query.Where(b =>
                                !b.IsDraft && b.AccTime != null &&
                                b.AccTime > DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now));
                            break;
                        case "draft": // 草稿
                            query = query.Where(b => b.IsDraft);
                            break;
                    }
                }

                // 分类过滤
                if (!string.IsNullOrEmpty(cateId) && uint.TryParse(cateId, out uint categoryId))
                {
                    query = query.Where(b => b.CateId == categoryId);
                }

                // 发布时间范围过滤
                if (!string.IsNullOrEmpty(publishTime))
                {
                    // 支持多种分隔符，例如 "~" 或 "/"
                    var timeParts = publishTime.Split(new[] { '~', '/' }, StringSplitOptions.RemoveEmptyEntries);
                    if (timeParts.Length == 2)
                    {
                        // 解析起始时间
                        if (DateTime.TryParse(timeParts[0], out DateTime startTime))
                        {
                            int startTimestamp = DateTimeHelper.ConvertToUnixTimestamp(startTime);
                            query = query.Where(b => b.AccTime >= startTimestamp);
                        }

                        // 解析结束时间
                        if (DateTime.TryParse(timeParts[1], out DateTime endTime))
                        {
                            // 设置结束时间为当天的 23:59:59
                            endTime = endTime.Date.AddDays(1).AddSeconds(-1);
                            int endTimestamp = DateTimeHelper.ConvertToUnixTimestamp(endTime);
                            query = query.Where(b => b.AccTime <= endTimestamp);
                        }
                    }
                }


                // 标签过滤
                if (!string.IsNullOrEmpty(tagId))
                {
                    // 将传入的 tagId 按逗号分隔为单个标签 ID 列表
                    var tagIdList = tagId.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(t => t.Trim())
                        .Where(t => !string.IsNullOrEmpty(t))
                        .ToList();

                    // 构建查询条件，确保每个标签 ID 都在 Tag 字段中
                    query = query.Where(b => b.Tag != null && tagIdList.All(tag => b.Tag.Contains(tag)));
                }

                // 应用排序
                bool isAscending = sortDirection?.ToUpper() == "ASC";

                switch (orderByField?.ToLower())
                {
                    case "viewcount":
                        query = isAscending ? query.OrderBy(b => b.ViewCount) : query.OrderByDescending(b => b.ViewCount);
                        break;
                    case "comments":
                        // 注意：Comments字段是在后面计算的，这里先按AccTime排序，后面会重新排序
                        query = isAscending ? query.OrderBy(b => b.AccTime) : query.OrderByDescending(b => b.AccTime);
                        break;
                    case "acctime":
                        query = isAscending ? query.OrderBy(b => b.AccTime) : query.OrderByDescending(b => b.AccTime);
                        break;
                    case "modifyat":
                        query = isAscending ? query.OrderBy(b => b.ModifyAt) : query.OrderByDescending(b => b.ModifyAt);
                        break;
                    default:
                        // 默认按发布时间降序排序
                        query = query.OrderBy(b => b.MyOrder).OrderByDescending(b => b.AccTime);
                        break;
                }
                // 查询分页数据
                var result = await query.ToPageListAsync(pageIndex, pageSize);
                var total = await query.CountAsync();

                // 查询每篇博客的评论数据
                if (result != null && result.Count > 0)
                {
                    foreach (var blog in result)
                    {
                        try
                        {
                            // 格式化发布时间
                            if (blog.AccTime.HasValue)
                            {
                                var toBeijingTime = DateTimeHelper.ConvertToBeijingTime(blog.AccTime.Value);
                                blog.StandardAccTime = toBeijingTime.ToString("MMM d, yyyy");
                            }

                            // 查询评论数据
                            uint blogId = (uint)blog.AId;
                            var queryRequest = new BlogReviewQueryRequest
                            {
                                BlogId = blogId,
                                PageIndex = 1,
                                PageSize = 1, // 只需要获取评论数量，不需要所有评论内容
                                OrderByFileds = "AccTime DESC"
                            };

                            var reviewResult = await _blogReviewService.GetReviewList(queryRequest);

                            // 设置评论计数
                            if (reviewResult != null)
                            {
                                blog.Comments = reviewResult.dataCount;

                                // 如果需要获取评论列表，可以取消下面的注释
                                // if (reviewResult.data.Any())
                                // {
                                //     blog.Reviews = reviewResult.data;
                                // }
                            }
                            else
                            {
                                blog.Comments = 0;
                            }
                        }
                        catch (Exception ex)
                        {
                            // 记录错误但不中断处理
                            _logger.LogError(ex, "获取博客ID {BlogId} 的评论数据时出错: {Message}", blog.AId, ex.Message);
                            blog.Comments = 0;
                        }
                    }
                }

                // 如果是按评论数排序，需要在获取评论数据后重新排序
                if (orderByField?.ToLower() == "comments" && result != null && result.Count > 0)
                {
                    bool isCommentsAscending = sortDirection?.ToUpper() == "ASC";
                    result = isCommentsAscending ?
                        result.OrderBy(b => b.Comments).ToList() :
                        result.OrderByDescending(b => b.Comments).ToList();
                }

                return new PageModel<BlogNew>
                {
                    page = pageIndex,
                    PageSize = pageSize, // 设置正确的PageSize
                    dataCount = (int)total,
                    data = result
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询博客列表时出错: {Message}", ex.Message);
                // 返回空结果而不是抛出异常
                return new PageModel<BlogNew>
                {
                    page = pageIndex,
                    PageSize = pageSize, // 设置正确的PageSize
                    dataCount = 0,
                    data = new List<BlogNew>()
                };
            }
        }

        /// <summary>
        /// 根据ID获取博客文章详情
        /// </summary>
        /// <param name="id">文章ID</param>
        /// <returns>博客文章实体</returns>
        public async Task<BlogNew> GetBlogById(int id)
        {
            // 获取博客详情
            BlogNew blogNew = await QueryById(id);
            if (blogNew == null)
            {
                return null;
            }

            if (blogNew.AccTime.HasValue)
            {
                // 将时间戳转为北京时间 DateTime
                var toBeijingTime = DateTimeHelper.ConvertToBeijingTime(blogNew.AccTime.Value);

                // 格式化为 "Aug 12, 2024" 形式
                blogNew.StandardAccTime = toBeijingTime.ToString("MMM d, yyyy");
            }
            else
            {
                blogNew.StandardAccTime = null; // 或者设置默认值
            }

            try
            {
                // 使用 uint 类型进行查询匹配
                uint uintId = (uint)id;

                // 使用BlogReviewService获取评论列表
                var queryRequest = new BlogReviewQueryRequest
                {
                    BlogId = uintId,
                    PageIndex = 1,
                    PageSize = 100, // 获取足够多的评论
                    OrderByFileds = "AccTime DESC" // 按时间倒序排序
                };

                var reviewResult = await _blogReviewService.GetReviewList(queryRequest);

                // 设置评论相关数据
                if (reviewResult != null && reviewResult.data != null)
                {
                    blogNew.Reviews = reviewResult.data;
                    blogNew.Comments = reviewResult.dataCount;

                    // 获取总点赞数
                    blogNew.Praise = reviewResult.data.Sum(r => r.Praise ?? 0);
                }
                else
                {
                    blogNew.Reviews = new List<BlogReview>();
                    blogNew.Comments = 0;
                    blogNew.Praise = 0;
                }

                // 使用标签服务解析标签名称集合
                blogNew.TagsNameList = await _tagService.GetTagNamesByBlog(blogNew, id);
            }
            catch (Exception ex)
            {
                blogNew.TagsNameList = new List<string>();
            }

            return blogNew;
        }

        /// <summary>
        /// 添加博客文章（包含内容和标签）
        /// </summary>
        /// <param name="blog">博客文章实体</param>
        /// <param name="content">博客内容实体（如果有）</param>
        /// <param name="tagNames">标签名称列表（如果有）</param>
        /// <returns>新增文章的ID</returns>
        public async Task<long> AddBlog(BlogNew blog, BlogNewContent content = null, List<string> tagNames = null)
        {
            // 处理PageUrl唯一性
            if (!string.IsNullOrEmpty(blog.PageUrl))
            {
                // 生成唯一的PageUrl
                blog.PageUrl = await _blogSeoService.GenerateUniqueBlogPageUrlAsync(blog.PageUrl, 0);
            }
            else if (!string.IsNullOrEmpty(blog.Title))
            {
                // 如果PageUrl为空，使用博客标题生成
                blog.PageUrl = await _blogSeoService.GenerateUniqueBlogPageUrlAsync(blog.Title, 0);
            }
            else
            {
                // 如果标题也为空，使用时间戳生成默认URL
                blog.PageUrl = $"blog-{DateTime.Now.Ticks}";
            }

            // 开启事务
            var result = await Db.Ado.UseTranAsync(async () =>
            {
                try
                {
                    // 设置创建时间和修改时间
                    blog.ModifyAt = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                    if (blog.AccTime == null && !blog.IsDraft)
                    {
                        blog.AccTime = blog.ModifyAt;
                    }

                    // 处理标签
                    if (tagNames != null && tagNames.Count > 0)
                    {
                        try
                        {
                            var tagIds = await _tagService.GetOrCreateTagsByNames(tagNames);
                            blog.Tag = _tagService.ConvertTagIdsToString(tagIds);
                        }
                        catch (Exception ex)
                        {
                            // 记录错误，但不抛出异常，使用默认标签
                            _logger.LogError(ex, "处理标签时出错: {Message}", ex.Message);
                            blog.Tag = "1"; // 使用默认标签ID
                        }
                    }

                    // 添加博客基本信息
                    int blogId = await AddWithIntId(blog);

                    // 如果有内容，保存博客内容
                    if (content != null && !string.IsNullOrEmpty(content.Content))
                    {
                        content.AId = blogId;
                        await _blogNewContentRepository.AddWithIntId(content);
                    }

                    return blogId;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "添加博客时出错: {Message}", ex.Message);
                    throw; // 重新抛出异常，让事务回滚
                }
            });

            // 如果操作成功，更新博客PageUrl缓存
            if (result.IsSuccess && !string.IsNullOrEmpty(blog.PageUrl) && result.Data > 0)
            {
                _blogSeoService.UpdateBlogPageUrlCache(blog.PageUrl, (int)result.Data);
            }

            return result.IsSuccess ? result.Data : 0;
        }

        /// <summary>
        /// 更新博客文章（包含内容和标签）
        /// </summary>
        /// <param name="blog">博客文章实体</param>
        /// <param name="content">博客内容实体（如果有）</param>
        /// <param name="tagNames">标签名称列表（如果有）</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateBlog(BlogNew blog, BlogNewContent content = null, List<string> tagNames = null)
        {
            try
            {
                // 处理PageUrl唯一性
                if (!string.IsNullOrEmpty(blog.PageUrl))
                {
                    // 生成唯一的PageUrl
                    blog.PageUrl = await _blogSeoService.GenerateUniqueBlogPageUrlAsync(blog.PageUrl, blog.AId);
                }
                else if (!string.IsNullOrEmpty(blog.Title))
                {
                    // 如果PageUrl为空，使用博客标题生成
                    blog.PageUrl = await _blogSeoService.GenerateUniqueBlogPageUrlAsync(blog.Title, blog.AId);
                }

                // 开启事务
                var result = await Db.Ado.UseTranAsync(async () =>
                {
                    try
                    {
                        // 更新修改时间
                        blog.ModifyAt = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);

                        // 处理标签
                        if (tagNames != null)
                        {
                            try
                            {
                                var tagIds = await _tagService.GetOrCreateTagsByNames(tagNames);
                                blog.Tag = _tagService.ConvertTagIdsToString(tagIds);
                            }
                            catch (Exception ex)
                            {
                                // 记录错误，但不抛出异常，保留原标签或使用默认标签
                                _logger.LogError(ex, "处理标签时出错: {Message}", ex.Message);
                                if (string.IsNullOrEmpty(blog.Tag))
                                {
                                    blog.Tag = "1"; // 使用默认标签ID
                                }
                            }
                        }

                        // 更新博客基本信息，但排除创建时间和浏览量字段
                        bool updateResult = await Db.Updateable(blog)
                            .IgnoreColumns(it => new { it.AccTime, it.ViewCount }) // 不更新创建时间和浏览量
                            .ExecuteCommandHasChangeAsync();

                        if (!updateResult)
                        {
                            return false;
                        }

                        // 如果有内容，保存或更新博客内容
                        if (content != null)
                        {
                            // 检查是否已存在该博客的内容
                            var existingContent = await _blogNewContentService.GetContentByArticleId(blog.AId);

                            if (existingContent != null)
                            {
                                // 更新现有内容
                                existingContent.Content = content.Content;
                                existingContent.UsedMobile = content.UsedMobile;
                                existingContent.MobileDescription = content.MobileDescription;
                                await _blogNewContentService.UpdateContent(existingContent);
                            }
                            else
                            {
                                // 添加新内容
                                content.AId = blog.AId;
                                await _blogNewContentService.AddContent(content);
                            }
                        }

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "更新博客时出错: {Message}", ex.Message);
                        throw; // 重新抛出异常，让事务回滚
                    }
                });

                // 如果操作成功，更新博客PageUrl缓存
                if (result.IsSuccess && result.Data && !string.IsNullOrEmpty(blog.PageUrl) && blog.AId > 0)
                {
                    _blogSeoService.UpdateBlogPageUrlCache(blog.PageUrl, blog.AId);
                }

                return result.IsSuccess && result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新博客文章失败，博客ID: {BlogId}, 错误信息: {Message}", blog?.AId, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 删除博客文章及其关联内容
        /// </summary>
        /// <param name="id">文章ID</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> DeleteBlog(int id)
        {
            // 开启事务，同时删除博客和相关内容
            var result = await Db.Ado.UseTranAsync(async () =>
            {
                // 删除博客内容
                await _blogNewContentService.DeleteContentByArticleId(id);

                // 删除博客基本信息
                return await DeleteById(id);
            });

            return result.IsSuccess && result.Data;
        }

        /// <summary>
        /// 增加博客文章的浏览数
        /// </summary>
        /// <param name="id">文章ID</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> IncrementViewCount(int id)
        {
            try
            {
                // 使用SQL直接更新浏览数，避免读取后再更新的开销
                await Db.Updateable<BlogNew>()
                    .SetColumns(b => new BlogNew { ViewCount = b.ViewCount + 1 })
                    .Where(b => b.AId == id)
                    .ExecuteCommandAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 将博客文章置顶
        /// </summary>
        /// <param name="id">文章ID</param>
        /// <returns>是否置顶成功</returns>
        public async Task<bool> SetBlogTopAsync(int id)
        {
            try
            {
                // 1. 获取当前的最大排序值
                var maxOrder = Db.Queryable<BlogNew>()
                    .Max(b => b.MyOrder);

                // 2. 设置新的排序值（最大值+10，确保排在最前面）
                short newOrder;
                if (maxOrder + 10 > short.MaxValue)
                {
                    newOrder = short.MaxValue;
                }
                else
                {
                    newOrder = (short)(maxOrder + 10);
                }

                // 3. 更新博客的排序值
                return await Db.Updateable<BlogNew>()
                    .SetColumns(b => new BlogNew { MyOrder = newOrder })
                    .Where(b => b.AId == id)
                    .ExecuteCommandHasChangeAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置博客置顶时出错: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 取消博客文章置顶
        /// </summary>
        /// <param name="id">文章ID</param>
        /// <returns>是否取消置顶成功</returns>
        public async Task<bool> CancelBlogTopAsync(int id)
        {
            try
            {
                // 设置为默认排序值（0），使其排在后面
                short defaultOrder = 0;

                // 更新博客的排序值
                return await Db.Updateable<BlogNew>()
                    .SetColumns(b => new BlogNew { MyOrder = defaultOrder })
                    .Where(b => b.AId == id)
                    .ExecuteCommandHasChangeAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消博客置顶时出错: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 更新博客文章的排序
        /// </summary>
        /// <param name="orderDict">排序字典，键为博客ID，值为目标排序值</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateBlogsOrder(Dictionary<int, int> orderDict)
        {
            try
            {
                // 检查字典是否为空
                if (orderDict == null || !orderDict.Any())
                {
                    return false;
                }

                // 遍历字典，更新博客文章的排序值
                foreach (var item in orderDict)
                {
                    int blogId = item.Key;
                    int targetOrder = item.Value;

                    // 更新博客文章的排序值
                    await Db.Updateable<BlogNew>()
                        .SetColumns(b => new BlogNew { MyOrder = (short)targetOrder })
                        .Where(b => b.AId == blogId)
                        .ExecuteCommandAsync();
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新博客文章排序时出错: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 根据博客ID列表获取博客列表，支持分页
        /// </summary>
        /// <param name="blogIds">博客ID列表</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页记录数</param>
        /// <param name="orderByField">排序字段，默认为MyOrder</param>
        /// <returns>分页后的博客列表</returns>
        public async Task<PageModel<BlogNew>> GetBlogsByIds(List<int> blogIds, int pageIndex = 1, int pageSize = 20,
            string orderByField = "MyOrder")
        {
            try
            {
                // 检查参数是否为空
                if (blogIds == null || !blogIds.Any())
                {
                    return new PageModel<BlogNew>
                    {
                        page = pageIndex,
                        PageSize = pageSize,
                        dataCount = 0,
                        data = new List<BlogNew>()
                    };
                }

                // 验证分页参数
                if (pageIndex < 1) pageIndex = 1;
                if (pageSize < 1) pageSize = 20;

                // 创建查询对象
                var query = Db.Queryable<BlogNew>()
                    .Where(b => blogIds.Contains(b.AId));

                // 设置排序
                switch (orderByField?.ToLower())
                {
                    case "acctime":
                        query = query.OrderByDescending(b => b.AccTime);
                        break;
                    case "modifyat":
                        query = query.OrderByDescending(b => b.ModifyAt);
                        break;
                    case "viewcount":
                        query = query.OrderByDescending(b => b.ViewCount);
                        break;
                    case "title":
                        query = query.OrderBy(b => b.Title);
                        break;
                    default: // 默认按MyOrder排序
                        query = query.OrderByDescending(b => b.MyOrder);
                        break;
                }

                // 获取总记录数
                var totalCount = await query.CountAsync();

                // 执行分页查询
                var result = await query.ToPageListAsync(pageIndex, pageSize);

                // 处理每个博客的附加信息
                if (result != null && result.Count > 0)
                {
                    foreach (var blog in result)
                    {
                        try
                        {
                            // 格式化发布时间
                            if (blog.AccTime.HasValue)
                            {
                                var toBeijingTime = DateTimeHelper.ConvertToBeijingTime(blog.AccTime.Value);
                                blog.StandardAccTime = toBeijingTime.ToString("MMM d, yyyy");
                            }

                            // 查询评论数据
                            uint blogId = (uint)blog.AId;
                            var queryRequest = new BlogReviewQueryRequest
                            {
                                BlogId = blogId,
                                PageIndex = 1,
                                PageSize = 1, // 只需要获取评论数量
                                OrderByFileds = "AccTime DESC"
                            };

                            var reviewResult = await _blogReviewService.GetReviewList(queryRequest);

                            // 设置评论计数
                            if (reviewResult != null)
                            {
                                blog.Comments = reviewResult.dataCount;
                            }
                            else
                            {
                                blog.Comments = 0;
                            }

                            // 获取标签名称列表
                            blog.TagsNameList = await _tagService.GetTagNamesByBlog(blog, blog.AId);
                        }
                        catch (Exception ex)
                        {
                            // 记录错误但不中断处理
                            _logger.LogError(ex, "获取博客ID {BlogId} 的附加信息时出错: {Message}", blog.AId, ex.Message);
                            blog.Comments = 0;
                            blog.TagsNameList = new List<string>();
                        }
                    }
                }

                // 返回分页结果
                return new PageModel<BlogNew>
                {
                    page = pageIndex,
                    PageSize = pageSize,
                    dataCount = (int)totalCount,
                    data = result
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID列表查询博客时出错: {Message}", ex.Message);
                // 返回空的分页结果
                return new PageModel<BlogNew>
                {
                    page = pageIndex,
                    PageSize = pageSize,
                    dataCount = 0,
                    data = new List<BlogNew>()
                };
            }
        }
    }
}