using SqlSugar;
using YseStore.IService.Blog;
using YseStore.Model;
using YseStore.Model.Entities.Blog;
using YseStore.Model.RequestModels.Blog;
using YseStore.Repo;

namespace YseStore.Service.Blog
{
    /// <summary>
    /// 博客评论服务实现
    /// </summary>
    public class BlogReviewService : BaseServices<BlogReview>, IBlogReviewService
    {
        private readonly IBaseRepository<BlogReply> _replyRepository;

        /// <summary>
        /// 构造函数，通过依赖注入获取数据仓储
        /// </summary>
        /// <param name="baseDal">BlogReview仓储</param>
        /// <param name="replyRepository">BlogReply仓储</param>
        public BlogReviewService(
            IBaseRepository<BlogReview> baseDal,
            IBaseRepository<BlogReply> replyRepository) : base(baseDal)
        {
            _replyRepository = replyRepository;
        }

        /// <summary>
        /// 获取博客评论列表，支持搜索和分页
        /// </summary>
        /// <param name="queryRequest">查询请求参数</param>
        /// <returns>分页后的博客评论列表</returns>
        public async Task<PageModel<BlogReview>> GetReviewList(BlogReviewQueryRequest queryRequest)
        {
            try
            {
                var query = Db.Queryable<BlogReview>();

                // 搜索条件：评论内容或评论者姓名包含关键词
                if (!string.IsNullOrEmpty(queryRequest.Keyword))
                {
                    query = query.Where(r =>
                        r.Content.Contains(queryRequest.Keyword) || r.Name.Contains(queryRequest.Keyword));
                }

                // 按博客ID筛选
                if (queryRequest.BlogId.HasValue)
                {
                    query = query.Where(r => r.AId == queryRequest.BlogId.Value);
                }

                // 按是否已回复筛选 - 使用子查询检查是否有回复记录
                if (queryRequest.IsReplied.HasValue)
                {
                    // var replyQuery = Db.Queryable<BlogReply>();
                    if (queryRequest.IsReplied.Value)
                    {
                        // 已回复：有关联的回复记录
                        query = query.Where(r => SqlFunc.Subqueryable<BlogReply>()
                            .Where(reply => reply.ReviewId == r.RId)
                            .Any());
                    }
                    else
                    {
                        // 未回复：没有关联的回复记录
                        query = query.Where(r => !SqlFunc.Subqueryable<BlogReply>()
                            .Where(reply => reply.ReviewId == r.RId)
                            .Any());
                    }
                }

                // 设置排序
                if (string.IsNullOrEmpty(queryRequest.OrderByFileds))
                {
                    // 默认按评论时间降序排序（新评论在前）
                    query = query.OrderByDescending(x => x.AccTime);
                }
                else
                {
                    // 使用自定义排序
                    query = query.OrderByIF(!string.IsNullOrEmpty(queryRequest.OrderByFileds),
                        queryRequest.OrderByFileds);
                }

                // 查询分页数据
                var result = await query.ToPageListAsync(queryRequest.PageIndex, queryRequest.PageSize);
                var total = await query.CountAsync();

                // 查询每条评论的回复数量和回复内容
                if (result != null && result.Count > 0)
                {
                    var reviewIds = result.Select(r => r.RId).ToList();

                    // 获取所有评论的回复数量
                    var replyCounts = await Db.Queryable<BlogReply>()
                        .Where(r => reviewIds.Contains(r.ReviewId))
                        .GroupBy(r => r.ReviewId)
                        .Select(g => new { ReviewId = g.ReviewId, Count = SqlFunc.AggregateCount(g.ReplyId) })
                        .ToListAsync();

                    // 获取所有评论的回复列表
                    var allReplies = await Db.Queryable<BlogReply>()
                        .Where(r => reviewIds.Contains(r.ReviewId))
                        .OrderBy(r => r.ReplyTime)
                        .ToListAsync();

                    // 按评论ID分组回复
                    var repliesByReviewId = allReplies.GroupBy(r => r.ReviewId)
                        .ToDictionary(g => g.Key, g => g.ToList());

                    foreach (var review in result)
                    {
                        // 设置回复数量
                        var replyCount = replyCounts.FirstOrDefault(r => r.ReviewId == review.RId);
                        review.ReplyCount = replyCount?.Count ?? 0;

                        // 将回复列表附加到评论对象
                        if (repliesByReviewId.TryGetValue(review.RId, out var replies))
                        {
                            review.Replies = replies;
                        }
                        else
                        {
                            review.Replies = new List<BlogReply>();
                        }
                    }
                }

                return new PageModel<BlogReview>
                {
                    page = queryRequest.PageIndex,
                    dataCount = (int)total,
                    PageSize = queryRequest.PageSize,
                    data = result
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查询评论列表时出错: {ex.Message}");
                // 返回空结果而不是抛出异常
                return new PageModel<BlogReview>
                {
                    page = queryRequest.PageIndex,
                    PageSize = queryRequest.PageSize,
                    dataCount = 0,
                    data = new List<BlogReview>()
                };
            }
        }

        /// <summary>
        /// 根据ID获取博客评论详情
        /// </summary>
        /// <param name="id">评论ID</param>
        /// <returns>博客评论实体</returns>
        public async Task<BlogReview> GetReviewById(int id)
        {
            var review = await QueryById(id);
            if (review != null)
            {
                // 获取回复数量
                review.ReplyCount = await Db.Queryable<BlogReply>()
                    .Where(r => r.ReviewId == id)
                    .CountAsync();

                // 获取回复列表
                review.Replies = await Db.Queryable<BlogReply>()
                    .Where(r => r.ReviewId == id)
                    .OrderBy(r => r.ReplyTime)
                    .ToListAsync();
            }

            return review;
        }

        /// <summary>
        /// 添加博客评论
        /// </summary>
        /// <param name="review">博客评论实体</param>
        /// <returns>新增评论的ID</returns>
        public async Task<long> AddReview(BlogReview review)
        {
            // 设置评论时间（Unix时间戳）
            if (review.AccTime == null || review.AccTime == 0)
            {
                review.AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
            }

            // 初始点赞数为0
            if (review.Praise == null)
            {
                review.Praise = 0;
            }

            return await AddWithIntId(review);
        }

        /// <summary>
        /// 删除博客评论
        /// </summary>
        /// <param name="id">评论ID</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> DeleteReview(int id)
        {
            // 开启事务，删除评论及其所有回复
            var result = await Db.Ado.UseTranAsync(async () =>
            {
                // 先删除该评论的所有回复
                await Db.Deleteable<BlogReply>().Where(r => r.ReviewId == id).ExecuteCommandAsync();

                // 再删除评论
                return await DeleteById(id);
            });

            return result.IsSuccess;
        }

        /// <summary>
        /// 批量删除博客评论
        /// </summary>
        /// <param name="ids">评论ID列表</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> BatchDeleteReviews(int[] ids)
        {
            if (ids == null || ids.Length == 0)
            {
                return false;
            }

            // 开启事务，批量删除评论及其所有回复
            var result = await Db.Ado.UseTranAsync(async () =>
            {
                // 先删除这些评论的所有回复
                await Db.Deleteable<BlogReply>().Where(r => ids.Contains(r.ReviewId)).ExecuteCommandAsync();

                // 再删除评论
                object[] objIds = Array.ConvertAll(ids, id => (object)id);
                return await DeleteByIds(objIds);
            });

            return result.IsSuccess;
        }

        /// <summary>
        /// 增加点赞数
        /// </summary>
        /// <param name="id">评论ID</param>
        /// <returns>是否增加成功</returns>
        public async Task<bool> IncrementPraiseCount(int id)
        {
            var review = await QueryById(id);
            if (review == null)
            {
                return false;
            }

            // 增加点赞数
            if (review.Praise == null)
            {
                review.Praise = 1;
            }
            else
            {
                review.Praise = (short)(review.Praise + 1);
            }

            return await Update(review);
        }

        /// <summary>
        /// 获取博客文章的评论数量
        /// </summary>
        /// <param name="blogId">博客文章ID</param>
        /// <returns>评论数量</returns>
        public async Task<int> GetBlogReviewCount(int blogId)
        {
            return await Db.Queryable<BlogReview>()
                .Where(r => r.AId == blogId)
                .CountAsync();
        }

        #region 回复相关方法

        /// <summary>
        /// 获取评论的回复列表
        /// </summary>
        /// <param name="reviewId">评论ID</param>
        /// <returns>回复列表</returns>
        public async Task<List<BlogReply>> GetRepliesByReviewId(int reviewId)
        {
            return await Db.Queryable<BlogReply>()
                .Where(r => r.ReviewId == reviewId)
                .OrderBy(r => r.ReplyTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取回复列表，支持搜索和分页
        /// </summary>
        /// <param name="queryRequest">查询请求参数</param>
        /// <returns>分页后的回复列表</returns>
        public async Task<PageModel<BlogReply>> GetReplyList(BlogReplyQueryRequest queryRequest)
        {
            try
            {
                var query = Db.Queryable<BlogReply>();

                // 按评论ID筛选
                if (queryRequest.ReviewId.HasValue)
                {
                    query = query.Where(r => r.ReviewId == queryRequest.ReviewId.Value);
                }

                // 按是否管理员回复筛选
                if (queryRequest.IsAdminOnly.HasValue)
                {
                    query = query.Where(r => r.IsAdmin == queryRequest.IsAdminOnly.Value);
                }

                // 搜索条件：回复内容或回复者姓名包含关键词
                if (!string.IsNullOrEmpty(queryRequest.Keyword))
                {
                    query = query.Where(r =>
                        r.Content.Contains(queryRequest.Keyword) || r.ReplyName.Contains(queryRequest.Keyword));
                }

                // A设置排序
                if (string.IsNullOrEmpty(queryRequest.OrderByFileds))
                {
                    // 默认按回复时间升序排序（老回复在前）
                    query = query.OrderBy(x => x.ReplyTime);
                }
                else
                {
                    // 使用自定义排序
                    query = query.OrderByIF(!string.IsNullOrEmpty(queryRequest.OrderByFileds),
                        queryRequest.OrderByFileds);
                }

                // 查询分页数据
                var result = await query.ToPageListAsync(queryRequest.PageIndex, queryRequest.PageSize);
                var total = await query.CountAsync();

                return new PageModel<BlogReply>
                {
                    page = queryRequest.PageIndex,
                    dataCount = (int)total,
                    PageSize = queryRequest.PageSize,
                    data = result
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查询回复列表时出错: {ex.Message}");
                // 返回空结果而不是抛出异常
                return new PageModel<BlogReply>
                {
                    page = queryRequest.PageIndex,
                    PageSize = queryRequest.PageSize,
                    dataCount = 0,
                    data = new List<BlogReply>()
                };
            }
        }

        /// <summary>
        /// 根据ID获取回复详情
        /// </summary>
        /// <param name="id">回复ID</param>
        /// <returns>回复实体</returns>
        public async Task<BlogReply> GetReplyById(int id)
        {
            return await _replyRepository.QueryById(id);
        }

        /// <summary>
        /// 添加回复
        /// </summary>
        /// <param name="reply">回复实体</param>
        /// <returns>新增回复的ID</returns>
        public async Task<long> AddReply(BlogReply reply)
        {
            // 设置回复时间
            reply.ReplyTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);

            return await _replyRepository.AddWithIntId(reply);
        }

        /// <summary>
        /// 删除回复
        /// </summary>
        /// <param name="id">回复ID</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> DeleteReply(int id)
        {
            return await _replyRepository.DeleteById(id);
        }

        /// <summary>
        /// 批量删除回复
        /// </summary>
        /// <param name="ids">回复ID列表</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> BatchDeleteReplies(int[] ids)
        {
            if (ids == null || ids.Length == 0)
            {
                return false;
            }

            object[] objIds = Array.ConvertAll(ids, id => (object)id);
            return await _replyRepository.DeleteByIds(objIds);
        }

        /// <summary>
        /// 更新回复内容
        /// </summary>
        /// <param name="id">回复ID</param>
        /// <param name="content">新的回复内容</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateReplyContent(int id, string content)
        {
            var reply = await _replyRepository.QueryById(id);
            if (reply == null)
            {
                return false;
            }

            reply.Content = content;
            // 不更新回复时间

            return await _replyRepository.Update(reply);
        }

        #endregion
    }
}