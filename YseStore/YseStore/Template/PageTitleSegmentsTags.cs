using Fluid.Ast;
using Fluid;
using Microsoft.AspNetCore.Html;
using System.Text.Encodings.Web;

namespace YseStore.Template;

internal static class PageTitleSegmentsTags
{
    public static async ValueTask<Completion> WritePageTitleAsync(IReadOnlyList<FilterArgument> arguments<PERSON>ist, TextWriter writer, TextEncoder encoder, TemplateContext context)
    {
        var arguments = new NamedExpressionList(argumentsList);

        var titleBuilder = await AddSegmentInternalAsync(arguments, context);

        var separatorExpression = arguments["separator", 2];
        var separator = separatorExpression == null ? null : new HtmlString((await separatorExpression.EvaluateAsync(context)).ToStringValue());

        titleBuilder.GenerateTitle(separator).WriteTo(writer, (HtmlEncoder)encoder);
        return Completion.Normal;
    }

    public static async ValueTask<Completion> WriteAddSegmentAsync(IReadOnlyList<FilterArgument> argumentsList, TextWriter _1, TextEncoder _2, TemplateContext context)
    {
        var arguments = new NamedExpressionList(argumentsList);

        await AddSegmentInternalAsync(arguments, context);

        return Completion.Normal;
    }

    private static async Task<IPageTitleBuilder> AddSegmentInternalAsync(NamedExpressionList arguments, TemplateContext context)
    {
        var services = ((LiquidTemplateContext)context).Services;

        var titleBuilder = services.GetRequiredService<IPageTitleBuilder>();

        var segmentExpression = arguments["segment", 0] ?? throw new ArgumentException("page_title tag requires a segment argument");
        var segment = (await segmentExpression.EvaluateAsync(context)).ToStringValue();

        var positionExpression = arguments["position", 1];
        var position = positionExpression == null ? "0" : (await positionExpression.EvaluateAsync(context)).ToStringValue();

        titleBuilder.AddSegment(new HtmlString(segment), position);

        return titleBuilder;
    }
}


public interface IPageTitleBuilder
{
    /// <summary>
    /// Sets a fixed title that will be used instead of any segmented titles added later.
    /// Can be cleared with <see cref="Clear()"/>.
    /// </summary>
    void SetFixedTitle(IHtmlContent title);

    /// <summary>
    /// Clears the current list of segments.
    /// </summary>
    void Clear();

    /// <summary>
    /// Adds a segment to the title.
    /// </summary>
    /// <param name="segment">A segments to add at the specific location in the title.</param>
    /// <param name="position">The position, defaults to 0.</param>
    void AddSegment(IHtmlContent segment, string position = "0");

    /// <summary>
    /// Concatenates every title segments using the separator defined in settings.
    /// </summary>
    /// <param name="separator">The html string that should separate all segments.</param>
    /// <returns>A string representing the aggregate title for the current page.</returns>
    IHtmlContent GenerateTitle(IHtmlContent separator);
}

public static class PageTitleBuilderExtensions
{
    /// <summary>
    /// Concatenates every title segments using the separator defined in settings.
    /// </summary>
    /// <returns>A string representing the aggregate title for the current page.</returns>
    public static IHtmlContent GenerateTitle(this IPageTitleBuilder builder)
    {
        return builder.GenerateTitle(null);
    }
}