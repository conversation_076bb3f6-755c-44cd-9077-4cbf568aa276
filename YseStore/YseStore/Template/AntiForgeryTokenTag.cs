using Fluid.Ast;
using Fluid;
using Microsoft.AspNetCore.Antiforgery;
using System.Text.Encodings.Web;

namespace YseStore.Template;

public class AntiForgeryTokenTag
{
    public static ValueTask<Completion> WriteToAsync(TextWriter writer, TextEncoder encoder, TemplateContext context)
    {
        var services = ((LiquidTemplateContext)context).Services;

        var antiforgery = services.GetRequiredService<IAntiforgery>();
        var httpContextAccessor = services.GetRequiredService<IHttpContextAccessor>();

        var httpContext = httpContextAccessor.HttpContext;
        var tokenSet = antiforgery.GetAndStoreTokens(httpContext);

        writer.Write("<input name=\"");
        writer.Write(encoder.Encode(tokenSet.FormFieldName));
        writer.Write("\" type=\"hidden\" value=\"");
        writer.Write(encoder.Encode(tokenSet.RequestToken));
        writer.Write("\" />");

        return ValueTask.FromResult(Completion.Normal);
    }
}
