namespace YseStore.Views.Components;

public class PaginationViewComponent : ViewComponent
{
    public IViewComponentResult Invoke(
        int totalItems,
        int currentPage = 1,
        int pageSize = 10,
        int spread = 2,
        string pageParamName = "page",
        string cssClass = "pagination",
        string activeClass = "active",
        string disabledClass = "disabled")
    {
        var totalPages = (int)Math.Ceiling(totalItems / (double)pageSize);

        var model = new PaginationViewModel
        {
            CurrentPage = currentPage,
            TotalPages = totalPages,
            PageSize = pageSize,
            Spread = spread,
            PageParamName = pageParamName,
            CssClass = cssClass,
            ActiveClass = activeClass,
            DisabledClass = disabledClass,
            QueryParams = HttpContext.Request.Query
                .Where(q => q.Key != pageParamName)
                .ToDictionary(q => q.Key, q => q.Value.ToString())
        };

        return View(model);
    }
}


public class PaginationViewModel
{
    // 基础参数
    public int CurrentPage { get; set; }
    public int TotalPages { get; set; }
    public int PageSize { get; set; }

    // 显示控制
    public int Spread { get; set; } // 左右显示页数
    public bool ShowEllipsis { get; set; } = true;

    // 样式参数
    public string CssClass { get; set; }
    public string ActiveClass { get; set; }
    public string DisabledClass { get; set; }

    // URL参数
    public string PageParamName { get; set; }
    public Dictionary<string, string> QueryParams { get; set; }

    // 生成页码逻辑
    public IEnumerable<int> GetPageNumbers()
    {
        var start = Math.Max(1, CurrentPage - Spread);
        var end = Math.Min(TotalPages, CurrentPage + Spread);

        if (start > 1) yield return 1;
        if (start > 2 && ShowEllipsis) yield return -1; // -1表示省略号

        for (var i = start; i <= end; i++) yield return i;

        if (end < TotalPages - 1 && ShowEllipsis) yield return -1;
        if (end < TotalPages) yield return TotalPages;
    }
}