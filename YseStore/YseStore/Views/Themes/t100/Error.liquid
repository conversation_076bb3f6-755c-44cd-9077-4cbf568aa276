{% if  ViewData["ErrorCode"]=="404" %}
<div id="page-content">
    <!--Body Container-->
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5 mt-5">
                <div class="error-content">
                    <img src="/assets/images/404_art.jpg" alt="404_art.jpg" style="max-width:300px">
                    <div class="page-title"><h1>404 Page Not Found</h1></div>
                    <p>The page you requested does not exist.</p>
                    <p><a href="/collections" class="btn btn--has-icon-after">Continue shopping</a></p>
                </div>
            </div>
        </div>
    </div><!--End Body Container-->
</div>

{% elsif ViewData["ErrorCode"]=="500" %}
<div id="page-content">
    <!--Body Container-->
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5 mt-5">
                <div class="error-content">
                    <img src="/assets/images/404_art.jpg" alt="404_art.jpg" style="max-width:300px">
                    <div class="page-title"><h1>{{ViewData["ErrorCode"]}}</h1></div>
                    <p>The page you requested does not exist.</p>
                    <p><a href="/collections" class="btn btn--has-icon-after">Continue shopping</a></p>
                </div>
            </div>
        </div>
    </div><!--End Body Container-->
</div>
{% else %}
<div id="page-content">
    <!--Body Container-->
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5 mt-5">
                <div class="error-content">
                    <img src="/assets/images/404_art.jpg" alt="404_art.jpg" style="max-width:300px">
                    <div class="page-title"><h1>{{ViewData["ErrorCode"]}}</h1></div>
                    <p>The page you requested does not exist.</p>
                    <p><a href="/collections" class="btn btn--has-icon-after">Continue shopping</a></p>
                </div>
            </div>
        </div>
    </div><!--End Body Container-->
</div>

{% endif %}