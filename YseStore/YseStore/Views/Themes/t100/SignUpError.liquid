<form method="post" hx-post="/Account/UserSignUp" accept-charset="UTF-8" class="customer-form text-start">
    <div id="response" hx-target="this" hx-swap="outerHTML">
 
        <p class="my-3">Email Address:</p>
        <div class="form-group">
            <label for="CustomerEmail" class="d-none">Email Address <span class="required">*</span></label>
            <input id="CustomerEmail" type="email" name="Email" placeholder="Email" class="form-control"  value="{{Model.Email}}" 
                   onkeyup="this.setCustomValidity(''); $(this).removeClass('is-invalid');"
                   hx-on:htmx:validation:validate="if(this.value == '') {
         this.setCustomValidity('Please enter your email')
            $(this).addClass('is-invalid') }else{$(this).removeClass('is-invalid')}" />
        </div>
        <p class="my-3">Password:</p>
        <div class="form-group">
            <label for="CustomerPassword" class="d-none"><span class="required">*</span></label>
            <input id="CustomerPassword" type="password" name="Password" class="form-control" placeholder="Password"
                   onkeyup="this.setCustomValidity(''); $(this).removeClass('is-invalid');"
                   hx-on:htmx:validation:validate="if(this.value == '') {
         this.setCustomValidity('Please enter your password')
            $(this).addClass('is-invalid') }else{$(this).removeClass('is-invalid')}" />
        </div>
        <p class="my-3">Confirm Password:</p>
        <div class="form-group">
            <label for="ReEnterPassword" class="d-none"><span class="required">*</span></label>
            <input id="ReEnterPassword" type="password" name="ReEnterPassword" class="form-control" placeholder="Confirm Password"
                   onkeyup="this.setCustomValidity(''); $(this).removeClass('is-invalid');"
                   hx-on:htmx:validation:validate="if(this.value == '') {
this.setCustomValidity('Please enter your Confirm password')
   $(this).addClass('is-invalid') }else{$(this).removeClass('is-invalid')}" />
        </div>

        <div class=" form-group">
            <input class="d-inline-block" type="checkbox" value="true" id="Agree" name="Agree">
            <label class="form-check-label d-inline-block" for="Agree">
                I agree with the <a href="#">Terms Of Service.</a>
            </label>
        </div>
        <div class="d-grid">
            <button type="submit"
                    class="btn rounded  mb-3">
                <i class="far fa-sign-in"></i> Create
                <span id="spinner" class="htmx-indicator">
                    <i class="icon an an-spinner an-spinner-l"></i>
                </span>
            </button>
        </div>
    </div>
</form>
