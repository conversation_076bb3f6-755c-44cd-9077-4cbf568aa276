<div class="user-area bg pt-100 pb-80">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 mb-4 user-sidebar">
                {% assign sidebar= '/Themes/'| append: theme | append:'/Account/Sidebar' %}
                {% include sidebar -%}
                {% assign orderStatus = Model.OrderStatus | default: 0 %}
            </div>
            <!--Main Content-->
            <div class="col-12 col-sm-9 col-md-9 col-lg-9 mb-5 user-content">
                <div class="orders-vue pb-5">
                    <div class="orders-container">
                        <div class="user-title page-main-title page-orders-title">
                            <h1 class="user-title__text">{{ "user.account.orderTitle"|translate}}</h1>
                        </div>
                        <div class="user-program orders-program">

                            <div class="row justify-content-center">
                                <div class="col-12 col-md-6">
                                    <div class="orders-search mt-5 mb-4">
                                        <div class="form minisearch search-inline" id="header-search1" action="#" method="get">
                                            <label class="label d-none"><span>{{ "blog.global.searchBtn"|translate}}</span></label>
                                            <div class="control">
                                                <div class="searchField d-flex">
                                                    <div class="input-box d-flex w-100">
                                                        <input type="text" id="search-keyword" value="{{ Model.Keyword }}" placeholder="{{ "blog.global.searchBtn"|translate}}..." class="input-text rounded-0 border-end-0 ">
                                                        <button type="submit" onclick="searchOrder()" title="{{ "blog.global.searchBtn"|translate}}" class="action search"><i class="icon an an-search-l"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="points-list">
                                <div class="tabs-listing">
                                    <ul class="coupon-tabs product-tabs list-unstyled d-flex-wrap border-bottom m-0 d-none d-md-flex"  id="OrderStatus">

                                      
                                       <li class="active" onclick="updateOrderStatus(0)"><a class="tablink">{{ "user.points.all"|translate}}</a></li>
                                                {% if Model.statusList != null and Model.statusList.size > 0 %}
                                                    {% for item in Model.statusList %}
                                                        <li class="{% if item.Value==orderStatus %}active{% endif %}" onclick="updateOrderStatus({{item.Value}})">
                                                            <a class="tablink"> {{ 'user.account.OrderStatusAry_'|append:item.Value | translate }}
                                                            </a>
                                                        </li>
                                                    {% endfor %}
                                                {% endif %}

                                       
                                    </ul>
                                    <div class="tab-container">
                                       
                                        <div  class="tab-all">
                                            <div class="mb-4">
                                                <div class="table-responsive order-table mt-4">
                                                    <table class="table table-bordered table-hover align-middle text-center mb-0">
                                                        <thead class="alt-font">
                                                            <tr>
                                                                <th>#{{ "user.account.order_no"|translate}}</th>
                                                                <th>{{ "user.account.orderDate"|translate}}</th>
                                                                <th>{{ "user.account.order_status"|translate}}</th>
                                                                <th>{{ "user.account.order_total"|translate}}</th>
                                                                <th>{{ "web.global.action"|translate}}</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                          {% if Model.MyOrders != null and Model.MyOrders.size > 0 %}
                                                            {% for orders in Model.MyOrders %}
                                                            <tr class="closestOrder">
                                                                <td>#{{ orders.OId }}</td>
                                                                <td>{{ orders.OrderTimeStr }}</td>
                                                                <td>
                                                                  {% if orders.OrderStatus == 1 %}
                                                                    <span class="badge badge-info">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                                    {% elsif orders.OrderStatus == 2 %}
                                                                    <span class="badge badge-primary">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                                    {% elsif orders.OrderStatus == 3 %}
                                                                    <span class="badge badge-danger">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                                    {% elsif orders.OrderStatus == 4 %}
                                                                    <span class="badge badge-success">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                                    {% elsif orders.OrderStatus == 5 %}
                                                                    <span class="badge badge-success">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                                    {% elsif orders.OrderStatus == 6 %}
                                                                    <span class="badge badge-success">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                                    {% elsif orders.OrderStatus == 7 %}
                                                                    <span class="badge badge-danger">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                                  {% endif %}
                                                                </td>
                                                                <td>{{ orders.OrderSymbol }}{{ orders.OrderSum }} </td>
                                                                <td>
                                                                <a class="link-underline view" href="/Account/OrderDetail/{{ orders.OrderId }}">{{ "user.account.order_details"|translate}}</a>
                                                                  <!-- 确认收货 -->
                                                                 {% if orders.OrderStatus == 5  %}
                                                                    <a class="text-success confirm_receiving"   value="{{ orders.OrderId }}">{{ "user.account.receiving"|translate}}</a>
                                                                 {% endif %}
                                                                
                                                                 <!-- 取消订单 -->
                                                                {% if orders.OrderStatus == 1  %}
                                                                    <a class="text-danger cancel_order" value="{{ orders.OrderId }}" >{{ "user.account.cancel_order"|translate}}</a><br />
                                                                {% endif %}
                                                                   <!-- 支付订单 -->
                                                                {% if orders.OrderStatus == 1 or orders.OrderStatus == 3 %}
                                                                    <a class="text-success rounded_order" href="/cart/{{ orders.OId }}/info?code={{ orders.OIdToBase64}}">{{ "checkout.global.paynow"|translate}}</a>
                                                                {% endif %}

                                                                <a class="link-underline view" href="/Account/MyContact/{{ orders.OrderId }}">{{ "web.global.contact"|translate}}</a>
                                                                </td>
                                                            </tr>
                                                                {% endfor %}
                                                            {% endif %}



                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>

                                              <div class="pagination">
                                                    <ul>
                                                        {% if Model.CurrentPage > 1 %}
                                                            <li class="prev"><a
                                                                        href="/Account/MyOrders{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ Model.CurrentPage | minus: 1 }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}"><i
                                                                            class="an an-lg an-angle-left" aria-hidden="true"></i></a></li>
                                                        {% else %}
                                                            <li class="prev disabled"><a href="#"><i class="an an-lg an-angle-left"
                                                                                                     aria-hidden="true"></i></a></li>
                                                        {% endif %}

                                                        {% assign startPage = Model.CurrentPage | minus: 2 %}
                                                        {% if startPage < 1 %}{% assign startPage = 1 %}{% endif %}

                                                        {% assign endPage = startPage | plus: 4 %}
                                                        {% if endPage > Model.TotalPages %}{% assign endPage = Model.TotalPages %}{% endif %}

                                                        {% if startPage > 1 %}
                                                            <li>
                                                                <a href="/Account/MyOrders{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page=1{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">1</a>
                                                            </li>
                                                            {% if startPage > 2 %}
                                                                <li class="disabled"><a href="#">...</a></li>{% endif %}
                                                        {% endif %}

                                                        {% for i in (startPage..endPage) %}
                                                            <li {% if i == Model.CurrentPage %}class="active"{% endif %}>
                                                                <a href="/Account/MyOrders{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ i }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">{{ i }}</a>
                                                            </li>
                                                        {% endfor %}

                                                        {% if endPage < Model.TotalPages %}
                                                            {% assign lastPageMinusOne = Model.TotalPages | minus: 1 %}
                                                            {% if endPage < lastPageMinusOne %}
                                                                <li class="disabled"><a href="#">...</a></li>{% endif %}
                                                            <li>
                                                                <a href="/Account/MyOrders{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ Model.TotalPages }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">{{ Model.TotalPages }}</a>
                                                            </li>
                                                        {% endif %}

                                                        {% if Model.CurrentPage < Model.TotalPages %}
                                                            <li class="next"><a
                                                                        href="/Account/MyOrders{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ Model.CurrentPage | plus: 1 }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}"><i
                                                                            class="an an-lg an-angle-right" aria-hidden="true"></i></a></li>
                                                        {% else %}
                                                            <li class="next disabled"><a href="#"><i class="an an-lg an-angle-right"
                                                                                                     aria-hidden="true"></i></a></li>
                                                        {% endif %}
                                                    </ul>
                                                </div>

                                        </div>
                                        
                                    </div>
                                </div>
                            </div>


                        </div>

                    </div>
                </div>
            </div>
            <!--End Main Content-->
        </div>
    </div>
</div>



<!-- 引入自定义弹窗JS库 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<script>
        // 搜索函数
    function searchOrder() {
        const keyword = document.getElementById('search-keyword').value;
        const currentUrl = new URL(window.location.href);
        const params = new URLSearchParams(currentUrl.search);
    
        if (keyword) {
            params.set('keyword', encodeURIComponent(keyword));
        } else {
            params.delete('keyword'); // 清除空关键词
        }
    params.delete('page');
        const newUrl = new URL(currentUrl.pathname, window.location.origin);
        newUrl.search = params.toString();
    
        window.location.href=newUrl.href;
    }
    
    // 为搜索输入框添加回车键搜索功能
    document.getElementById('search-keyword').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            searchOrder();
        }
    });
  
       function updateOrderStatus(orderStatus) {
        // 获取选中的订单状态值
      
        // 解析当前URL参数
        const currentUrl = new URL(window.location.href);
        const params = new URLSearchParams(currentUrl.search);
    
        // 更新或添加OrderStatus参数
        params.set('OrderStatus', orderStatus);
        params.delete('page');
        // 构建新URL
        const newUrl = new URL(currentUrl.pathname, window.location.origin);
        newUrl.search = params.toString();
   
       window.location.href=newUrl.href;

    }


    // 原有代码保持不变，在文档底部添加以下内容
    document.addEventListener('DOMContentLoaded', function() {
        // 获取URL参数中的OrderStatus
        const urlParams = new URLSearchParams(window.location.search);
        const orderStatus = urlParams.get('OrderStatus');
 
        if (orderStatus) {
              const orderStatusUl = document.getElementById('OrderStatus');
        if (orderStatusUl) {
            // 移除所有 li 的 active 类
            const liElements = orderStatusUl.querySelectorAll('li');
            liElements.forEach(li => li.classList.remove('active'));
 
            // 找到对应的 li 并添加 active 类
            const targetLi = orderStatusUl.querySelector(`li[onclick*="updateOrderStatus(${orderStatus})"]`);
            if (targetLi) {
                targetLi.classList.add('active');
            }
        }
        }



    // 监听表单提交事件
    $('.cancel_order').click(function(e){
        e.preventDefault();
        var form = $(this);
        var formData = {
            OrderId: form.attr('value')
        };
        
          const productItem = this.closest('.closestOrder');
           const badgeElement = productItem.querySelector('.badge-info');
           const cancelElement = productItem.querySelector('.cancel_order');
           const roundedElement = productItem.querySelector('.rounded_order');
          customize_pop.confirm('{{'user.account.cancelOrder'|translate}}', function() {



        sendData(formData);
        function sendData(data) {
            $.ajax({
                url: '/api/account/comment/cancelorder',
                type: 'POST',
                data: data,
                success: function(response) {
                    if(response.success) {
                       customize_pop.success('{{'web.global.submit_success'|translate}}');
                       // 先获取元素
                       
    
                        // 修改文本内容
                        badgeElement.textContent = '{{ 'user.account.OrderStatusAry_'|append:7 | translate }}';  // 设置显示的文本
    
                        // 修改样式类（推荐方式）
                        badgeElement.classList.remove('badge-info');
                        badgeElement.classList.add('badge-danger');
                        cancelElement.style.display = 'none'; 
                        roundedElement.style.display = 'none'; 
                    } else {
                        
                          // 显示错误消息
                            customize_pop.error(response.message || 'Failed to cancel item. Please try again');
                    }
                },
                error: function(xhr) {
                    console.error('AJAX Error:', xhr.responseText);
                     customize_pop.error('An error occurred. Please try again later');
                }
            });
        }

          }, null, '{{'web.global.confirm'|translate}}', '{{'web.global.cancel'|translate}}', '{{'web.global.confirm'|translate}}', false);

    });


    
      // 确认收货
    $('.confirm_receiving').click(function(e){
        e.preventDefault();
        var form = $(this);
        var formData = {
            OrderId: form.attr('value')
        };
        
          const productItem = this.closest('.closestOrder');
           const badgeElement = productItem.querySelector('.badge-success');
           const cancelElement = productItem.querySelector('.confirm_receiving');
          customize_pop.confirm('{{'user.account.sure'|translate}}', function() {



        sendData(formData);
        function sendData(data) {
            $.ajax({
                url: '/api/account/comment/receiving',
                type: 'POST',
                data: data,
                success: function(response) {
                    if(response.success) {
                       customize_pop.success('{{'web.global.submit_success'|translate}}');
                       // 先获取元素
                        // 修改文本内容
                        badgeElement.textContent = '{{ 'user.account.OrderStatusAry_'|append:6 | translate }}';  // 设置显示的文本
    
                        // 修改样式类（推荐方式）
                       
                        cancelElement.style.display = 'none'; 
                    } else {
                        
                          // 显示错误消息
                            customize_pop.error(response.message || 'Failed to sure item. Please try again');
                    }
                },
                error: function(xhr) {
                    console.error('AJAX Error:', xhr.responseText);
                     customize_pop.error('An error occurred. Please try again later');
                }
            });
        }

          }, null, '{{'web.global.confirm'|translate}}', '{{'web.global.cancel'|translate}}', '{{'web.global.confirm'|translate}}', false);

    });






 });









</script>
