{% section meta_keywords -%}
   {% if Model.Product.ProductSeo and Model.Product.ProductSeo.SeoKeyword_en and Model.Product.ProductSeo.SeoKeyword_en != "" %}
   <meta name="keywords" content="{{ Model.Product.ProductSeo.SeoKeyword_en }}">
   {% elsif Model.Product.Tags and Model.Product.Tags.size > 0 %}
   <meta name="keywords" content="{% for tag in Model.Product.Tags %}{{ tag.Name }}{% unless forloop.last %}, {% endunless %}{% endfor %}, {{ Model.Product.ProductName }}">
   {% else %}
   <meta name="keywords" content="{{ Model.Product.ProductName }}, {{ Model.Product.BrandName }}">
   {% endif %}
  {% endsection -%}
  {% section meta_description -%}
   {% if Model.Product.ProductSeo and Model.Product.ProductSeo.SeoDescription_en and Model.Product.ProductSeo.SeoDescription_en != "" %}
   <meta name="description" content="{{ Model.Product.ProductSeo.SeoDescription_en }}">
   {% elsif Model.Product.BriefDescription and Model.Product.BriefDescription != "" %}
   <meta name="description" content="{{ Model.Product.BriefDescription }}">
   {% else %}
   {% endif %}
  {% endsection -%}
  {% section title -%}
   {% if Model.Product.ProductSeo and Model.Product.ProductSeo.SeoTitle_en and Model.Product.ProductSeo.SeoTitle_en != "" %}
   <title>{{ Model.Product.ProductSeo.SeoTitle_en }}</title>
   {% else %}
   <title>{{ Model.Product.ProductName }}</title>
   {% endif %}
  {% endsection -%}

<div class="offcanvas-wrapper">
    <!-- Start Page Title -->
    <div class="page-title">
        <div class="container">
            <div class="column">
                <h1>Single Produc</h1>
            </div>
            <div class="column">
                <ul class="breadcrumbs">
                    <li><a href="/">{{ "web.global.home"|translate}}</a></li>
                    <li class="separator">&nbsp;</li>
                    <li><a href="/collections">Shop </a></li>
                    <li class="separator">&nbsp;</li>
                    <li>Single Product</li>
                </ul>
            </div>
        </div>
    </div>
    <!-- End Page Title -->
    <!-- Start Product Content -->
    <div class="container padding-top-1x padding-bottom-3x">
        <div class="row">
            <!-- Start Product Gallery -->
            <div class="col-md-6">
                <div class="product-gallery">
                    <div class="product-single product-labels rectangular"><span class="lbl on-sale">Sales</span><span class="lbl pr-label1">New</span></div>
                    <div class="gallery-wrapper">
                        <div class="gallery-item active"><a href="{{static_path}}/assets/images/shop/products/sv1.jpg" data-hash="one" data-size="1000x667"></a></div>
                        <div class="gallery-item"><a href="{{static_path}}/assets/images/shop/products/sv2.jpg" data-hash="two" data-size="1000x667"></a></div>
                        <div class="gallery-item"><a href="{{static_path}}/assets/images/shop/products/sv3.jpg" data-hash="three" data-size="1000x667"></a></div>
                        <div class="gallery-item"><a href="{{static_path}}/assets/images/shop/products/sv4.webp" data-hash="four" data-size="1000x667"></a></div>
                        <div class="gallery-item"><a href="{{static_path}}/assets/images/shop/products/sv5.webp" data-hash="five" data-size="1000x667"></a></div>
                    </div>
                    <div class="product-single-img" style="position:relative;">
                        <div class="product-carousel owl-carousel">
                            <div data-hash="one"><img src="{{static_path}}/assets/images/shop/products/sv1.jpg" alt="Product"></div>
                            <div data-hash="two"><img src="{{static_path}}/assets/images/shop/products/sv2.jpg" alt="Product"></div>
                            <div data-hash="three"><img src="{{static_path}}/assets/images/shop/products/sv3.jpg" alt="Product"></div>
                            <div data-hash="four"><img src="{{static_path}}/assets/images/shop/products/sv4.webp" alt="Product"></div>
                            <div data-hash="five"><img src="{{static_path}}/assets/images/shop/products/sv5.webp" alt="Product"></div>
                        </div>
                        <div class="product-labels product-labels-img" style="background-image:url({{static_path}}/assets/images/shop/products/product-bg.png)"></div>
                    </div>
                    <!--<ul class="product-thumbnails">-->
                    <ul class="product-thumbnails owl-carousel" data-owl-carousel='{ "nav": true, "dots": false, "loop": true, "margin": 30, "autoplay": false, "responsive": {"0":{"items":3},"630":{"items":3},"991":{"items":3},"1200":{"items":5}} }'>
                        <li class="active"><a href="#one"><img src="{{static_path}}/assets/images/shop/single/th01.jpg" alt="Product"></a></li>
                        <li><a href="#two"><img src="{{static_path}}/assets/images/shop/single/th02.jpg" alt="Product"></a></li>
                        <li><a href="#three"><img src="{{static_path}}/assets/images/shop/single/th03.jpg" alt="Product"></a></li>
                        <li><a href="#four"><img src="{{static_path}}/assets/images/shop/single/th04.jpg" alt="Product"></a></li>
                        <li><a href="#five"><img src="{{static_path}}/assets/images/shop/single/th05.jpg" alt="Product"></a></li>
                    </ul>
                </div>
            </div>
            <!-- End Product Gallery -->
            <!-- Start Product Info -->
            <div class="col-md-6 single-shop">
                <div class="hidden-md-up"></div>
                <div class="rating-stars">
                    <i class="icon-star filled"></i>
                    <i class="icon-star filled"></i>
                    <i class="icon-star filled"></i>
                    <i class="icon-star filled"></i>
                    <i class="icon-star filled"></i>
                </div>
                <a class="review-label"  href="#tablist" style="text-decoration: none;"><span class="text-muted align-middle">&nbsp;&nbsp;5 | 13 {{ "products.goods.reviews" | translate }}</span></a>
                
                <h2 class="padding-top-1x text-normal with-side">iPhone X Gold 128GB</h2>
                <span class="h2 d-block with-side"><del class="text-muted text-normal">$899.00</del>&nbsp; $749.60<span class="product-badge text-danger">20% Off</span></span>
                <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five ...</p>
                <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five ...</p>
                <div class="row margin-top-1x">
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label for="size">Memory Size</label>
                            <select class="form-control" id="size">
                                <option>Chooze Size</option>
                                <option>16GB</option>
                                <option>32GB</option>
                                <option>64GB</option>
                                <option>128GB</option>
                                <option>256GB</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-5">
                        <div class="form-group">
                            <label for="color">Choose Color</label>
                            <select class="form-control" id="color">
                                <option>White / Red / Blue</option>
                                <option>Black / Orange / Green</option>
                                <option>Gray / Purple / White</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label for="quantity">Quantity</label>
                            <select class="form-control" id="quantity">
                                <option>1</option>
                                <option>2</option>
                                <option>3</option>
                                <option>4</option>
                                <option>5</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="pt-1 mb-2"><span class="text-medium">SKU:</span> #17685932</div>
                <div class="padding-bottom-1x mb-2">
                    <span class="text-medium">Categories:&nbsp;</span>
                    <a class="navi-link" href="#">Apple,</a>
                    <a class="navi-link" href="#"> Smartphone,</a>
                    <a class="navi-link" href="#"> Mobile</a>
                </div>
            </div>
            <div class="col-md-12">
                <hr class="mt-30 mb-30">
                <div class="d-flex flex-wrap justify-content-between mb-30">
                    <div class="entry-share">
                        <span class="text-muted">Share:</span>
                        <div class="share-links">
                            <a class="social-button shape-circle sb-facebook" href="#" data-toggle="tooltip" data-placement="top" title="" data-original-title="Facebook">
                                <i class="socicon-facebook"></i>
                            </a>
                            <a class="social-button shape-circle sb-twitter" href="#" data-toggle="tooltip" data-placement="top" title="" data-original-title="Twitter">
                                <i class="socicon-twitter"></i>
                            </a>
                            <a class="social-button shape-circle sb-instagram" href="#" data-toggle="tooltip" data-placement="top" title="" data-original-title="Instagram">
                                <i class="socicon-instagram"></i>
                            </a>
                            <a class="social-button shape-circle sb-google-plus" href="#" data-toggle="tooltip" data-placement="top" title="" data-original-title="Google +">
                                <i class="socicon-googleplus"></i>
                            </a>
                        </div>
                    </div>
                    <div class="sp-buttons mt-2 mb-2">
                        <button class="btn btn-outline-secondary btn-sm btn-wishlist" data-toggle="tooltip" title="" data-original-title="Whishlist">
                            <i class="icon-heart"></i>
                        </button>
                        <button class="btn btn-primary" data-toast="" data-toast-type="success" data-toast-position="topRight" data-toast-icon="icon-circle-check" data-toast-title="Product" data-toast-message="successfuly added to cart!"><i class="icon-bag"></i> Add to Cart</button>
                    </div>
                </div>
            </div>
            <!-- End Product Info -->
        </div>
        <!-- Start Product Tabs -->
        <div class="col-md-12">
            <ul class="nav nav-tabs" role="tablist" id="tablist">
                <li class="nav-item" data-id="description"><a class="nav-link active" href="#description" data-toggle="tab" role="tab">Description</a></li>
                <li class="nav-item" data-id="reviews"><a class="nav-link" href="#reviews" data-toggle="tab" role="tab">Reviews</a></li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane fade show active" id="description" role="tabpanel">
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged.</p>
                    <p class="mb-30">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged.</p>
                </div>
                <div class="tab-pane fade" id="reviews" role="tabpanel">
                    <!-- Start Review #1 -->
                    <div class="comment">
                        <div class="comment-author-ava"><img src="{{static_path}}/assets/images/reviews/01.jpg" alt="Review Author"></div>
                        <div class="comment-body">
                            <div class="comment-header d-flex flex-wrap justify-content-between">
                                <h4 class="comment-title">Lorem Ipsum is simply dummy</h4>
                                <div class="mb-2">
                                    <div class="rating-stars"><i class="icon-star filled"></i><i class="icon-star filled"></i><i class="icon-star filled"></i><i class="icon-star filled"></i><i class="icon-star filled"></i></div>
                                </div>
                            </div>
                            <p class="comment-text">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries.</p>
                            <div class="comment-footer"><span class="comment-meta">John Doe</span></div>
                        </div>
                    </div>
                    <!-- End Review #1 -->
                    <!-- Start Review #2 -->
                    <div class="comment">
                        <div class="comment-author-ava"><img src="{{static_path}}/assets/images/reviews/02.jpg" alt="Review Author"></div>
                        <div class="comment-body">
                            <div class="comment-header d-flex flex-wrap justify-content-between">
                                <h4 class="comment-title">Lorem Ipsum is simply dummy</h4>
                                <div class="mb-2">
                                    <div class="rating-stars"><i class="icon-star filled"></i><i class="icon-star filled"></i><i class="icon-star filled"></i><i class="icon-star filled"></i><i class="icon-star filled"></i></div>
                                </div>
                            </div>
                            <p class="comment-text">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries.</p>
                            <div class="comment-footer"><span class="comment-meta">Julia Smith</span></div>
                        </div>
                    </div>
                    <!-- End Review #2 -->
                    <!-- Start Review #3 -->
                    <div class="comment">
                        <div class="comment-author-ava"><img src="{{static_path}}/assets/images/reviews/03.jpg" alt="Review Author"></div>
                        <div class="comment-body">
                            <div class="comment-header d-flex flex-wrap justify-content-between">
                                <h4 class="comment-title">Lorem Ipsum is simply dummy</h4>
                                <div class="mb-2">
                                    <div class="rating-stars"><i class="icon-star filled"></i><i class="icon-star filled"></i><i class="icon-star filled"></i><i class="icon-star filled"></i><i class="icon-star filled"></i></div>
                                </div>
                            </div>
                            <p class="comment-text">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries.</p>
                            <div class="comment-footer"><span class="comment-meta">Rick Armstrong</span></div>
                        </div>
                    </div>
                    <!-- End Review #3 -->
                    <!-- Start Review Form -->
                    <h5 class="mb-30 padding-top-1x">Leave Review</h5>
                    <form class="row" method="post">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label for="review_name">Your Name</label>
                                <input class="form-control form-control-rounded" type="text" id="review_name" required>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label for="review_email">Your Email</label>
                                <input class="form-control form-control-rounded" type="email" id="review_email" required>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label for="review_subject">Your Subject</label>
                                <input class="form-control form-control-rounded" type="text" id="review_subject" required>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label for="review_rating">Your Rate</label>
                                <select class="form-control form-control-rounded" id="review_rating">
                                    <option>5 Stars</option>
                                    <option>4 Stars</option>
                                    <option>3 Stars</option>
                                    <option>2 Stars</option>
                                    <option>1 Star</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label for="review_text">Review </label>
                                <textarea class="form-control form-control-rounded" id="review_text" rows="8" required></textarea>
                            </div>
                        </div>
                        <div class="col-12 text-right">
                            <button class="btn btn-outline-primary" type="submit">Submit Review</button>
                        </div>
                    </form>
                    <!-- End Review Form -->
                </div>
            </div>
        </div>
        <!-- End Product Tabs -->
        {% assign recommendproductslider = '/Themes/' | append: theme | append: '/Shop/RecommendProductSlider' %}
        {% include recommendproductslider, RecommendProducts: Model.RecommendProducts -%}
    </div>
    <!-- End Product Content -->
</div>
<!-- Start Photoswipe Container -->
<div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="pswp__bg"></div>
    <div class="pswp__scroll-wrap">
        <div class="pswp__container">
            <div class="pswp__item"></div>
            <div class="pswp__item"></div>
            <div class="pswp__item"></div>
        </div>
        <div class="pswp__ui pswp__ui--hidden">
            <div class="pswp__top-bar">
                <div class="pswp__counter"></div>
                <button class="pswp__button pswp__button--close" title="Close (Esc)"></button>
                <button class="pswp__button pswp__button--share" title="Share"></button>
                <button class="pswp__button pswp__button--fs" title="Toggle fullscreen"></button>
                <button class="pswp__button pswp__button--zoom" title="Zoom in/out"></button>
                <div class="pswp__preloader">
                    <div class="pswp__preloader__icn">
                        <div class="pswp__preloader__cut">
                            <div class="pswp__preloader__donut"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
                <div class="pswp__share-tooltip"></div>
            </div>
            <button class="pswp__button pswp__button--arrow--left" title="Previous (arrow left)"></button>
            <button class="pswp__button pswp__button--arrow--right" title="Next (arrow right)"></button>
            <div class="pswp__caption">
                <div class="pswp__caption__center"></div>
            </div>
        </div>
    </div>
</div>
<script>
    //跳转评论
    document.querySelector(".review-label").addEventListener('click', function () {
        document.querySelectorAll('.nav-tabs li').forEach(function (tab) {
            var dataId = tab.getAttribute('data-id');
            // 获取当前 tab 的 rel 属性
            var activeTab = tab.getAttribute('data-id');
            var activeContent = document.getElementById(activeTab);
            if (activeContent) {
                // 使用淡入效果（可选，简单实现）
                activeContent.style.opacity = 0;
                activeContent.style.display = 'block';
                setTimeout(function () {
                    activeContent.style.transition = 'opacity 0.3s';
                    activeContent.style.opacity = 1;
                }, 10);
            }
                document.querySelectorAll('.nav-tabs li a').forEach(function (li) {
                    li.classList.remove('active');
                });
            // 移除所有 tab 的 active 类
            if (dataId == "reviews") {
                var child = tab.querySelector('.nav-link');
                if (child) {
                    child.classList.add('active');
                    child.classList.add('show');
                }
                document.querySelector(".tab-content #description").classList.remove('show')
                document.querySelector(".tab-content #description").classList.remove('active')
                document.querySelector('.tab-content #description').style.display = 'none';
            }
        });
    })
    //tab切换
    document.querySelectorAll('#tablist .nav-item').forEach(function (tabItem) {
        tabItem.addEventListener('click', function (e) {
            e.preventDefault();
            // 1. 移除所有 tab 的 active 类
            document.querySelectorAll('#tablist .nav-link').forEach(function (link) {
                link.classList.remove('active');
                link.classList.remove('show');
            });

            // 2. 当前 tab 的 a 添加 active
            var link = tabItem.querySelector('.nav-link');
            if (link) link.classList.add('active'); link.classList.add('show');

            // 3. 隐藏所有 tab-pane
            document.querySelectorAll('.tab-content .tab-pane').forEach(function (pane) {
                pane.classList.remove('show', 'active');
                pane.style.display = 'none';
            });

            // 4. 显示当前 tab 对应的内容
            var tabId = tabItem.getAttribute('data-id');
            var pane = document.getElementById(tabId);
            if (pane) {
                pane.classList.add('show', 'active');
                pane.style.display = 'block';
            }
        });
    });
    


</script>