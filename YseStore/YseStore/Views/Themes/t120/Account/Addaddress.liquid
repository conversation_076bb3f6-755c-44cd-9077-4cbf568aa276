<div class="offcanvas-wrapper">
    <div class="offcanvas-wrapper">
        <!-- Start Page Title -->
        <div class="page-title">
            <div class="container">
                <div class="column">
                    <h1>
                    {% if Model.AId==0  %}
                        <span>{{ "web.global.add"|translate}}</span>
                    {% else %}
                        <span>{{ "web.global.edit"|translate}}</span>
                    {% endif %}
                    {% if Model.Billing==1  %}
                        <span>{{ "user.account.bill_addr"|translate}}</span>
                    {% else %}
                        <span>{{ "user.account.ship_addr"|translate}}</span>
                    {% endif %}
                    </h1>
                </div>
                <div class="column">
                    <ul class="breadcrumbs">
                        <li><a href="/">{{ "web.global.home"|translate}}</a></li>
                        <li class="separator">&nbsp;</li>
                        <li>
                        {% if Model.AId==0  %}
                            <span>{{ "web.global.add"|translate}}</span>
                        {% else %}
                            <span>{{ "web.global.edit"|translate}}</span>
                        {% endif %}
                        {% if Model.Billing==1  %}
                            <span>{{ "user.account.bill_addr"|translate}}</span>
                        {% else %}
                            <span>{{ "user.account.ship_addr"|translate}}</span>
                        {% endif %}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- Start Contacts & Shipping Address -->
        <div class="container padding-top-1x padding-bottom-3x">
            <div class="row">
                {% assign sidebar= '/Themes/'| append: theme | append:'/Account/Sidebar' %}
                {% include sidebar -%}
                <div class="col-lg-8">
                    <div class="padding-top-2x mt-2 hidden-lg-up"></div>
                    <div class="row cart-col-in">
                        <div class="col-5 col-lg-6">
                        <h3>
                       {% if Model.AId==0  %}
                            <span>{{ "web.global.add"|translate}}</span>
                        {% else %}
                            <span>{{ "web.global.edit"|translate}}</span>
                        {% endif %}
                        {% if Model.Billing==1  %}
                            <span>{{ "user.account.bill_addr"|translate}}</span>
                        {% else %}
                            <span>{{ "user.account.ship_addr"|translate}}</span>
                        {% endif %}
                        </h3>
                        </div>
                        <div class="col-7 col-lg-6" style="text-align:right;"><a href="/Account/MyAddress" class="btn btn-primary">{{ "user.account.addressTitle"|translate}} </a></div>
                    </div>
                    <hr class="padding-bottom-1x">
                    <form class="row" id="Addaddress">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="account-first-name">{{ "user.account.firstname"|translate}}</label>
                                <input class="form-control" name="FirstName" value="{{ Model.address.FirstName }}" type="text" id="account-first-name" placeholder="{{ "user.account.firstname"|translate}}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="account-last-name">{{ "user.account.lastname"|translate}}</label>
                                <input class="form-control" name="LastName" value="{{ Model.address.LastName }}" type="text" id="account-last-name" placeholder="{{ "user.account.lastname"|translate}}" required>
                            </div>
                        </div>


                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="account-country">{{ "user.account.country"|translate}}</label>
                                <select class="form-control CountryList" id="account-country">
                                   {% if Model.CountryList != null and Model.CountryList.size > 0 %}
                                        {% for item in Model.CountryList %}
                                            {% if item.Value == Model.address.CId %}
                                            <option selected value="{{ item.Value }}">{{ item.Name }}</option>
                                            {% else %}
                                            <option value="{{ item.Value }}">{{ item.Name }}</option>
                                        {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6 IsProvince"  style=" {% if Model.IsProvince %} display: block; {% else %} display: none;{% endif %}">
                            <div class="form-group">
                                <label for="account-city">{{ "user.account.province_state"|translate}}</label>
                                <select class="form-control ProvinceList" id="account-city">
                                    {% if Model.ProvinceList != null and Model.ProvinceList.size > 0 %}
                                        {% for item in Model.ProvinceList %}
                                        {% if item.SId == Model.address.SId %}
                                            <option selected value="{{ item.SId }}">{{ item.States }}</option>
                                            {% else %}
                                                <option value="{{ item.SId }}">{{ item.States }}</option>
                                        {% endif %}

                                        {% endfor %}
                                    {% endif %}
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-PhoneNumber">
                                <label for="account-email">{{ "user.account.phoneNum"|translate}}</label>
                                <input class="form-control" name="PhoneNumber" value="{{ Model.address.PhoneNumber }}" type="text" id="account-PhoneNumber" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="account-AddressLine1">{{ "user.account.address1"|translate}} 1</label>
                                <input class="form-control" name="AddressLine1" value="{{ Model.address.AddressLine1 }}" type="text" id="account-AddressLine1" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="account-AddressLine2">{{ "user.account.address1"|translate}} 2</label>
                                <input class="form-control" name="AddressLine2" value="{{ Model.address.AddressLine2 }}" type="text" id="account-AddressLine2" >
                            </div>
                        </div>

                      <div class="col-md-6">
                            <div class="form-group">
                                <label for="account-City">{{ "user.account.city"|translate}}</label>
                                <input class="form-control" name="City" value="{{ Model.address.City }}" type="text" id="account-City"  required>
                            </div>
                        </div>



                        
                        {% if Model.address.IsBillingAddress  %}
                           <div class="col-md-6">
                            <div class="form-group">
                                <label for="account-ZipCode">{{ "user.account.zip_code"|translate}}</label>
                                <input class="form-control" name="ZipCode" value="{{ Model.address.ZipCode }}" type="text" id="account-ZipCode" required>
                            </div>
                        </div>
                        {% else %}
                            <div class="col-md-6">
                            <div class="form-group">
                                <label for="account-TaxCode">{{ "user.account.tax_code"|translate}}</label>
                                <input class="form-control"  name="TaxCode" value="{{ Model.address.TaxCode }}" type="text" id="account-TaxCode"  required>
                            </div>
                        </div>
                        {% endif %}


                         

                        
                        



                        <div class="col-12">
                            <hr class="mt-2 mb-3">
                            <div class="d-flex flex-wrap justify-content-end align-items-center">
                             <input type="hidden" name="AId" value="{{ Model.AId }}">
                             <input type="hidden" name="Billing" value="{{ Model.Billing }}">
                               <button type="button" id="SaveAddress" class="btn margin-15px-top btn-primary theme-btn">{{ "checkout.checkout.saved_addresses"|translate}}</button>
                              
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- End Contacts & Shipping Address -->
    </div>
    <script>

    // 原有代码保持不变，在文档底部添加以下内容
    document.addEventListener('DOMContentLoaded', function() {
        
     // 页面加载时获取初始值
  const initialValue = $('.CountryList').val();

  // 监听值变化
  $('.CountryList').on('change', function() {
    const newValue = $(this).val();
      var data = {
            CId: newValue
        };
            $.ajax({
                url: '/api/account/comment/CountryCId',
                type: 'POST',
                data: data,
                success: function(response) {
                    if(response.success) {
                        if(response.isProvince){
                             $('.IsProvince').attr('style', 'display:block');
                            var ProvinceList = response.message;
                            for(var i = 0; i < ProvinceList.length; i++) {
							    $('.ProvinceList').append('<option value="'+ProvinceList[i].sId+'">'+ProvinceList[i].states+'</option>');
						    }
                        }else{
                         $('.IsProvince').attr('style', 'display:none');
                        }
                       
                        
                    } else {
                        console.log('获取失败：' + (response.message || '未知错误'));
                    }
                },
                error: function(xhr) {
                    console.error('AJAX Error:', xhr.responseText);
                    console.log('获取失败：' + (xhr.statusText || '网络错误'));
                }
            });
        
  });


   $('#SaveAddress').click(function(e){
        e.preventDefault();

       
        var data = {
            FirstName: $('input[name="FirstName"]').val(),
            LastName: $('input[name="LastName"]').val(),
            PhoneNumber: $('input[name="PhoneNumber"]').val(),
            AddressLine1: $('input[name="AddressLine1"]').val(),
            AddressLine2: $('input[name="AddressLine2"]').val(),
            CId: $('.CountryList').val(),
            SId: $('.ProvinceList').val(),
            City: $('input[name="City"]').val(),
            ZipCode: $('input[name="ZipCode"]').val(),
            TaxCode: $('input[name="TaxCode"]').val(),
            AId: $('input[name="AId"]').val(),
            Billing: $('input[name="Billing"]').val()
        };

            $.ajax({
                url: '/api/account/comment/SaveAddress',
                type: 'POST',
                data: data,
                success: function(response) {
                    if(response.status) {
                        window.location.href = "/Account/MyAddress"; 
                    } else {
                        console.log('提交失败：' + (response.msg || '未知错误'));
                    }
                },
                error: function(xhr) {
                    console.error('AJAX Error:', xhr.msg);
                    console.log('提交失败：' + (xhr.statusText || '网络错误'));
                }
            });
        
    });

    });



</script>