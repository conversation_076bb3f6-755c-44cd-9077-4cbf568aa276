<div class="offcanvas-wrapper">
    <!-- Start Page Title -->
    <div class="page-title">
        <div class="container">
            <div class="column">
                <h1>{{ "blog.global.blog"|translate}}</h1>
            </div>
            <div class="column">
                <ul class="breadcrumbs">
                    <li><a href="/">{{ "web.global.home"|translate}}</a></li>
                    <li class="separator">&nbsp;</li>
                    <li>{{ "blog.global.blog"|translate}}</li>
                </ul>
            </div>
        </div>
    </div>
    <!-- End Page Title -->
    <!-- Start Page Content -->
    <div class="container padding-top-1x padding-bottom-3x">
        <div class="row">
            <!-- Start Blog Content -->
            <div class="col-lg-9 order-lg-1">
                <div class="owl-carousel mb-4" data-owl-carousel='{ "nav": true, "dots": false, "loop": true }'>
                    <figure>
                        <img src="{{static_path}}/assets/images/blog/single/01.jpg" alt="Image">
                        <figcaption class="text-white">Image Caption</figcaption>
                    </figure>
                    <figure>
                        <img src="{{static_path}}/assets/images/blog/single/02.jpg" alt="Image">
                        <figcaption class="text-white">Image Caption</figcaption>
                    </figure>
                    <figure>
                        <img src="{{static_path}}/assets/images/blog/single/03.jpg" alt="Image">
                        <figcaption class="text-white">Image Caption</figcaption>
                    </figure>
                </div>
                <!-- Start Blog Posts -->
                <article class="row">
                    <div class="col-md-3 order-md-2">
                        <ul class="post-meta">
                            <li><i class="icon-clock"></i><a href="/blog/datail">&nbsp;Feb 11, 2018</a></li>
                            <li><i class="icon-head"></i>&nbsp;John Doe</li>
                            <li><i class="icon-tag"></i><a href="#">&nbsp;Mobile,</a><a href="#">&nbsp;Laptop</a></li>
                            <li><i class="icon-speech-bubble"></i><a href="#">&nbsp;3 {{ "blog.global.comments"|translate}}</a></li>
                        </ul>
                    </div>
                    <div class="col-md-9 order-md-1 blog-post">
                        <a class="post-thumb" href="/blog/datail">
                            <img src="{{static_path}}/assets/images/blog/01.jpg" alt="Post">
                        </a>
                        <h3 class="post-title"><a href="/blog/datail">This is a Standard post with a Preview Image</a></h3>
                        <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic ... <a href='/blog/datail' class='text-medium'>Read More</a></p>
                    </div>
                </article>
                <article class="row">
                    <div class="col-md-3 order-md-2">
                        <ul class="post-meta">
                            <li><i class="icon-clock"></i><a href="/blog/datail">&nbsp;Jan 11, 2018</a></li>
                            <li><i class="icon-head"></i>&nbsp;John Doe</li>
                            <li><i class="icon-tag"></i><a href="#">&nbsp;Television,</a><a href="#">&nbsp;Camera</a></li>
                            <li><i class="icon-speech-bubble"></i><a href="#">&nbsp;7 {{ "blog.global.comments"|translate}}</a></li>
                        </ul>
                    </div>
                    <div class="col-md-9 order-md-1 blog-post">
                        <a class="post-thumb" href="/blog/datail">
                            <img src="{{static_path}}/assets/images/blog/02.jpg" alt="Post">
                        </a>
                        <h3 class="post-title"><a href="/blog/datail">This is a Standard post with a Preview Image</a></h3>
                        <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic ... <a href='/blog/datail' class='text-medium'>Read More</a></p>
                    </div>
                </article>
                <article class="row">
                    <div class="col-md-3 order-md-2">
                        <ul class="post-meta">
                            <li><i class="icon-clock"></i><a href="/blog/datail">&nbsp;Jan 11, 2018</a></li>
                            <li><i class="icon-head"></i>&nbsp;John Doe</li>
                            <li><i class="icon-tag"></i><a href="#">&nbsp;Console,</a><a href="#">&nbsp;Printer</a></li>
                            <li><i class="icon-speech-bubble"></i><a href="#">&nbsp;14 {{ "blog.global.comments"|translate}}</a></li>
                        </ul>
                    </div>
                    <div class="col-md-9 order-md-1 blog-post">
                        <a class="post-thumb" href="/blog/datail">
                            <img src="{{static_path}}/assets/images/blog/03.jpg" alt="Post">
                        </a>
                        <h3 class="post-title"><a href="/blog/datail">This is a Standard post with a Preview Image</a></h3>
                        <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic ... <a href='/blog/datail' class='text-medium'>Read More</a></p>
                    </div>
                </article>
                <!-- End Blog Posts -->
                <!-- Start Pagination -->
                <nav class="pagination">
                    <div class="column">
                        <ul class="pages">
                            <li class="active"><a href="#">1</a></li>
                            <li><a href="#">2</a></li>
                            <li><a href="#">3</a></li>
                            <li>...</li>
                            <li><a href="#">10</a></li>
                            <li><a href="#">20</a></li>
                            <li><a href="#">30</a></li>
                        </ul>
                    </div>
                    <div class="column text-right hidden-xs-down">
                        <a class="btn btn-outline-secondary btn-sm" href="#">Next&nbsp;<i class="icon-arrow-right"></i></a>
                    </div>
                </nav>
                <!-- End Pagination -->
            </div>
            <!-- End Blog Content -->
            <!--Sidebar-->
            {% assign blogsidebar= '/Themes/'| append: theme | append:'/Blog/BlogSidebar' %}
            {% include blogsidebar -%}
            <!--End Sidebar-->
        </div>
    </div>
    <!-- End Page Content -->
</div>