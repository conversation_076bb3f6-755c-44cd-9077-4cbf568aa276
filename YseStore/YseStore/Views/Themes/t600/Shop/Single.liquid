{% section meta_keywords -%}
   {% if Model.Product.ProductSeo and Model.Product.ProductSeo.SeoKeyword_en and Model.Product.ProductSeo.SeoKeyword_en != "" %}
   <meta name="keywords" content="{{ Model.Product.ProductSeo.SeoKeyword_en }}">
   {% elsif Model.Product.Tags and Model.Product.Tags.size > 0 %}
   <meta name="keywords" content="{% for tag in Model.Product.Tags %}{{ tag.Name }}{% unless forloop.last %}, {% endunless %}{% endfor %}, {{ Model.Product.ProductName }}">
   {% else %}
   <meta name="keywords" content="{{ Model.Product.ProductName }}, {{ Model.Product.BrandName }}">
   {% endif %}
  {% endsection -%}
  {% section meta_description -%}
   {% if Model.Product.ProductSeo and Model.Product.ProductSeo.SeoDescription_en and Model.Product.ProductSeo.SeoDescription_en != "" %}
   <meta name="description" content="{{ Model.Product.ProductSeo.SeoDescription_en }}">
   {% elsif Model.Product.BriefDescription and Model.Product.BriefDescription != "" %}
   <meta name="description" content="{{ Model.Product.BriefDescription }}">
   {% else %}
   {% endif %}
  {% endsection -%}
  {% section title -%}
   {% if Model.Product.ProductSeo and Model.Product.ProductSeo.SeoTitle_en and Model.Product.ProductSeo.SeoTitle_en != "" %}
   <title>{{ Model.Product.ProductSeo.SeoTitle_en }}</title>
   {% else %}
   <title>{{ Model.Product.ProductName }}</title>
   {% endif %}
  {% endsection -%}

<div class="shop-single py-90">
    <div class="container">
        <div class="row">
            <div class="col-md-9 col-lg-6 col-xxl-5">
                <div class="shop-single-gallery">
                    {% if Model.Product.VideoUrl != null and Model.Product.VideoUrl != "" %}
                    <a class="shop-single-video popup-youtube" href="{{ Model.Product.VideoUrl }}"
                       data-tooltip="tooltip" title="Watch Video">
                        <i class="far fa-play"></i>
                    </a>
                    {% endif %}
                    <div class="flexslider-thumbnails">
                        <ul class="slides">
                            {% if Model.Product.ProductImages != null and Model.Product.ProductImages.size > 0 %}
                            {% for image in Model.Product.ProductImages %}
                            <li data-thumb="{{ image.PicPath }}" rel="adjustX:10, adjustY:">
                                <img src="{{ image.PicPath }}"
                                     alt="{{ Model.Product.ProductName }}" />
                            </li>
                            {% endfor %}
                            {% else %}
                            <li data-thumb="/assets/img/product/01.png" rel="adjustX:10, adjustY:">
                                <img src="{{ Model.Product.PicPath }}"
                                     alt="{{ Model.Product.ProductName }}" />
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-lg-6 col-xxl-6">
                <div class="shop-single-info">
                    <h4 class="shop-single-title">{{ Model.Product.ProductName }}</h4>
                    <div class="shop-single-rating">
                        {% assign fullStars = Model.Product.Rating | floor %}
                        {% assign halfStar = Model.Product.Rating | minus: fullStars %}
                        {% assign nextStar = fullStars | plus: 1 %}
                        {% for i in (1..5) %}
                        {% if i <= fullStars %}
                        <i class="fas fa-star"></i>
                        {% elsif halfStar >= 0.5 and i == nextStar %}
                        <i class="fas fa-star-half-alt"></i>
                        {% else %}
                        <i class="far fa-star"></i>
                        {% endif %}
                        {% endfor %}
                        <a href="#nav-tab"><span class="rating-count"> ({{ Model.Product.ReviewCount }} {{ "products.goods.customer_review" | translate }})</span></a>
                    </div>
                    <div class="shop-single-price">
                        {% if Model.Product.OriginalPrice != null and Model.Product.OriginalPrice > Model.Product.Price %}
                        <del>{{ Model.Product.OriginalPriceFormat }}</del>
                        <span class="amount">{{ Model.Product.PriceFormat }}</span>
                        {% assign discountPercent = Model.Product.OriginalPrice | minus: Model.Product.Price | times: 100 | divided_by: Model.Product.OriginalPrice | round %}
                        <span class="discount-percentage">{{ discountPercent }}% Off</span>
                        {% else %}
                        <span class="amount">{{ Model.Product.PriceFormat }}</span>
                        {% endif %}
                    </div>
                    {% if Model.Product.PromotionPrice != 0 %}
                    <div class="shop-single-price">
                        <span style="font-size:18px;">Promotional Price ：</span>
                        <span style="color: var(--color-red2);">{{ Model.Product.PromotionPriceFormat }}</span>
                    </div>
                    {% endif %}


                    <p class="mb-3">
                        {{ Model.Product.BriefDescription |  raw }}
                    </p>
                    <div class="shop-single-cs">
                        <div class="row">
                            <div class="col-md-3 col-lg-4 col-xl-3">
                                <div class="shop-single-size">
                                    <h6>{{ "web.global.qty" | translate }}</h6>
                                    <div class="shop-cart-qty">
                                        <button class="minus-btn">
                                            <i class="fal fa-minus"></i>
                                        </button>
                                        <input class="quantity" type="text" value="1" disabled="">
                                        <button class="plus-btn">
                                            <i class="fal fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% comment %} 动态渲染所有属性，不再硬编码特定属性类型 {% endcomment %}
                            {% if Model.Product.DynamicAttributes != null and Model.Product.DynamicAttributes.size > 0 %}
                            {% for attribute in Model.Product.DynamicAttributes %}
                            {% assign attributeName = attribute[0] %}
                            {% assign attributeOptions = attribute[1] %}

                            {% if attributeOptions != null and attributeOptions.size > 0 %}
                            {% comment %} 检查是否有颜色代码，如果有则渲染为颜色选择器 {% endcomment %}
                            {% assign hasColorCode = false %}
                            {% for option in attributeOptions %}
                            {% if option.ColorCode != null and option.ColorCode != "" %}
                            {% assign hasColorCode = true %}
                            {% break %}
                            {% endif %}
                            {% endfor %}

                            {% if hasColorCode %}
                            {% comment %} 渲染颜色选择器 {% endcomment %}
                            <div class="col-md-6 col-lg-12 col-xl-6">
                                <div class="shop-single-color">
                                    <h6>{{ attributeName }}</h6>
                                    <ul class="shop-checkbox-list color">
                                        {% for option in attributeOptions %}
                                        <li>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       id="{{ attributeName | downcase }}{{ forloop.index }}"
                                                       data-attribute="{{ attributeName }}"
                                                       data-option-id="{{ option.Id }}"
                                                       data-option-name="{{ option.Name }}"
                                                       {% if forloop.first %}checked{% endif %}>
                                                <label class="form-check-label"
                                                       for="{{ attributeName | downcase }}{{ forloop.index }}">
                                                    <span style="background-color: {{ option.ColorCode }}"
                                                          title="{{ option.Name }}"></span>
                                                </label>
                                            </div>
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                            {% else %}
                            {% comment %} 渲染下拉选择器（适用于尺寸等其他属性） {% endcomment %}
                            <div class="col-md-3 col-lg-4 col-xl-3">
                                <div class="shop-single-size">
                                    <h6>{{ attributeName }}</h6>
                                    <select class="select" data-attribute="{{ attributeName }}" id="select_t600">
                                        <option value="">Choose {{ attributeName }}</option>
                                        {% for option in attributeOptions %}
                                        <option value="{{ option.Id }}"
                                                data-option-name="{{ option.Name }}"
                                                {% if forloop.first %}selected{% endif %}>
                                            {{ option.Name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            {% endif %}
                            {% endif %}
                            {% endfor %}
                            {% endif %}
                        </div>
                    </div>

                    {% comment %} 仓库选择区域 {% endcomment %}
                    {% if Model.Product.OptionalWarehouses != null and Model.Product.OptionalWarehouses.size > 0 %}
                    <div class="shop-single-cs">
                        <div class="row">
                            <div class="col-md-6 col-lg-12 col-xl-6">
                                <div class="shop-single-warehouse">
                                    <h6>仓库选择</h6>
                                    {% if Model.Product.OptionalWarehouses.size > 0 %}
                                    {% comment %} 多个仓库时显示选择器 {% endcomment %}
                                    <ul class="shop-checkbox-list warehouse">
                                        {% for warehouse in Model.Product.OptionalWarehouses %}
                                        <li>
                                            <div class="form-check">
                                                <input class="form-check-input warehouseInput" type="checkbox"
                                                       id="warehouse{{ forloop.index }}"
                                                       data-warehouse-id="{{ warehouse.OvId }}"
                                                       data-warehouse-name="{{ warehouse.Name }}"
                                                       {% if forloop.first %}checked{% endif %}>
                                                <label class="form-check-label" for="warehouse{{ forloop.index }}">
                                                    <span>{{ warehouse.Name }}</span>
                                                </label>
                                            </div>
                                        </li>
                                        {% endfor %}
                                    </ul>
                                    {% else %}
                                    {% comment %} 只有一个仓库时自动选中 {% endcomment %}
                                    <input type="hidden" class="warehouseInput"
                                           data-warehouse-id="{{ Model.Product.OptionalWarehouses[0].OvId }}"
                                           data-warehouse-name="{{ Model.Product.OptionalWarehouses[0].Name }}"
                                           checked>
                                    <span class="single-warehouse">{{ Model.Product.OptionalWarehouses[0].Name }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="shop-single-sortinfo">
                        <ul>
                            <li>
                                {{ "user.account.order_status" | translate }}:
                                <span>{% if Model.Product.IsInStock %}Available{% else %}Out of Stock{% endif %}</span>
                            </li>
                            <li>
                                {{ "products.goods.sku" | translate }}: <span>{{ Model.Product.Sku }}</span>
                            </li>
                            <li>
                                {{ "blog.global.blog_category" | translate }}:
                                <span>{{ Model.Product.CategoryName }}</span>
                            </li>
                            <li>
                                Brand: <a href="#">{{ Model.Product.BrandName }}</a>
                            </li>
                            <li>
                                {{ "blog.global.tags" | translate }}:
                                {% if Model.Product.Tags != null and Model.Product.Tags.size > 0 %}
                                {% for tag in Model.Product.Tags %}
                                <a href="#">{{ tag.Name }}</a>{% unless forloop.last %},{% endunless %}
                                {% endfor %}
                                {% else %}
                                <span>No tags</span>
                                {% endif %}
                            </li>
                        </ul>
                    </div>
                    <div class="shop-single-action">
                        <div class="row align-items-center">
                            <div class="col-md-6 col-lg-12 col-xl-6">
                                <div class="shop-single-btn">
                                    {% if Model.Product.IsInStock %}
                                    <a href="#" class="theme-btn add-to-cart-btn"
                                       data-product-id="{{ Model.Product.ProductId }}">
                                        <span class="far fa-shopping-bag"></span>{{ "products.goods.addToCart" | translate }}
                                    </a>
                                    {% endif %}
                                    <a href="#" class="theme-btn theme-btn2" data-tooltip="tooltip"
                                       title="{{ "products.goods.addToFavorites" | translate }}"
                                       data-product-id="{{ Model.Product.ProductId }}">
                                        <span class="far fa-heart"></span>
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-12 col-xl-6">
                                <div class="shop-single-share">
                                    <span>Share:</span>
                                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                                    <a href="#"><i class="fab fa-x-twitter"></i></a>
                                    <a href="#"><i class="fab fa-linkedin-in"></i></a>
                                    <a href="#"><i class="fab fa-pinterest-p"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 调试面板 -->
        <div class="debug-info" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 20px 0;">
            <h6 style="color: #495057; margin-bottom: 10px;">调试信息</h6>
            <div style="font-size: 14px; color: #6c757d;">
                <div><strong>匹配模式:</strong> <span id="debugMode">{% if Model.Product.IsCombination == 0 %}单规格{% elsif Model.Product.IsCombination == 1 %}多规格(组合匹配){% elsif Model.Product.IsCombination == 2 %}多规格加价(单独匹配){% else %}未知({{ Model.Product.IsCombination }}){% endif %}</span></div>
                <div><strong>选中的变体组合:</strong> <span id="debugSelectedAttributes">未选择</span></div>
                <div><strong>匹配的VariantsId:</strong> <span id="debugVariantsId">未匹配</span></div>
                <div><strong>选中的仓库:</strong> <span id="debugWarehouse">未选择</span></div>
            </div>
        </div>

        <!-- 隐藏字段用于存储匹配结果 -->
        <input type="hidden" id="selectedVariantsCombination" name="selectedVariantsCombination" value="">
        <input type="hidden" id="matchedVariantsId" name="matchedVariantsId" value="">

        <div class="shop-single-details">
            <nav>
                <div class="nav nav-tabs" id="nav-tab" role="tablist">
                    <!-- 从 Model.ProductSwitches 中动态生成所有面板标签 -->
                    {% if Model.ProductSwitches != null and Model.ProductSwitches.size > 0 %}
                    {% for switch in Model.ProductSwitches %}
                    <button class="nav-link {% if forloop.first %}active{% endif %}"
                            id="nav-tab-{{ switch.SId }}"
                            data-id="{{ switch.DataId }}"
                            data-bs-toggle="tab"
                            data-bs-target="#tab-{{ switch.SId }}"
                            type="button"
                            role="tab"
                            aria-controls="tab-{{ switch.SId }}"
                            aria-selected="{% if forloop.first %}true{% else %}false{% endif %}">
                        {{ switch.Name }}
                    </button>
                    {% endfor %}
                    {% endif %}
                </div>
            </nav>
            <div class="tab-content" id="nav-tabContent">
                <!-- 动态生成面板内容，对 Description 和 Reviews 特殊处理 -->
                {% if Model.ProductSwitches != null and Model.ProductSwitches.size > 0 %}
                {% for switch in Model.ProductSwitches %}
                <div class="tab-pane fade {% if forloop.first %}show active{% endif %}"
                     id="tab-{{ switch.SId }}"
                     data-id="{{ switch.DataId }}"
                     role="tabpanel"
                     aria-labelledby="nav-tab-{{ switch.SId }}">

                    <!-- 如果是 Description 面板，显示原有的产品描述内容 -->
                    {% if switch.Identity == "Description" %}
                    <div class="shop-single-desc">
                        {{ Model.Product.Description | raw }}

                        {% if Model.Product.Features != null and Model.Product.Features.size > 0 %}
                        <div class="row">
                            <div class="col-lg-5 col-xl-4">
                                <div class="shop-single-list">
                                    <h5 class="title">Features</h5>
                                    <ul>
                                        {% for feature in Model.Product.Features %}
                                        <li>{{ feature }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                            {% endif %}

                            {% if Model.Product.Specifications != null and Model.Product.Specifications.size > 0 %}
                            <div class="col-lg-6 col-xl-5">
                                <div class="shop-single-list">
                                    <h5 class="title">Specifications</h5>
                                    <ul>
                                        {% for spec in Model.Product.Specifications %}
                                        <li>
                                            <span>{{ spec.Name }}:</span> {{ spec.Value }}
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 如果是 Reviews 面板，显示原有的评论内容 -->
                    {% elsif switch.Identity == "Reviews" %}
                    <div class="shop-single-review">
                        <div class="blog-comments">
                            <div class="blog-comments-title">
                                <h4>
                                    {% if Model.Product.ReviewCount > 0 %}{{ "blog.global.comments" | translate }}({{ Model.Product.ReviewCount }}{% else %}0{% endif %}
                                    )
                                </h4>
                            </div>
                            <div class="blog-comments-content">
                                {% if Model.Product.Reviews.size > 0 %}
                                {% for review in Model.Product.Reviews %}
                                <div class="blog-comments-item">
                                    <div class="blog-comments-img">
                                        {% if review.UserAvatar %}
                                        <img src="{{ review.UserAvatar }}"
                                             alt="{{ review.UserName }}">
                                        {% else %}
                                        <img src="/themes/t600/assets/img/icon-img/user.png"
                                             alt="{{ review.UserName }}">
                                        {% endif %}
                                    </div>
                                    <div class="blog-comments-content">
                                        <div class="blog-comments-top">
                                            <div class="blog-comments-name">
                                                <h5>{{ review.UserName }}</h5>
                                                <span>{{ review.ReviewDate | date: '%Y-%m-%d' }}</span>
                                            </div>
                                            <div class="blog-rating">
                                                {% assign rating = review.Rating %}
                                                {% for i in (1..5) %}
                                                {% if i <= rating %}
                                                <i class="fas fa-star"></i>
                                                {% else %}
                                                <i class="far fa-star"></i>
                                                {% endif %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                        <p>{{ review.Content }}</p>

                                        <!-- 显示评论图片 -->
                                        {% if review.Images.size > 0 %}
                                        <div class="review-images">
                                            {% for image in review.Images %}
                                            <a href="{{ image }}" class="review-image-item">
                                                <img src="{{ image }}" alt="评论图片">
                                            </a>
                                            {% endfor %}
                                        </div>
                                        {% endif %}

                                        <!-- 显示回复（直接在主评论内容区域内） -->
                                        {% if review.Replies.size > 0 %}
                                        <div class="replies-container">
                                            {% for reply in review.Replies %}
                                            <div class="reply-item">
                                                <div class="reply-header">
                                                    <strong>{{ reply.UserName }}</strong>
                                                    {% if reply.IsAdmin %}<span class="admin-badge">管理员</span>{% endif %}
                                                    <span class="reply-date">{{ reply.ReplyDate | date: '%Y-%m-%d' }}</span>
                                                </div>
                                                <div class="reply-content">
                                                    {% if reply.ReplyToName != null and reply.ReplyToName != '' %}
                                                    <span class="reply-to-tag">@{{ reply.ReplyToName }}</span>
                                                    {% endif %}
                                                    {{ reply.Content }}
                                                </div>
                                                <div class="reply-actions">
                                                    <a href="javascript:void(0);"
                                                       onclick="replyToComment('{{ review.Id }}', '{{ reply.UserName }}', '{{ reply.Id }}')"
                                                       class="reply-btn">{{ "user.account.reply_btn" | translate }}</a>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                        {% endif %}

                                        <!-- 回复按钮 -->
                                        <div class="blog-comments-btn">
                                            <a href="javascript:void(0);"
                                               onclick="replyToComment('{{ review.Id }}', '{{ review.UserName }}')"
                                               data-review-id="{{ review.Id }}"
                                               data-user-name="{{ review.UserName }}">{{ "user.account.reply_btn" | translate }}</a>
                                        </div>

                                    </div>
                                </div>
                                {% endfor %}
                                {% else %}
                                <div class="no-reviews">
                                    <p>
                                        {{ "products.goods.no_review_data" | translate }}
                                        . {{ "blog.global.leaveAReply" | translate }} !
                                    </p>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="leave-form-area">
                            <div class="blog-reply">
                                <h4 class="comment-reply-title">{{ "blog.global.leaveAReply" | translate }}</h4>
                                <form id="review-form" class="comment-form-area">
                                    <!-- 回复信息提示 -->
                                    <div id="reply-info"
                                         style="display: none; margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                                        <p>
                                            回复给: <span id="reply-to-name" class="reply-to-tag"></span>
                                            <a href="javascript:void(0);" onclick="cancelReply()"
                                               style="float: right; color: #dc3545;">{{ "web.global.cancel" | translate }}</a>
                                        </p>
                                    </div>

                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="leave-form">
                                                <input id="review-name"
                                                       placeholder="{{ "products.goods.write_your_name" | translate }}*"
                                                       type="text">
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="leave-form">
                                                <input id="review-email"
                                                       placeholder="{{ "web.global.newsletter_your_email" | translate }}*"
                                                       type="email">
                                            </div>
                                        </div>
                                        <!--<div class="col-lg-12">
                                            <div class="leave-form">
                                                <input id="review-subject" placeholder="Your Subject*" type="text">
                                            </div>
                                        </div>-->
                                        <div class="col-lg-12">
                                            <div class="leave-form">
                                                <select id="review-rating" class="form-control">
                                                    <option value="">{{ "products.goods.your_rating" | translate }}</option>
                                                    <option value="5">5 Stars</option>
                                                    <option value="4">4 Stars</option>
                                                    <option value="3">3 Stars</option>
                                                    <option value="2">2 Stars</option>
                                                    <option value="1">1 Star</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-12">
                                            <div class="text-leave">
                                                <textarea id="review-content"
                                                          placeholder="{{ "products.goods.write_your_review" | translate }}*"></textarea>
                                                <input id="captchaVerifyParam" type="hidden" value="">
                                                <div id="captcha-element"></div>
                                                <button id="review-submit-btn" class="submit" type="button"
                                                        onclick="submitReview()">
                                                    {{ "products.goods.writeReview" | translate }}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 其他面板，显示从 SwitchContents 中获取的内容 -->
                    {% else %}
                    <div class="shop-single-content">
                        {% if Model.SwitchContents != null and Model.SwitchContents != "" %}
                        <div class="content-body">
                            {{ Model.SwitchContents | raw }}
                        </div>
                        {% else %}
                        <p>No content available for this section.</p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
                {% endif %}
            </div>
        </div>


        <div class="product-area related-item pt-40">
            <div class="container px-0">
                <div class="row">
                    <div class="col-12">
                        <div class="site-heading-inline">
                            <h2 class="site-title">{{ "web.global.related_category" | translate }}</h2>
                            <a href="/shop">
                                {{ "web.global.view_more_s" | translate }} <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="row g-4 item-2">
                    {% if Model.RelatedProducts != null and Model.RelatedProducts.size > 0 %}
                    {% for product in Model.RelatedProducts %}
                    <div class="col-md-6 col-lg-3">
                        <div class="product-item">
                            <div class="product-img">
                                {% if product.TypeLabel != null and product.TypeLabel != "" %}
                                <span class="type {% if product.TypeLabel == " Hot" %}hot{% elsif product.TypeLabel=="Out Of Stock" %}oos{% endif %}">{{ product.TypeLabel }}</span>
                                {% endif %}
                                <a href="/products/{{ product.PageUrl }}"
                                   hx-get="/products/{{ product.PageUrl }}" hx-target="#main"
                                   hx-push-url="true" hx-swap="innerHTML">
                                    <img src="{% if product.PicPath != null and product.PicPath != '' %}{{ product.PicPath }}{% else %}/assets/img/product/01.png{% endif %}"
                                         alt="{{ product.ProductName }}" />
                                </a>
                                <div class="product-action-wrap">
                                    <div class="product-action">
                                        <a href="#" data-bs-toggle="modal" data-bs-target="#quickview"
                                           data-bs-placement="right" data-tooltip="tooltip"
                                           title="{{ "web.global.quickView" | translate }}"
                                           data-product-id="{{ product.ProductId }}">
                                            <i class="far fa-eye"></i>
                                        </a>
                                        <a href="#" data-bs-placement="right" data-tooltip="tooltip"
                                           title="{{ "products.goods.addToFavorites" | translate }}">
                                            <i class="far fa-heart"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="product-content">
                                <h3 class="product-title">
                                    <a href="/products/{{ product.PageUrl }}"
                                       hx-get="/products/{{ product.PageUrl }}" hx-target="#main"
                                       hx-push-url="true" hx-swap="innerHTML">{{ product.ProductName }}</a>
                                </h3>
                                <div class="product-rate">
                                    {% assign fullStars = product.Rating | floor %}
                                    {% assign halfStar = product.Rating | minus: fullStars %}
                                    {% assign nextStar = fullStars | plus: 1 %}
                                    {% for i in (1..5) %}
                                    {% if i <= fullStars %}
                                    <i class="fas fa-star"></i>
                                    {% elsif halfStar >= 0.5 and i == nextStar %}
                                    <i class="fas fa-star-half-alt"></i>
                                    {% else %}
                                    <i class="far fa-star"></i>
                                    {% endif %}
                                    {% endfor %}
                                </div>
                                <div class="product-bottom">
                                    <div class="product-price">
                                        {% if product.OriginalPrice != null and product.OriginalPrice > product.Price %}
                                        <del>{{ product.OriginalPriceFormat }}</del>
                                        {% endif %}
                                        <span>{{ product.PriceFormat }}</span>
                                    </div>
                                    <button type="button" class="product-cart-btn" data-bs-placement="left"
                                            data-tooltip="tooltip"
                                            title="{{ "products.goods.addToCart" | translate }}"
                                            {% if product.IsInStock==false %}disabled{% endif %}
                                            data-product-id="{{ product.ProductId }}">
                                        <i class="far fa-shopping-bag"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <div class="col-12 text-center">
                    <p>{{ "products.goods.no_products" | translate }}.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

</div>
</div>

<div class="modal quickview fade" id="quickview" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
     aria-labelledby="quickview" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                <i class="far fa-xmark"></i>
            </button>
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
                        <div class="quickview-img">
                            <img src="/assets/img/product/04.png" alt="#">
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
                        <div class="quickview-content">
                            <h4 class="quickview-title">Surgical Face Mask</h4>
                            <div class="quickview-rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                                <i class="far fa-star"></i>
                                <span class="rating-count"> (4 {{ "products.goods.customer_review" | translate }})</span>
                            </div>
                            <div class="quickview-price">
                                <h5>
                                    <del>$860</del>
                                    <span>$740</span>
                                </h5>
                            </div>
                            <ul class="quickview-list">
                                <li>Brand:<span>Medica</span></li>
                                <li>{{ "blog.global.blog_category" | translate }}:<span>Healthcare</span></li>
                                <li>
                                    {{ "user.account.order_status" | translate }}:<span class="stock">Available</span>
                                </li>
                                <li>{{ "products.goods.sku" | translate }}:<span>789FGDF</span></li>
                            </ul>
                            <div class="quickview-cart">
                                <a href="#" class="theme-btn">{{ "products.goods.addToCart" | translate }}</a>
                            </div>
                            <div class="quickview-social">
                                <span>{{ "user.account.DIST_how_to_share" | translate }}:</span>
                                <a href="#"><i class="fab fa-facebook-f"></i></a>
                                <a href="#"><i class="fab fa-x-twitter"></i></a>
                                <a href="#"><i class="fab fa-pinterest-p"></i></a>
                                <a href="#"><i class="fab fa-instagram"></i></a>
                                <a href="#"><i class="fab fa-linkedin-in"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Sticky Cart-->
<div class="stickyCart">
    <div class="container">
        <div class="img"><img src="{{ static_path }}/assets/img/product/01.png" class="product-featured-img" alt=""></div>
        <div class="sticky-title">
            <span>Canon CSG-QX10 Full Shot Lens</span><br />
            <span class="old-price">$400.00</span><span class="price">$499.00</span>
        </div>
        <button name="add" class="btn product-form__cart-submit theme-btn" style="background: var(--color-red2);"><span>Add to cart</span></button>
        <button name="add" class="btn product-form__cart-submit theme-btn"><span>Buy it now</span></button>
    </div>
</div>
<!--End Sticky Cart-->
<!-- 直接导入快速预览脚本 -->
<script src="/businessJs/Product/Index/quickView.js"></script>
<!-- 引入评论区域样式 -->
<link rel="stylesheet" href="/css/product/product-comments.css">
<!-- 引入阿里云验证码2.0 SDK -->
<script src="/js/AliyunCaptcha.js"></script>
<!-- 阿里云验证码核心模块 -->
<script src="/businessJs/aliyun-captcha-core.js"></script>
<!-- 引入自定义消息弹窗脚本 -->
<script src="/js/Pop-ups/frame-message.js"></script>

<script>
    // HTML实体解码函数
    function decodeHtmlEntities(str) {
        if (!str) return str;
        const textarea = document.createElement('textarea');
        textarea.innerHTML = str;
        return textarea.value;
    }

    // 产品变体数据（原始数据）
    const rawProductVariants = [
        {% if Model.Product.ProductVariants != null and Model.Product.ProductVariants.size > 0 %}
            {% for variant in Model.Product.ProductVariants %}
                {
                    VariantsId: "{{ variant.VariantsId }}",
                    Title: "{{ variant.Title }}",
                    AttrName: "{{ variant.AttrName }}",
                    Price: {{ variant.Price | default: 0 }},
                    Stock: {{ variant.Stock | default: 0 }}
                }{% unless forloop.last %},{% endunless %}
            {% endfor %}
        {% endif %}
    ];

    // 解码后的产品变体数据
    window.productVariants = rawProductVariants.map(variant => ({
        ...variant,
        Title: decodeHtmlEntities(variant.Title),
        AttrName: decodeHtmlEntities(variant.AttrName),
        PriceFormat: decodeHtmlEntities(variant.PriceFormat),
        OldPriceFormat: decodeHtmlEntities(variant.OldPriceFormat),
        PromotionPriceFormat: decodeHtmlEntities(variant.PromotionPriceFormat)
    }));

    // 存储用户实际选择的变体值（避免原生select状态不准确的问题）
    window.userSelectedVariants = {};





    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        // 初始化验证码（但不显示）
        initCaptcha();

        // 延迟初始化产品变体选择功能，确保nice-select已经初始化
        setTimeout(function() {
            initProductVariants();
        }, 500);
    });

    // 初始化验证码
    function initCaptcha() {
        // 确保YseCaptcha已加载
        if (typeof YseCaptcha === 'undefined') {
            console.error('YseCaptcha not loaded');
            return;
        }
        // 初始化验证码
        YseCaptcha.init({
            onSuccess: function () {
                // 验证成功后处理表单提交
                processFormSubmission();
            },
            onClose: function () {
                // 用户关闭或取消验证码时恢复按钮状态
                resetSubmitButton();
            }
        });
    }

    // 重置提交按钮状态
    function resetSubmitButton() {
        const submitBtn = document.getElementById('review-submit-btn');
        submitBtn.innerHTML = '{{ "products.goods.writeReview" | translate }}';
        submitBtn.disabled = false;
    }

    // 提交评论函数
    function submitReview() {
        // 获取表单数据
        const name = document.getElementById('review-name').value;
        const content = document.getElementById('review-content').value;
        const rating = document.getElementById('review-rating').value;

        // 验证表单数据
        if (!name.trim()) {
            customize_pop.warning('Please enter your name', null, null, {showIcon: false});
            return;
        }

        if (!content.trim()) {
            customize_pop.warning('Please enter your comment', null, null, {showIcon: false});
            return;
        }

        // 检查是否在回复模式
        const reviewForm = document.getElementById('review-form');
        const isReplyMode = reviewForm.dataset.replyTo;

        // 如果不是回复模式，需要验证邮箱和评分
        if (!isReplyMode) {
            const email = document.getElementById('review-email').value;
            if (!email.trim()) {
                customize_pop.warning('Please enter your email', null, null, {showIcon: false});
                return;
            }

            if (!rating || rating === '0') {
                customize_pop.warning('Please select a rating', null, null, {showIcon: false});
                return;
            }
        }

        // 显示提交中的状态
        const submitBtn = document.getElementById('review-submit-btn');
        submitBtn.innerHTML = 'Verifying...';
        submitBtn.disabled = true;

        // 触发验证码验证
        if (typeof YseCaptcha !== 'undefined') {
            YseCaptcha.verify();
        } else {
            customize_pop.error('Verification service not loaded, please refresh the page', null, null, {showIcon: false});
            resetSubmitButton();
        }
    }

    // 处理表单提交（验证码验证成功后）
    function processFormSubmission() {
        // 获取表单数据
        const name = document.getElementById('review-name').value;
        const email = document.getElementById('review-email').value;
        const content = document.getElementById('review-content').value;
        const rating = document.getElementById('review-rating').value;
        const captchaVerifyParam = YseCaptcha.getVerifyParam();
        const productId = '{{ Model.Product.ProductId }}';

        // 显示提交中的状态
        const submitBtn = document.getElementById('review-submit-btn');
        submitBtn.innerHTML = 'Submitting...';

        // 检查是否在回复评论
        const reviewForm = document.getElementById('review-form');
        const replyToId = reviewForm.dataset.replyTo;

        if (replyToId) {
            // 提交回复
            const replyData = {
                name: name,
                content: content,
                isAdmin: false,
                replyToId: parseInt(reviewForm.dataset.replyToId || "0"),
                replyToName: reviewForm.dataset.replyToName || "",
                captchaVerifyParam: captchaVerifyParam
            };

            // 发送回复请求
            fetch(`/api/product/review/${replyToId}/reply`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(replyData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        customize_pop.success('Reply submitted successfully, thank you for your participation!', null, null, {showIcon: false});
                        // 重置表单
                        resetForm();
                        // 刷新页面以显示新回复
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        customize_pop.error(data.message || 'Failed to submit reply, please try again later', null, null, {showIcon: false});
                        // 恢复提交按钮
                        resetSubmitButton();
                    }
                })
                .catch(error => {
                    console.error('Error submitting reply:', error);
                    customize_pop.error('Failed to submit reply, please try again later', null, null, {showIcon: false});
                    // 恢复提交按钮
                    resetSubmitButton();
                });
        } else {
            // 提交评论
            const reviewData = {
                ProductId: productId,
                Name: name,
                Email: email,
                Title: 'Product Review',
                Content: content,
                Rating: parseInt(rating) || 0,
                CaptchaVerifyParam: captchaVerifyParam
            };

            // 发送评论请求
            fetch('/api/product/review/submit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(reviewData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        customize_pop.success('Review submitted successfully, thank you for your participation!', null, null, {showIcon: false});
                        // 重置表单
                        resetForm();
                        // 刷新页面以显示新评论
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        customize_pop.error(data.message || 'Failed to submit review, please try again later', null, null, {showIcon: false});
                        // 恢复提交按钮
                        resetSubmitButton();
                    }
                })
                .catch(error => {
                    console.error('Error submitting review:', error);
                    customize_pop.error('Failed to submit review, please try again later', null, null, {showIcon: false});
                    // 恢复提交按钮
                    resetSubmitButton();
                });
        }
    }



    // 回复评论函数
    function replyToComment(reviewId, name, replyId) {
        // 滚动到评论表单
        const reviewForm = document.getElementById('review-form');
        reviewForm.scrollIntoView({behavior: 'smooth'});

        // 显示回复提示
        const replyInfo = document.getElementById('reply-info');
        const replyToName = document.getElementById('reply-to-name');
        replyInfo.style.display = 'block';
        replyToName.textContent = name;

        // 隐藏评分选择（回复模式下不需要评分）
        const ratingElement = document.getElementById('review-rating');
        if (ratingElement) {
            ratingElement.closest('.leave-form').style.display = 'none';
        }

        // 隐藏邮箱字段（回复模式下不需要邮箱）
        const emailElement = document.getElementById('review-email');
        if (emailElement) {
            emailElement.closest('.leave-form').style.display = 'none';
        }

        // 聚焦到表单
        document.getElementById('review-name').focus();

        // 存储正在回复的评论ID
        reviewForm.dataset.replyTo = reviewId;

        // 存储回复目标ID和名称（如果有）
        reviewForm.dataset.replyToId = replyId ? replyId : 0;
        reviewForm.dataset.replyToName = name || "";
    }

    // 取消回复函数
    function cancelReply() {
        // 隐藏回复提示
        document.getElementById('reply-info').style.display = 'none';

        // 重新显示评分选择（退出回复模式）
        const ratingElement = document.getElementById('review-rating');
        if (ratingElement) {
            ratingElement.closest('.leave-form').style.display = 'block';
        }

        // 重新显示邮箱字段（退出回复模式）
        const emailElement = document.getElementById('review-email');
        if (emailElement) {
            emailElement.closest('.leave-form').style.display = 'block';
        }

        // 清除回复ID和回复目标信息
        const reviewForm = document.getElementById('review-form');
        delete reviewForm.dataset.replyTo;
        reviewForm.dataset.replyToId = "0";
        reviewForm.dataset.replyToName = "";
    }

    // 重置表单函数
    function resetForm() {
        document.getElementById('review-name').value = '';
        document.getElementById('review-email').value = '';
        document.getElementById('review-content').value = '';
        document.getElementById('review-rating').value = '';

        // 重置验证码
        YseCaptcha.reset();

        // 取消回复模式
        cancelReply();
    }

    // 初始化产品变体选择功能
    function initProductVariants() {

        // 处理颜色选择器（checkbox）- 确保只能选择一个
        document.addEventListener('change', function(event) {
            if (event.target.matches('.form-check-input[type="checkbox"]')) {
                const checkbox = event.target;
                const attributeName = checkbox.getAttribute('data-attribute');
                const optionName = checkbox.getAttribute('data-option-name');

                if (checkbox.checked) {
                    // 取消同一属性组中的其他选择，确保只能选择一个
                    const sameAttributeInputs = document.querySelectorAll(`input[data-attribute="${attributeName}"]`);
                    sameAttributeInputs.forEach(input => {
                        if (input !== checkbox) {
                            input.checked = false;
                        }
                    });

                    // 存储用户选择到全局变量
                    window.userSelectedVariants[attributeName] = optionName;
                } else {
                    // 从全局变量中移除选择
                    delete window.userSelectedVariants[attributeName];
                }

                updateVariantsCombination();
            }
        });

        // 处理nice-select下拉选择器 - 监听option点击事件
        document.addEventListener('click', function(event) {
            // 检查是否点击了nice-select的option
            if (event.target.matches('.nice-select .option')) {
                const clickedOption = event.target;
                const niceSelectContainer = clickedOption.closest('.nice-select');

                // 找到对应的原生select元素
                let originalSelect = null;
                if (niceSelectContainer && niceSelectContainer.previousElementSibling) {
                    originalSelect = niceSelectContainer.previousElementSibling;
                    if (originalSelect.tagName !== 'SELECT') {
                        originalSelect = null;
                    }
                }

                if (originalSelect && originalSelect.id === 'select_t600') {
                    // 获取点击的选项信息
                    const optionText = clickedOption.textContent.trim();
                    const attributeName = originalSelect.getAttribute('data-attribute');

                    // 直接从原生select中通过文本匹配找到正确的option
                    let correctOption = null;
                    for (let i = 0; i < originalSelect.options.length; i++) {
                        const option = originalSelect.options[i];
                        const optionDataName = option.getAttribute('data-option-name');
                        // 使用data-option-name匹配，因为这个更可靠
                        if (optionDataName === optionText) {
                            correctOption = option;
                            break;
                        }
                    }

                    if (correctOption) {
                        // 手动设置原生select的值
                        originalSelect.value = correctOption.value;

                        // 存储用户实际选择到全局变量
                        window.userSelectedVariants[attributeName] = optionText;
                    }

                    // 延迟执行更新
                    setTimeout(function() {
                        updateVariantsCombination();
                    }, 50);
                }
            }
        });

        // 初始化仓库选择功能
        initWarehouseSelection();

        // 初始化时也执行一次更新
        updateVariantsCombination();
    }

    // 初始化仓库选择功能
    function initWarehouseSelection() {
        // 仓库选择变化事件（处理checkbox类型的仓库选择）
        document.addEventListener('change', function(event) {
            if (event.target.matches('.warehouseInput[type="checkbox"]')) {
                const checkbox = event.target;
                const warehouseName = checkbox.getAttribute('data-warehouse-name');
                const warehouseId = checkbox.getAttribute('data-warehouse-id');

                if (checkbox.checked) {
                    // 取消其他仓库的选择，确保只能选择一个
                    const allWarehouseInputs = document.querySelectorAll('.warehouseInput[type="checkbox"]');
                    allWarehouseInputs.forEach(input => {
                        if (input !== checkbox) {
                            input.checked = false;
                        }
                    });

                    // 更新调试信息
                    const debugWarehouse = document.getElementById('debugWarehouse');
                    if (debugWarehouse) {
                        debugWarehouse.textContent = warehouseName + ' (ID: ' + warehouseId + ')';
                    }
                } else {
                    // 如果取消选择，清空调试信息
                    const debugWarehouse = document.getElementById('debugWarehouse');
                    if (debugWarehouse) {
                        debugWarehouse.textContent = '未选择';
                    }
                }
            }
        });

        // 初始化仓库显示
        initWarehouseDisplay();
    }

    // 初始化仓库显示
    function initWarehouseDisplay() {
        // 获取选中的仓库（包括隐藏的单仓库情况）
        const selectedWarehouse = document.querySelector('.warehouseInput[checked], .warehouseInput:checked');

        if (selectedWarehouse) {
            const warehouseName = selectedWarehouse.getAttribute('data-warehouse-name');
            const warehouseId = selectedWarehouse.getAttribute('data-warehouse-id');

            // 更新调试信息
            const debugWarehouse = document.getElementById('debugWarehouse');
            if (debugWarehouse) {
                debugWarehouse.textContent = warehouseName + ' (ID: ' + warehouseId + ')';
            }
        } else {
            const debugWarehouse = document.getElementById('debugWarehouse');
            if (debugWarehouse) {
                debugWarehouse.textContent = '无可选仓库';
            }
        }
    }

    // 获取选中的仓库ID
    function getSelectedWarehouseId() {
        const selectedWarehouse = document.querySelector('.warehouseInput[checked], .warehouseInput:checked');
        return selectedWarehouse ? parseInt(selectedWarehouse.getAttribute('data-warehouse-id'), 10) : 0;
    }

    // 更新变体组合和匹配VariantsId
    function updateVariantsCombination() {
        // 获取产品的组合模式
        const isCombinationRaw = '{{ Model.Product.IsCombination }}';
        let isCombination = 1; // 默认为1（多规格模式）

        if (isCombinationRaw && isCombinationRaw !== '' && isCombinationRaw !== 'null' && isCombinationRaw !== 'undefined') {
            isCombination = parseInt(isCombinationRaw, 10);
            if (isNaN(isCombination)) {
                isCombination = 1;
            }
        }

        // 从全局变量获取所有用户选择的变体值
        const selectedVariants = [];

        // 遍历所有用户选择的变体
        for (const attributeName in window.userSelectedVariants) {
            const selectedValue = window.userSelectedVariants[attributeName];
            if (selectedValue) {
                selectedVariants.push(selectedValue.trim());
            }
        }

        // 生成组合字符串（用 / 分割）
        const combinationString = selectedVariants.join(' / ');
        let matchedVariantsId = '';

        if (window.productVariants && selectedVariants.length > 0) {
            if (isCombination === 0) {
                // 单规格模式：通常只有一个变体，直接使用第一个
                if (window.productVariants.length > 0) {
                    matchedVariantsId = window.productVariants[0].VariantsId || '';
                }
            } else if (isCombination === 2) {
                // 多规格加价模式：按属性组的固定顺序匹配，而不是按用户选择顺序
                const matchedIds = [];

                // 按属性组的顺序获取选中的值（先颜色，后尺寸）
                // 1. 先处理颜色属性
                const colorAttributes = ['颜色', 'Color', '色彩'];
                for (const attrName of colorAttributes) {
                    if (window.userSelectedVariants[attrName]) {
                        const selectedValue = window.userSelectedVariants[attrName];
                        let foundId = '';

                        // 先尝试精确匹配
                        for (const variant of window.productVariants) {
                            // 精确匹配Title
                            if (variant.Title && variant.Title.trim() === selectedValue) {
                                foundId = variant.VariantsId || '';
                                break;
                            }

                            // 精确匹配AttrName
                            if (variant.AttrName && variant.AttrName.trim() === selectedValue) {
                                foundId = variant.VariantsId || '';
                                break;
                            }
                        }

                        // 如果精确匹配没找到，再尝试部分匹配
                        if (!foundId) {
                            for (const variant of window.productVariants) {
                                if (variant.Title && variant.Title.includes(selectedValue)) {
                                    foundId = variant.VariantsId || '';
                                    break;
                                }
                            }
                        }

                        if (foundId) {
                            matchedIds.push(foundId);
                        }
                        break; // 找到颜色属性就跳出
                    }
                }

                // 2. 再处理尺寸属性
                const sizeAttributes = ['尺寸', 'Size', '大小'];
                for (const attrName of sizeAttributes) {
                    if (window.userSelectedVariants[attrName]) {
                        const selectedValue = window.userSelectedVariants[attrName];
                        let foundId = '';

                        // 先尝试精确匹配
                        for (const variant of window.productVariants) {
                            // 精确匹配Title
                            if (variant.Title && variant.Title.trim() === selectedValue) {
                                foundId = variant.VariantsId || '';
                                break;
                            }

                            // 精确匹配AttrName
                            if (variant.AttrName && variant.AttrName.trim() === selectedValue) {
                                foundId = variant.VariantsId || '';
                                break;
                            }
                        }

                        // 如果精确匹配没找到，再尝试部分匹配
                        if (!foundId) {
                            for (const variant of window.productVariants) {
                                if (variant.Title && variant.Title.includes(selectedValue)) {
                                    foundId = variant.VariantsId || '';
                                    break;
                                }
                            }
                        }

                        if (foundId) {
                            matchedIds.push(foundId);
                        }
                        break; // 找到尺寸属性就跳出
                    }
                }

                // 组合所有匹配的ID
                matchedVariantsId = matchedIds.join(',');
            } else if (isCombination === 1) {
                // 多规格模式：使用组合匹配逻辑，变体ID只有一个
                let matchedVariant = null;

                window.productVariants.forEach((variant, index) => {
                    if (!variant.Title && !variant.AttrName) {
                        return;
                    }

                    // 检查Title字段
                    let titleMatch = false;
                    if (variant.Title) {
                        titleMatch = checkVariantMatch(variant.Title, selectedVariants);
                    }

                    // 检查AttrName字段
                    let attrNameMatch = false;
                    if (variant.AttrName) {
                        attrNameMatch = checkVariantMatch(variant.AttrName, selectedVariants);
                    }

                    if (titleMatch || attrNameMatch) {
                        matchedVariant = variant;
                        matchedVariantsId = variant.VariantsId || '';
                    }
                });
            }
        } else if (window.productVariants && selectedVariants.length === 0 && isCombination === 0) {
            // 单规格模式下，即使没有选择也可能需要返回默认变体
            if (window.productVariants.length > 0) {
                matchedVariantsId = window.productVariants[0].VariantsId || '';
            }
        }

        // 更新调试显示
        const debugSelectedAttributes = document.getElementById('debugSelectedAttributes');
        const debugVariantsId = document.getElementById('debugVariantsId');
        const hiddenCombination = document.getElementById('selectedVariantsCombination');
        const hiddenVariantsId = document.getElementById('matchedVariantsId');

        if (debugSelectedAttributes) {
            debugSelectedAttributes.textContent = combinationString || '未选择';
        }

        if (debugVariantsId) {
            debugVariantsId.textContent = matchedVariantsId || '未匹配';
        }

        // 更新隐藏字段
        if (hiddenCombination) {
            hiddenCombination.value = combinationString || '';
        }

        if (hiddenVariantsId) {
            hiddenVariantsId.value = matchedVariantsId || '';
        }
    }

    // 检查变体是否匹配（不区分顺序）
    function checkVariantMatch(variantString, selectedVariants) {
        if (!variantString || selectedVariants.length === 0) {
            return false;
        }

        // 将变体字符串按 / 分割并清理空格
        const variantParts = variantString.split('/').map(part => part.trim()).filter(part => part);

        // 检查长度是否一致
        if (variantParts.length !== selectedVariants.length) {
            return false;
        }

        // 检查是否所有选中的变体都在变体字符串中
        const allMatch = selectedVariants.every(selected => {
            return variantParts.some(part => part === selected);
        });

        return allMatch;
    }

    //跳转评论
    document.querySelector(".rating-count").addEventListener('click', function () {
        // 隐藏所有 tab-content
        document.querySelectorAll('.tab-pane').forEach(function (content) {
            var dataId = content.getAttribute('data-id');
            if (dataId == "reviews") {
                content.classList.add('show');
                content.classList.add('active');
            } else {
                content.classList.remove('show');
                content.classList.remove('active');
            }
        });
        document.querySelectorAll('.nav-tabs button').forEach(function (tab) {
            var dataId = tab.getAttribute('data-id');
            // 获取当前 tab 的 rel 属性
            var activeTab = tab.getAttribute('data-bs-target');
            // 移除所有 tab 的 active 类
            document.querySelectorAll('.nav-tabs button').forEach(function (li) {
                li.classList.remove('active');
            });
            if (dataId == "reviews") {
                // 当前 tab 添加 active 类
                tab.classList.add('active');
            }
        });
    })

    // 添加到购物车功能
    function addToCart() {
        const quantity = document.querySelector('.quantity').value || 1;
        const variantsId = document.getElementById('matchedVariantsId').value || '';
        const ovId = getSelectedWarehouseId();
        const productId = parseInt('{{ Model.Product.ProductId }}', 10);

        const addCartData = {
            ProductId: productId,
            variantsId: variantsId,
            Nums: parseInt(quantity, 10),
            cartType: 1,
            ovId: ovId
        };

        // 发送加购数据到服务器
        fetch('/Cart/AddCart', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(addCartData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status) {
                //更新购物车数量角标
                $(".site-cart-count").text(data.otherData);

                customize_pop.success(data.msg || '添加成功', null, null, {showIcon: false});
            } else {
                customize_pop.error('Failed to add to cart: ' + (data.msg || 'Unknown error'), null, null, {showIcon: true});
            }
        })
        .catch(error => {
            customize_pop.error('Please try again later.', null, null, {showIcon: true});
        });
    }


    //立即购买
    function buyItNow() {

        const quantity = document.querySelector('.quantity').value || 1;
        const variantsId = document.getElementById('matchedVariantsId').value || '';
        var cartType = 2;
        //variantsId ="10050,10052"
        var ovId = getSelectedWarehouseId(); // 使用选中的仓库ID
        var ProductId = parseInt('{{ Model.Product.ProductId }}', 10);

        const addCartData = {
            ProductId: ProductId,
            variantsId: variantsId,
            Nums: parseInt(quantity, 10),
            cartType: cartType,
            ovId: ovId,
        };

        //var param = { entity: addCartData };

        //console.log(param);

        // 发送加购数据到服务器
        $.ajax({
            url: '/Cart/BuyItNow',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(addCartData),
            success: function (data) {
                //debugger;
                // 关闭加载提示
                customize_pop.loadingClose();

                var msg = data.msg;
                if (msg != '') {
                    msg = data.msg;
                }
                if (data.status) {
                    //更新购物车数量角标
                    $(".site-cart-count").text(data.otherData);

                    //结算
                    window.location.href = data.data.location;

                } else {
                    customize_pop.error('Failed to buy it now: ' + (data.msg || 'Unknown error'), null, null, { showIcon: true });
                }
            },
            error: function (xhr, status, error) {
                // 关闭加载提示
                customize_pop.loadingClose();
                customize_pop.error('Please try again later.', null, null, { showIcon: true });
            },
            complete: function () {
                // 可以在这里添加完成后的处理逻辑
            }
        });
    };


    // 初始化购物车按钮事件
    document.addEventListener('DOMContentLoaded', function() {
        const addToCartBtn = document.querySelector('.add-to-cart-btn');
        if (addToCartBtn) {
            addToCartBtn.addEventListener('click', function(e) {
                e.preventDefault();
                addToCart();
            });
        }
    });

    // 确保函数全局可访问
    window.replyToComment = replyToComment;
    window.cancelReply = cancelReply;
    window.submitReview = submitReview;
    window.initProductVariants = initProductVariants;
    window.updateVariantsCombination = updateVariantsCombination;
    window.addToCart = addToCart;
    window.getSelectedWarehouseId = getSelectedWarehouseId;
</script>

<!-- 添加自定义样式 -->
<style>
    /* 仓库选择样式 */
    .shop-single-warehouse {
        margin-bottom: 20px;
    }

        .shop-single-warehouse h6 {
            margin-bottom: 15px;
            font-weight: 600;
            color: #333;
        }

    .shop-checkbox-list.warehouse {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        list-style: none;
        padding: 0;
        margin: 0;
    }

        .shop-checkbox-list.warehouse li {
            margin: 0;
        }

        .shop-checkbox-list.warehouse .form-check {
            margin: 0;
            padding: 0;
        }

        .shop-checkbox-list.warehouse .form-check-input {
            display: none;
        }

        .shop-checkbox-list.warehouse .form-check-label {
            display: inline-block;
            padding: 8px 16px;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            background-color: #fff;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0;
        }

            .shop-checkbox-list.warehouse .form-check-label:hover {
                border-color: #007bff;
                background-color: #f8f9fa;
            }

        .shop-checkbox-list.warehouse .form-check-input:checked + .form-check-label {
            border-color: #007bff;
            background-color: #007bff;
            color: #fff;
        }

    .single-warehouse {
        display: inline-block;
        padding: 8px 16px;
        border: 2px solid #28a745;
        border-radius: 5px;
        background-color: #28a745;
        color: #fff;
        font-weight: 500;
    }

    /* 回复容器样式 */
    .replies-container {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #e9ecef;
    }

    /* 回复项样式 */
    .reply-item {
        background-color: #f8f9fa;
        border-left: 3px solid #007bff;
        padding: 12px 15px;
        margin-bottom: 10px;
        border-radius: 0 5px 5px 0;
    }

    /* 回复头部样式 */
    .reply-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;
    }

        .reply-header strong {
            color: #333;
            margin-right: 8px;
        }

    .reply-date {
        color: #6c757d;
        font-size: 12px;
        margin-left: auto;
    }

    /* 回复内容样式 */
    .reply-content {
        color: #555;
        line-height: 1.5;
        margin-bottom: 8px;
    }

    /* 回复操作样式 */
    .reply-actions {
        text-align: right;
    }

    .reply-btn {
        color: #007bff;
        text-decoration: none;
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 3px;
        transition: all 0.3s ease;
    }

        .reply-btn:hover {
            background-color: #e7f3ff;
            color: #0056b3;
        }

    /* 回复标签样式 */
    .reply-to-tag {
        color: #007bff;
        font-weight: 600;
        margin-right: 5px;
    }

    /* 回复按钮样式 */
    .blog-comments-btn a {
        display: inline-block;
        padding: 5px 10px;
        margin-top: 5px;
        color: #0056b3;
        border-radius: 3px;
        text-decoration: none;
        transition: all 0.3s ease;
    }

        .blog-comments-btn a:hover {
            background-color: #e9ecef;
        }

    /* 管理员标记样式 */
    .admin-badge {
        background-color: #28a745;
        color: white;
        padding: 2px 5px;
        border-radius: 3px;
        font-size: 11px;
        margin-left: 5px;
        vertical-align: middle;
    }

    /* 移除旧的回复样式 */
    .blog-comments-reply {
        display: none;
    }
</style>

<!-- 导入图片处理助手 -->
{% comment %}<script src="/businessJs/imageUrlHelper.js"></script>{% endcomment %}

{% comment %}<script>{% endcomment %}
    {% comment %}// 页面加载完成后处理所有图片URL{% endcomment %}
    {% comment %}document.addEventListener('DOMContentLoaded', function() {{% endcomment %}
        {% comment %}// 处理产品详情页面中的所有图片{% endcomment %}
        {% comment %}const productDetailImages = document.querySelectorAll('.shop-single-gallery img, .flexslider-thumbnails img');{% endcomment %}
        {% comment %}productDetailImages.forEach(function(img) {{% endcomment %}
            {% comment %}if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {{% endcomment %}
                {% comment %}img.src = ImageUrlHelper.getMediumUrl(img.src);{% endcomment %}
            {% comment %}}{% endcomment %}
            {% comment %}if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {{% endcomment %}
                {% comment %}img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);{% endcomment %}
            {% comment %}}{% endcomment %}
            {% comment %}if (img.dataset.thumb && !ImageUrlHelper.hasOssParams(img.dataset.thumb)) {{% endcomment %}
                {% comment %}img.dataset.thumb = ImageUrlHelper.getMediumUrl(img.dataset.thumb);{% endcomment %}
            {% comment %}}{% endcomment %}
        {% comment %}});{% endcomment %}

        {% comment %}// 处理快速预览模态框中的图片{% endcomment %}
        {% comment %}const quickViewImages = document.querySelectorAll('#quickview img');{% endcomment %}
        {% comment %}quickViewImages.forEach(function(img) {{% endcomment %}
            {% comment %}if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {{% endcomment %}
                {% comment %}img.src = ImageUrlHelper.getMediumUrl(img.src);{% endcomment %}
            {% comment %}}{% endcomment %}
            {% comment %}if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {{% endcomment %}
                {% comment %}img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);{% endcomment %}
            {% comment %}}{% endcomment %}
        {% comment %}});{% endcomment %}
    {% comment %}});{% endcomment %}
{% comment %}</script>{% endcomment %}

