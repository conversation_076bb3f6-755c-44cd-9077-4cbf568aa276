<div class="login-area py-90">
    <div class="container">
        <div class="col-md-7 col-lg-5 mx-auto">
            <div class="login-form">
                <div class="login-header">
                    <img src="/assets/img/logo/logo.png" alt>
                    <p>{{ "user.forgot.signIndex"|translate}}</p>
                </div>
                <form hx-post="/Account/UserLogin" 
                      hx-trigger="submit throttle:2s"
                      id="loginForm" method="post" >
                    <div id="response" hx-target="this" hx-swap="outerHTML">

                        <div class="form-group error" hx-target="this" hx-swap="outerHTML">
                            <label>{{ "user.account.email_addr"|translate}}</label>
                            <input type="email" class="form-control" name="Email" placeholder="{{ "user.global.distributorEmail"|translate}}">
                        </div>
                        <div class="form-group">
                            <label>{{ "user.global.password"|translate}}</label>
                            <input type="password" class="form-control" name="Password" placeholder="{{ "user.global.password"|translate}}">
                        </div>
                        <div class="d-flex justify-content-between mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="true" name="RememberMe" id="remember">
                                <label class="form-check-label" for="remember">
                                    {{ "user.login.remember_me"|translate}}
                                </label>

                            </div>
                            <a href="/Account/FindPwd" class="forgot-pass">{{ "user.login.forgetpwd"|translate}}</a>
                        </div>
                        <input type="hidden" name="JumpUrl" value="{{Model.jumpUrl}}" />
                        <div class="d-flex align-items-center">
                            <button type="submit" hx-on="htmx:configRequest: this.disabled = true; this.innerText = 'Submitting...'" class="theme-btn"><i class="far fa-sign-in"></i> {{ "user.login.log_in"|translate}}</button>
                        </div>
                    </div>
                </form>
                <div class="login-footer">
                    <p>{{ "user.register.createAccount"|translate}} <a href="/Account/SignUp" hx-get="/Account/SignUp" hx-target="#main" hx-push-url="true" hx-swap="innerHTML">{{ "user.global.sign_up"|translate}}.</a></p>
                    <div class="social-login">
                        <span class="social-divider">{{ "web.global.or"|translate}}</span>
                        <p>Continue with social media</p>
                        <div class="social-login-list">
                            <a href="#" class="fb-auth"><i class="fab fa-facebook-f"></i> {{ "web.global.facebookStr"|translate}}</a>
                            <a href="#" class="gl-auth"><i class="fab fa-google"></i> {{ "web.global.googleStr"|translate}}</a>
                            <a href="#" class="tw-auth"><i class="fab fa-x-twitter"></i> {{ "web.global.twitterStr"|translate}}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

