<div class="user-area bg pt-100 pb-80">
    <div class="container">
        <div class="row">
            <div class="col-lg-3">
                {% assign sidebar= '/Themes/'| append: theme | append:'/Account/Sidebar' %}
                {% include sidebar -%}
                {% assign orderStatus = Model.OrderStatus | default: 0 %}
            </div>
            <div class="col-lg-9">
                <div class="user-wrapper">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="user-card">
                                <div class="user-card-header">
                                    <h4 class="user-card-title">{{ "user.account.orderTitle"|translate}}</h4>

                                   
                                    <div class="user-card-header-right">
                                        <div class="user-card-filter">
                                            <select class="select" id="OrderStatus" onchange="updateOrderStatus()" style="display: none;">
                                                <option value="0">{{ "web.global.default"|translate}}</option>
                                                {% if Model.statusList != null and Model.statusList.size > 0 %}
                                                    {% for item in Model.statusList %}
                                                <option value="{{ item.Value }}"
                                                        {% if item.Value==orderStatus %}selected{% endif %}>
                                                    {{ 'user.account.OrderStatusAry_'|append:item.Value | translate }}
                                                </option>
                                                    {% endfor %}
                                                {% endif %}
                                            </select>
                                        </div>
                                        <div class="user-card-search">
                                            <div class="form-group">
                                                <input type="text" id="search-keyword" class="form-control" placeholder="{{ "blog.global.searchNote"|translate}}..." value="{{ Model.Keyword }}">
                                                <i class="far fa-search"></i>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="table-responsive">
                                    <!-- 空数据样式 -->
                                    <!-- <div class="empty-date pt-100 pb-100"><img src="{{static_path}}/assets/img/Home/empty.png" alt></div> -->
                                    <table class="table table-borderless text-nowrap">
                                        <thead>
                                            <tr>
                                                <th>#{{ "user.account.order_no"|translate}}</th>
                                                <th>{{ "user.account.orderDate"|translate}}</th>
                                                <th>{{ "user.account.order_total"|translate}}</th>
                                                <th>{{ "user.account.order_status"|translate}}</th>
                                                <th>{{ "web.global.action"|translate}}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                         {% if Model.MyOrders != null and Model.MyOrders.size > 0 %}
                                            {% for orders in Model.MyOrders %}
                                                <tr class="closestOrder">
                                                    <td><span class="table-list-code">#{{ orders.OId }}</span></td>
                                                    <td>{{ orders.OrderTimeStr }}</td>
                                                    <td>{{ orders.OrderSymbol }}{{ orders.OrderSum }}</td>
                                                    <td>
                                                      {% if orders.OrderStatus == 1 %}
                                                        <span class="badge badge-info">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                      {% elsif orders.OrderStatus == 2 %}
                                                        <span class="badge badge-primary">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                      {% elsif orders.OrderStatus == 3 %}
                                                        <span class="badge badge-danger">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                      {% elsif orders.OrderStatus == 4 %}
                                                        <span class="badge badge-success">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                      {% elsif orders.OrderStatus == 5 %}
                                                        <span class="badge badge-success">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                      {% elsif orders.OrderStatus == 6 %}
                                                        <span class="badge badge-success">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                      {% elsif orders.OrderStatus == 7 %}
                                                        <span class="badge badge-danger">{{ 'user.account.OrderStatusAry_'|append:orders.OrderStatus | translate }}</span>
                                                      {% endif %}
                                                    </td>
                                                    <td>
                                                        <a href="/Account/OrderDetail/{{ orders.OrderId }}" class="btn btn-outline-secondary btn-sm rounded-2" data-tooltip="tooltip" title="{{ "user.account.order_details"|translate}}">
                                                            <i class="far fa-eye"></i>
                                                        </a>
                                                      
                                                         <!-- 确认收货 -->
                                                        {% if orders.OrderStatus == 5  %}
                                                        <a class="btn btn-outline-secondary btn-sm rounded-2 confirm_receiving"   value="{{ orders.OrderId }}">{{ "user.account.receiving"|translate}}</a>
                                                        {% endif %}
                                                        <!-- 取消订单 -->
                                                         {% if orders.OrderStatus == 1  %}
                                                        <a href="#" class="btn btn-outline-secondary btn-sm rounded-2 cancel_order"  value="{{ orders.OrderId }}" data-tooltip="tooltip" title="{{ "user.account.cancel_order"|translate}}">
                                                            <i class="far fa-cancel"></i>
                                                        </a>
                                                        {% endif %}
                                                        <!-- 支付订单 -->
                                                        {% if orders.OrderStatus == 1 or orders.OrderStatus == 3 %}
                                                        <a href="/cart/{{ orders.OId }}/info?code={{ orders.OIdToBase64}}" class="btn btn-outline-secondary btn-sm rounded-2 rounded_order" data-tooltip="tooltip" title="{{ "checkout.global.paynow"|translate}}">
                                                            <i class="far fa-wallet"></i>
                                                        </a>
                                                        {% endif %}
                                                          <a href="/account/MyContact/{{ orders.OrderId }}" class="btn btn-outline-secondary btn-sm rounded-2" data-tooltip="tooltip" title="{{ "user.account.Contact"|translate}}">
                                                            <i class="far fa-heat"></i>
                                                        </a>

                                                    </td>
                                                </tr>

                                            {% endfor %}
                                        {% else %}
                                            <div class="col-12 text-center">
                                                <p>{{ "web.tracking.no_orders"|translate}}</p>
                                            </div>
                                        {% endif %}


                                        </tbody>
                                    </table>
                                </div>

                                 <hr class="clear">
                <div class="pagination">
                    <ul>
                        {% if Model.CurrentPage > 1 %}
                            <li class="prev"><a
                                        href="/Account/MyOrders{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ Model.CurrentPage | minus: 1 }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}"><i
                                            class="an an-lg an-angle-left" aria-hidden="true"></i></a></li>
                        {% else %}
                            <li class="prev disabled"><a href="#"><i class="an an-lg an-angle-left"
                                                                     aria-hidden="true"></i></a></li>
                        {% endif %}

                        {% assign startPage = Model.CurrentPage | minus: 2 %}
                        {% if startPage < 1 %}{% assign startPage = 1 %}{% endif %}

                        {% assign endPage = startPage | plus: 4 %}
                        {% if endPage > Model.TotalPages %}{% assign endPage = Model.TotalPages %}{% endif %}

                        {% if startPage > 1 %}
                            <li>
                                <a href="/Account/MyOrders{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page=1{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">1</a>
                            </li>
                            {% if startPage > 2 %}
                                <li class="disabled"><a href="#">...</a></li>{% endif %}
                        {% endif %}

                        {% for i in (startPage..endPage) %}
                            <li {% if i == Model.CurrentPage %}class="active"{% endif %}>
                                <a href="/Account/MyOrders{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ i }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">{{ i }}</a>
                            </li>
                        {% endfor %}

                        {% if endPage < Model.TotalPages %}
                            {% assign lastPageMinusOne = Model.TotalPages | minus: 1 %}
                            {% if endPage < lastPageMinusOne %}
                                <li class="disabled"><a href="#">...</a></li>{% endif %}
                            <li>
                                <a href="/Account/MyOrders{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ Model.TotalPages }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">{{ Model.TotalPages }}</a>
                            </li>
                        {% endif %}

                        {% if Model.CurrentPage < Model.TotalPages %}
                            <li class="next"><a
                                        href="/Account/MyOrders{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ Model.CurrentPage | plus: 1 }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}"><i
                                            class="an an-lg an-angle-right" aria-hidden="true"></i></a></li>
                        {% else %}
                            <li class="next disabled"><a href="#"><i class="an an-lg an-angle-right"
                                                                     aria-hidden="true"></i></a></li>
                        {% endif %}
                    </ul>
                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入自定义弹窗JS库 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<script>
    // 页面切换函数
    function changePage(pageNumber) {
        if (pageNumber < 1 || pageNumber > {{ Model.TotalPages }} || pageNumber === {{ Model.CurrentPage }}) {
            return;
        }

        const currentUrl = new URL(window.location.href);
        const params = new URLSearchParams(currentUrl.search);
        params.set('page', pageNumber); // 更新页码
    
        const newUrl = new URL(currentUrl.pathname, window.location.origin);
        newUrl.search = params.toString();
    
        htmx.ajax('GET', newUrl.href, '#main');
        history.pushState(null, '', newUrl.href);
    }

    // 搜索函数
    function searchOrder() {
        const keyword = document.getElementById('search-keyword').value;
        const currentUrl = new URL(window.location.href);
        const params = new URLSearchParams(currentUrl.search);
    
        if (keyword) {
            params.set('keyword', encodeURIComponent(keyword));
        } else {
            params.delete('keyword'); // 清除空关键词
        }
    params.delete('page');
        const newUrl = new URL(currentUrl.pathname, window.location.origin);
        newUrl.search = params.toString();
    
        window.location.href=newUrl.href;
    }
    
    // 为搜索输入框添加回车键搜索功能
    document.getElementById('search-keyword').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            searchOrder();
        }
    });

    function updateOrderStatus() {
        // 获取选中的订单状态值
        const orderStatus = document.getElementById('OrderStatus').value;
    
        // 解析当前URL参数
        const currentUrl = new URL(window.location.href);
        const params = new URLSearchParams(currentUrl.search);
    
        // 更新或添加OrderStatus参数
        params.set('OrderStatus', orderStatus);
    params.delete('page');
        // 构建新URL
        const newUrl = new URL(currentUrl.pathname, window.location.origin);
        newUrl.search = params.toString();
   
       window.location.href=newUrl.href;

    }

    // 原有代码保持不变，在文档底部添加以下内容
    document.addEventListener('DOMContentLoaded', function() {
        // 获取URL参数中的OrderStatus
        const urlParams = new URLSearchParams(window.location.search);
        const orderStatus = urlParams.get('OrderStatus');
 
        if (orderStatus) {
            const select = document.getElementById('OrderStatus');
     
            // 设置select的value
            select.value = orderStatus;
            // 强制更新niceSelect显示
            jQuery(select).niceSelect('update');
        }



    // 监听表单提交事件
    $('.cancel_order').click(function(e){
        e.preventDefault();
        var form = $(this);
        var formData = {
            OrderId: form.attr('value')
        };

          const productItem = this.closest('.closestOrder');
           const badgeElement = productItem.querySelector('.badge-info');
           const cancelElement = productItem.querySelector('.cancel_order');
           const roundedElement = productItem.querySelector('.rounded_order');
          customize_pop.confirm('{{'user.account.cancelOrder'|translate}}', function() {



        sendData(formData);
        function sendData(data) {
            $.ajax({
                url: '/api/account/comment/cancelorder',
                type: 'POST',
                data: data,
                success: function(response) {
                    if(response.success) {
                       customize_pop.success('{{'web.global.submit_success'|translate}}');
                       // 先获取元素
                       
    
                        // 修改文本内容
                        badgeElement.textContent = '{{ 'user.account.OrderStatusAry_'|append:7 | translate }}';  // 设置显示的文本
    
                        // 修改样式类（推荐方式）
                        badgeElement.classList.remove('badge-info');
                        badgeElement.classList.add('badge-danger');
                        cancelElement.style.display = 'none'; 
                        roundedElement.style.display = 'none'; 
                    } else {
                        
                          // 显示错误消息
                            customize_pop.error(response.message || 'Failed to cancel item. Please try again');
                    }
                },
                error: function(xhr) {
                    console.error('AJAX Error:', xhr.responseText);
                     customize_pop.error('An error occurred. Please try again later');
                }
            });
        }

          }, null, '{{'web.global.confirm'|translate}}', '{{'web.global.cancel'|translate}}', '{{'web.global.confirm'|translate}}', false);

    });


         // 确认收货
    $('.confirm_receiving').click(function(e){
        e.preventDefault();
        var form = $(this);
        var formData = {
            OrderId: form.attr('value')
        };
        
          const productItem = this.closest('.closestOrder');
           const badgeElement = productItem.querySelector('.badge-success');
           const cancelElement = productItem.querySelector('.confirm_receiving');
          customize_pop.confirm('{{'user.account.sure'|translate}}', function() {



        sendData(formData);
        function sendData(data) {
            $.ajax({
                url: '/api/account/comment/receiving',
                type: 'POST',
                data: data,
                success: function(response) {
                    if(response.success) {
                       customize_pop.success('{{'web.global.submit_success'|translate}}');
                       // 先获取元素
                        // 修改文本内容
                        badgeElement.textContent = '{{ 'user.account.OrderStatusAry_'|append:6 | translate }}';  // 设置显示的文本
    
                        // 修改样式类（推荐方式）
                       
                        cancelElement.style.display = 'none'; 
                    } else {
                        
                          // 显示错误消息
                            customize_pop.error(response.message || 'Failed to sure item. Please try again');
                    }
                },
                error: function(xhr) {
                    console.error('AJAX Error:', xhr.responseText);
                     customize_pop.error('An error occurred. Please try again later');
                }
            });
        }

          }, null, '{{'web.global.confirm'|translate}}', '{{'web.global.cancel'|translate}}', '{{'web.global.confirm'|translate}}', false);

    });










 });









</script>

