{% layout '' %}
<div id="response" hx-target="this" hx-swap="outerHTML">
    {% if ViewData["ErrCount"]>0 %}
    <div class="alert alert-danger">
        <ul>
            {% for errItem in ViewData["Errors"] %}
            <li>{{ errItem }}</li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}
    <form method="post" hx-post="/Account/UserSignUp"
          hx-trigger="submit throttle:2s"
          accept-charset="UTF-8" class="contact-form">

        <div class="row">
            <div class="col-12 col-sm-12 col-md-12 col-lg-12 mb-2">
                <div class="form-group">
                    <label for="CustomerEmail">{{ "user.login.emailAddress"|translate}} <span class="required">*</span></label>
                    <input id="CustomerEmail" type="email" name="Email" placeholder="{{ "user.account.email_addr"|translate}}" class="form-control" value="{{Model.Email}}" onkeyup="this.setCustomValidity(''); $(this).removeClass('is-invalid');"
                           hx-on:htmx:validation:validate="if(this.value == '') {
                            this.setCustomValidity('Please enter your email')
                               $(this).addClass('is-invalid') }else{$(this).removeClass('is-invalid')}">
                </div>
            </div>
        </div>


        <div class="row">
            <div class="col-12 col-sm-12 col-md-6 col-lg-6 mb-2">
                <div class="form-group">
                    <label for="CustomerPassword">{{ "user.global.password"|translate}} <span class="required">*</span></label>
                    <input id="CustomerPassword" type="password" name="Password" class="form-control" value="{{Model.Password}}" placeholder="{{ "user.global.password"|translate}}" onkeyup="this.setCustomValidity(''); $(this).removeClass('is-invalid');"
                           hx-on:htmx:validation:validate="if(this.value == '') {
                            this.setCustomValidity('Please enter your password')
                               $(this).addClass('is-invalid') }else{$(this).removeClass('is-invalid')}">
                </div>
            </div>
            <div class="col-12 col-sm-12 col-md-6 col-lg-6 mb-2">
                <div class="form-group">
                    <label for="CustomerConfirmPassword">{{ "user.account.rePWD"|translate}} <span class="required">*</span></label>
                    <input id="CustomerConfirmPassword" type="Password" name="ReEnterPassword" class="form-control" value="{{Model.ReEnterPassword}}" placeholder="{{ "user.account.rePWD"|translate}}" onkeyup="this.setCustomValidity(''); $(this).removeClass('is-invalid');"
                           hx-on:htmx:validation:validate="if(this.value == '') {
            this.setCustomValidity('Please enter your Confirm password')
               $(this).addClass('is-invalid') }else{$(this).removeClass('is-invalid')}">
                </div>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="form-group form-check col-12 col-sm-12 col-md-12 col-lg-12 mb-2">
                <label class="form-check-label padding-15px-left" for="Agree">
                    <input type="checkbox" class="form-check-box" value="true" name="Agree" id="Agree">
                    I agree with the <a href="#">Terms Of Service.</a>
                </label>
            </div>
        </div>
        <div class="row">
            <div class="text-left col-6 col-sm-6 col-md-6 col-lg-6 mt-3">
                <input type="submit" class="btn mb-3 theme-btn" value="{{ "web.global.submit"|translate}}">
            </div>
            <div class="text-right col-6 col-sm-6 col-md-6 col-lg-6 mt-3">
                <a href="/Account/SignIn">« {{ "user.global.sign_in"|translate}}</a>
            </div>
        </div>
        <input type="hidden" name="ReturnUrl" value="{{ViewData["ReturnUrl"]}}" />
    </form>
</div>