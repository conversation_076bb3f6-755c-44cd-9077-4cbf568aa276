
<div class="page-header">
    <div class="page-title">
    <h1>
        {% if Model.AId==0  %}
            <span>{{ "web.global.add"|translate}}</span>
        {% else %}
            <span>{{ "web.global.edit"|translate}}</span>
        {% endif %}
        {% if Model.Billing==1  %}
            <span>{{ "user.account.bill_addr"|translate}}</span>
        {% else %}
            <span>{{ "user.account.ship_addr"|translate}}</span>
        {% endif %}
    </h1>
    </div>
</div>
<div class="container">
    <div class="row mb-5">
        {% assign sidebar= '/Themes/'| append: theme | append:'/Account/Sidebar' %}
        {% include sidebar -%}

        <div class="col-xs-10 col-lg-10 col-md-12">
            <!-- Tab panes -->
            <div class="dashboard-content padding-30px-all md-padding-15px-all" style="">
                <!-- Address -->
                    <div class="row">
                        <div class="col-6 col-lg-6">
                        <h3>
                        {% if Model.AId==0  %}
                                <span>{{ "web.global.add"|translate}}</span>
                        {% else %}
                            <span>{{ "web.global.edit"|translate}}</span>
                        {% endif %}
                        {% if Model.Billing==1  %}
                            <span>{{ "user.account.bill_addr"|translate}}</span>
                        {% else %}
                            <span>{{ "user.account.ship_addr"|translate}}</span>
                        {% endif %}
                        </h3>
                        </div>
                        <div class="col-6 col-lg-6" style="text-align:right;"><a href="/Account/MyAddress" class="btn cart-continue theme-btn">{{ "user.account.addressTitle"|translate}} </a></div>
                        <div class="col-12 col-lg-12">
                            <form action="#" id="Addaddress">
                                <fieldset>
                                    <div class="row">
                                        <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                            <label for="input-name">{{ "user.account.firstname"|translate}} <span class="required-f">*</span></label>
                                            <input name="FirstName" value="{{ Model.address.FirstName }}" type="text">
                                        </div>
                                        <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                            <label for="input-lastname">{{ "user.account.lastname"|translate}} <span class="required-f">*</span></label>
                                            <input name="LastName" value="{{ Model.address.LastName }}" type="text">
                                        </div>
                                    </div>
                                    

                                    <div class="row">
                                        <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                            <label for="input-email">{{ "user.account.country"|translate}} <span class="required-f">*</span></label>
                                            <select class="form-control CountryList">
                                                 {% if Model.CountryList != null and Model.CountryList.size > 0 %}
                                                   {% for item in Model.CountryList %}
                                                     {% if item.Value == Model.address.CId %}
                                                        <option selected value="{{ item.Value }}">{{ item.Name }}</option>
                                                     {% else %}
                                                        <option value="{{ item.Value }}">{{ item.Name }}</option>
                                                    {% endif %}
                                                   {% endfor %}
                                               {% endif %}
                                            </select>
                                        </div>



                                        <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3 IsProvince"   style=" {% if Model.IsProvince %} display: block; {% else %} display: none;{% endif %}">
                                            <label for="input-regionState">{{ "user.account.province_state"|translate}} </label>
                                             <select class="form-control ProvinceList">
                                                {% if Model.ProvinceList != null and Model.ProvinceList.size > 0 %}
                                                   {% for item in Model.ProvinceList %}
                                                    {% if item.SId == Model.address.SId %}
                                                        <option selected value="{{ item.SId }}">{{ item.States }}</option>
                                                     {% else %}
                                                         <option value="{{ item.SId }}">{{ item.States }}</option>
                                                    {% endif %}

                                                   {% endfor %}
                                                {% endif %}
				                            </select>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                            <label for="input-phonenumber">{{ "user.account.phoneNum"|translate}} <span class="required-f">*</span></label>
                                            <input name="PhoneNumber" value="{{ Model.address.PhoneNumber }}" type="text">
                                        </div>
                                        <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                            <label for="input-addressLine1">{{ "user.account.address1"|translate}} 1<span class="required-f">*</span></label>
                                            <input name="AddressLine1" value="{{ Model.address.AddressLine1 }}" type="text">
                                        </div>
                                    </div>


                                    <div class="row">
                                        <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                            <label for="input-addressLine2">{{ "user.account.address1"|translate}} 2<span class="required-f">*</span></label>
                                            <input name="AddressLine2" value="{{ Model.address.AddressLine2 }}" type="text">
                                        </div>
                                        <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                            <label for="input-city">{{ "user.account.city"|translate}} <span class="required-f">*</span></label>
                                            <input name="City" value="{{ Model.address.City }}" type="text">
                                        </div>
                                    </div>

                                    <div class="row">
                                        
                                         {% if Model.address.IsBillingAddress  %}
                                           <div class="form-group col-md-6 col-lg-6 col-xl-6 mb-3">
                                            <label for="input-zipcode">{{ "user.account.zip_code"|translate}} <span class="required-f">*</span></label>
                                            <input name="ZipCode" value="{{ Model.address.ZipCode }}" type="text">
                                        </div>
                                        {% else %}
                                            <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                            <label for="input-taxCode">{{ "user.account.tax_code"|translate}} <span class="required-f">*</span></label>
                                            <input name="TaxCode" value="{{ Model.address.TaxCode }}" type="text">
                                        </div>
                                        {% endif %}
                                    </div>

                                </fieldset>
                                  <input type="hidden" name="AId" value="{{ Model.AId }}">
                                  <input type="hidden" name="Billing" value="{{ Model.Billing }}">
                                <button type="button" id="SaveAddress" class="btn margin-15px-top btn-primary theme-btn">{{ "checkout.checkout.saved_addresses"|translate}}</button>
                        </form>
                        </div>
                    </div>
                <!-- End Address -->
            </div>
            <!-- End Tab panes -->
        </div>
    </div>
</div>

<script>

    // 原有代码保持不变，在文档底部添加以下内容
    document.addEventListener('DOMContentLoaded', function() {
        
     // 页面加载时获取初始值
  const initialValue = $('.CountryList').val();

  // 监听值变化
  $('.CountryList').on('change', function() {
    const newValue = $(this).val();
      var data = {
            CId: newValue
        };
            $.ajax({
                url: '/api/account/comment/CountryCId',
                type: 'POST',
                data: data,
                success: function(response) {
                    if(response.success) {
                        if(response.isProvince){
                             $('.IsProvince').attr('style', 'display:block');
                            var ProvinceList = response.message;
                            for(var i = 0; i < ProvinceList.length; i++) {
							    $('.ProvinceList').append('<option value="'+ProvinceList[i].sId+'">'+ProvinceList[i].states+'</option>');
						    }
                        }else{
                         $('.IsProvince').attr('style', 'display:none');
                        }
                       
                        
                    } else {
                        console.log('获取失败：' + (response.message || '未知错误'));
                    }
                },
                error: function(xhr) {
                    console.error('AJAX Error:', xhr.responseText);
                    console.log('获取失败：' + (xhr.statusText || '网络错误'));
                }
            });
        
  });


   $('#SaveAddress').click(function(e){
        e.preventDefault();

       
        var data = {
            FirstName: $('input[name="FirstName"]').val(),
            LastName: $('input[name="LastName"]').val(),
            PhoneNumber: $('input[name="PhoneNumber"]').val(),
            AddressLine1: $('input[name="AddressLine1"]').val(),
            AddressLine2: $('input[name="AddressLine2"]').val(),
            CId: $('.CountryList').val(),
            SId: $('.ProvinceList').val(),
            City: $('input[name="City"]').val(),
            ZipCode: $('input[name="ZipCode"]').val(),
            TaxCode: $('input[name="TaxCode"]').val(),
            AId: $('input[name="AId"]').val(),
            Billing: $('input[name="Billing"]').val()
        };

            $.ajax({
                url: '/api/account/comment/SaveAddress',
                type: 'POST',
                data: data,
                success: function(response) {
                    if(response.status) {
                       
                        window.location.href = "/Account/MyAddress"; 
                    } else {
                        console.log('提交失败：' + (response.msg || '未知错误'));
                    }
                },
                error: function(xhr) {
                    console.error('AJAX Error:', xhr.msg);
                    console.log('提交失败：' + (xhr.statusText || '网络错误'));
                }
            });
        
    });

    });



</script>

