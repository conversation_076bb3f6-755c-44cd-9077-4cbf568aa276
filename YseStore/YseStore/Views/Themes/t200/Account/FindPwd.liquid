<!--Body Container-->
<!--<PERSON> Header-->
<div class="page-header">
    <div class="page-title"><h1>{{ "user.forgot.resetPWD"|translate}}</h1></div>
</div>
<!--End Page Header-->
<div class="container">
    <div class="row">
        <!--Main Content-->
        <div class="col-12 col-sm-12 col-md-12 col-lg-6 box offset-lg-3">
            <div class="mb-4 mt-lg-5">
                <form method="post" hx-post="/Account/OnFindPassword" id="findPwd-form"
                      hx-target="#findPwd-result"
                      hx-trigger="submit throttle:2s"
                      hx-swap="afterbegin"
                      hx-on::after-request="if(event.detail.successful){ findPwdSubmitCallback(event.detail.xhr.responseText) }"
                      accept-charset="UTF-8" class="contact-form">
                    <!--<h3>Retrieve your password here</h3>
                    <p>Please enter your email address below.</p>-->
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-12 col-lg-12 mb-3">
                            <div class="form-group">
                                <label for="CustomerEmail">{{ "user.account.email_addr"|translate}} <span class="required">*</span></label>

                                <input type="email" class="form-control" name="email" placeholder="{{ "web.footer.enter_email"|translate}}" id="CustomerEmail"
                                       autocapitalize="off" autofocus="" autocorrect="off"
                                       onkeyup="this.setCustomValidity(''); $(this).removeClass('is-invalid');"
                                       hx-on:htmx:validation:validate="if(this.value == '') {
                               this.setCustomValidity('Please enter Your Email');
                               $(this).addClass('is-invalid');
                           }else{$(this).removeClass('is-invalid')}" />
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="text-left col-12 col-sm-12 col-md-12 col-lg-12">
                            <input type="submit" class="btn mb-3 theme-btn" value="{{ "web.global.send"|translate}}"
                                   hx-swap="none"
                                   hx-disabled-elt="this"
                                   hx-indicator="#spinner">
                            <span id="spinner" class="htmx-indicator">
                                <i class="icon an an-spinner an-spinner-l"></i>
                            </span>

                            <p class="mb-4">
                                <a href="/account/signIn">« {{ "user.global.sign_in"|translate}}</a>
                            </p>
                        </div>
                    </div>
                </form>
                <div id="findPwd-result" class="mt-2"></div>
            </div>
        </div>
        <!--End Main Content-->
    </div>

</div><!--End Body Container-->
<script>
    function findPwdSubmitCallback(responseText) {
        //console.log(responseText);
        const notification = document.getElementById('findPwd-result');
        notification.classList.remove('hidden');
        const response = JSON.parse(responseText);

        notification.innerHTML = `
                <div class="alert alert-${response.status ? 'success' : 'danger'}">
                  ${response.msg}
                </div>`;
        if (response.status == true) {
            document.getElementById('findPwd-form').reset();
        }
        // 3秒后自动隐藏
        setTimeout(() => {
            notification.classList.add('hidden');
        }, 10000);
    }
</script>
