<div class="page-header">
    <div class="page-title"><h1>{{ "user.account.couponTitle"|translate}}</h1></div>
</div>
<div class="container">
    <div class="row mb-5">
		{% assign sidebar= '/Themes/'| append: theme | append:'/Account/Sidebar' %}
		{% include sidebar -%}
		{% assign filteredModel = Model | where: "ActiveStatus", "Already received" %}
		{% assign expiredModel = Model | where: "ActiveStatus", "Expired" %}

		<div class="col-xs-10 col-lg-10 col-md-12">
            <!-- Tab panes -->
            <div class="dashboard-content padding-30px-all md-padding-15px-all" style="">
				<h3>{{ "user.account.couponTitle"|translate}}</h3>
                <div class="section product-slider tab-slider-product no-pt-section">
                    <div class="tab-slider-ul mt-4 mb-4">
                        <ul class="tabs-style2 tabs nav nav-tabs head-font d-inline" id="productTabs" role="tablist">
                            <li class="nav-item" role="presentation">
								<a class="active" id="allPoints-tab" data-bs-toggle="tab" data-bs-target="#allPoints" role="tab" aria-controls="allPoints" aria-selected="true">{{ "user.points.all"|translate}}</a>
                            </li>
                            <li class="nav-item" role="presentation">
								<a id="earnedPoints-tab" data-bs-toggle="tab" data-bs-target="#earnedPoints" role="tab" aria-controls="earnedPoints" aria-selected="false">{{ "user.account.already_received"|translate}}</a>
                            </li>
                            <li class="nav-item" role="presentation">
								<a id="usedPoints-tab" data-bs-toggle="tab" data-bs-target="#usedPoints" role="tab" aria-controls="usedPoints" aria-selected="false">{{ "user.account.expired"|translate}}</a>
                            </li>
                        </ul>
                    </div>
                    <div class="tab-content" id="productTabsContent">
                        <div class="tab-pane show active grid-products" id="allPoints" role="tabpanel" aria-labelledby="allPoints-tab">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead class="alt-font">
                                        <tr>
											<th>{{ "checkout.checkout.coupon_code"|translate}}</th>
											<th>{{ "web.global.date"|translate}}</th>
											<th>{{ "web.global.usage_condition"|translate}}</th>
											<th>{{ "user.account.status"|translate}}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
										{% if Model == null or Model.size == 0 %}
										<div class="empty-date pt-100 pb-100">
										<img src="{{static_path}}/assets/img/Home/empty.png" alt=""/>
										</div>
										{% else %}
										{% for item in Model %}
										<tr>
                                            <td style="color:var( --theme-color);">{{ item.CouponNumber }}</td>
                                            <td>{{ item.Time }}</td>
                                            <td>{{item.Rules}}</td>
                                            <td>{{item.ActiveStatus}}</td>
                                        </tr>
										{% endfor %}
										{% endif %}
										
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane show grid-products" id="earnedPoints" role="tabpanel" aria-labelledby="earnedPoints-tab">
                            <div class="table-responsive">
								<table class="table">
									<thead class="alt-font">
										<tr>
											<th>{{ "checkout.checkout.coupon_code"|translate}}</th>
											<th>{{ "web.global.date"|translate}}</th>
											<th>{{ "web.global.usage_condition"|translate}}</th>
											<th>{{ "user.account.status"|translate}}</th>
										</tr>
									</thead>
									<tbody>
										{% if filteredModel == null or filteredModel.size == 0 %}
										<div class="empty-date pt-100 pb-100">
											<img src="{{static_path}}/assets/img/Home/empty.png" alt=""/>
										</div>
										{% else %}
										{% for item in filteredModel %}
										<tr>
											<td style="color:var( --theme-color);">{{ item.CouponNumber }}</td>
											<td>{{ item.Time }}</td>
											<td>{{item.Rules}}</td>
											<td>{{item.ActiveStatus}}</td>
										</tr>
										{% endfor %}
										{% endif %}

									</tbody>
								</table>
                            </div>
                        </div>
                        <div class="tab-pane show grid-products" id="usedPoints" role="tabpanel" aria-labelledby="usedPoints-tab">
                            <div class="table-responsive">
								<table class="table">
									<thead class="alt-font">
										<tr>
											<th>{{ "checkout.checkout.coupon_code"|translate}}</th>
											<th>{{ "web.global.date"|translate}}</th>
											<th>{{ "web.global.usage_condition"|translate}}</th>
											<th>{{ "user.account.status"|translate}}</th>
										</tr>
									</thead>
									<tbody>
										{% if expiredModel == null or expiredModel.size == 0 %}
										<div class="empty-date pt-100 pb-100">
											<img src="{{static_path}}/assets/img/Home/empty.png" alt=""/>
										</div>
										{% else %}
										{% for item in expiredModel %}
										<tr>
											<td style="color:var( --theme-color);">{{ item.CouponNumber }}</td>
											<td>{{ item.Time }}</td>
											<td>{{item.Rules}}</td>
											<td>{{item.ActiveStatus}}</td>
										</tr>
										{% endfor %}
										{% endif %}

									</tbody>
								</table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Tab panes -->
        </div>
    </div>
</div>