{% layout '' %}
<div id="response" hx-target="this" hx-swap="outerHTML">
    <div class="login-area py-100">
        <div class="container">
            <div class="col-md-5 mx-auto">
                <div class="login-form">
                    <div class="login-header">
                        <p>{{"user.register.varInfo_0"|translate}}</p>
                    </div>

                    <div id="response" hx-target="this" hx-swap="outerHTML">
                        <p>{{ "user.register.varInfo_1"| translate  }}  <a href="{{ViewData["MailServer"]}}">{{ViewData["UserEmail"]}}</a></p>
                    </div>

                    <div class="row">
                        <div class="text-left col-6 col-sm-6 col-md-6 col-lg-6 mt-3">
                            <a href="{{ViewData["MailServer"]}}" target="_blank" class="btn mb-3 theme-btn">{{"user.register.verifyNow"|translate}}</a>
                        </div>
                        <div class="text-right col-6 col-sm-6 col-md-6 col-lg-6 mt-3">
                            <a href="#" id="resendemail" hx-post="/Account/ResendEmail?email={{ViewData["UserEmail"]}}" hx-swap="none"
                               hx-on-htmx-before-request="disableLinkAndStartCountdown(this)" class="btn btn-outline-info mb-3 theme-btn"> {{"user.register.resendEmail"|translate}}</a>
                        </div>
                    </div>
                    <script>
        function disableLinkAndStartCountdown(link) {
            link.classList.add('disabled');
            link.style.pointerEvents = 'none'; // 禁用点击
            link.innerHTML = 'Please wait 60 seconds...';

            let countdown = 60; // 设置倒计时初始值

            const intervalId = setInterval(() => {
                countdown--;
                document.getElementById('resendemail').innerText = `Time remaining: ${countdown} seconds`;

                if (countdown <= 0) {
                    clearInterval(intervalId);
                    link.classList.remove('disabled');
                    link.style.pointerEvents = 'auto'; // 恢复点击
                    link.innerHTML = 'Resend Mail';
                    document.getElementById('resendemail').innerText = '{{"user.register.resendEmail"|translate}}';
                }
            }, 1000); // 每秒更新一次
        }
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
