using YseStore.Common;

namespace YseStore.Ext
{
    public static class RequestExt
    {


        public static string GetQueryString(this HttpRequest request, string key, bool removeHtmlTag = true)
        {
            return request.GetQueryString(key, "", removeHtmlTag);
        }

        public static string GetQueryString(this HttpRequest request, string key, string defaultValue, bool removeHtmlTag = true)
        {
            string value = request.Query[key];
            if (!string.IsNullOrWhiteSpace(value))
                if (removeHtmlTag)
                {
                    return value.RemoveHTMLTag();
                }
                else
                {
                    return value;
                }

            else
                return defaultValue;
        }


        /// <summary>
        /// 获得查询字符串中的值
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns></returns>
        public static int GetQueryInt(this HttpRequest request, string key, int defaultValue)
        {
            return StringHelper.StringToInt(request.GetQueryString(key), defaultValue);
        }

        /// <summary>
        /// 获得查询字符串中的值
        /// </summary>
        /// <param name="key">键</param>
        /// <returns></returns>
        public static int GetQueryInt(this HttpRequest request, string key)
        {
            return request.GetQueryInt(key, 0);
        }
        public static uint GetQueryUInt(this HttpRequest request, string key)
        {
            return StringHelper.StringToUInt(request.GetQueryString(key), 0);
        }


        public static decimal GetFormDecimal(this HttpRequest request, string key)
        {
            return StringHelper.StringToDecimal(request.GetFormString(key, "0"));
        }



        /// <summary>
        /// 获得表单中的值 ,并移除html
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns></returns>
        public static string GetFormString(this HttpRequest request, string key, string defaultValue, bool removeHtmlTag = true)
        {
            if (!request.Form.ContainsKey(key))
            {
                return defaultValue;
            }

            string value = request.Form[key];
            if (!string.IsNullOrWhiteSpace(value))
                if (removeHtmlTag)
                {
                    return value.Trim().RemoveHTMLTag();
                }
                else
                {
                    return value.Trim();
                }
            else
                return defaultValue;
        }

        /// <summary>
        /// 获得表单中的值
        /// </summary>
        /// <param name="key">键</param>
        /// <returns></returns>
        public static string GetFormString(this HttpRequest request, string key, bool removeHtmlTag = true)
        {
            return request.GetFormString(key, "", removeHtmlTag);
        }


        /// <summary>
        /// 获取ip
        /// </summary>
        /// <returns></returns>
        //public static string GetIP(this HttpRequest request)
        //{
        //    string ip = request.Headers["X-Real-IP"].FirstOrDefault();
        //    //Logger.Info($"X-Real-IP:{ip}");
        //    if (string.IsNullOrEmpty(ip))
        //    {
        //        ip = request.Headers["X-Forwarded-For"].FirstOrDefault();
        //    }
        //    //Logger.Info($"X-Forwarded-For:{ip}");
        //    if (string.IsNullOrEmpty(ip))
        //    {
        //        ip = "127.0.0.1";
        //    }
        //    return ip;
        //}

        /// <summary>
        /// 获取ip
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static string GetIP(this HttpRequest request)
        {
            string ip = request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ip))
            {
                ip = "127.0.0.1";
            }
            return ip;
        }



        public static string GetIPList(this HttpRequest request)
        {
            var ip = request.Headers["X-Real-IP"].ToJson();
            return ip;


        }
        /// <summary>
        /// 获取上个url
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static string GetUrlReferrer(this HttpRequest request)
        {

            try
            {
                string UrlReferrer = request.Headers["Referer"].ToString();

                return UrlReferrer;
            }
            catch (Exception ex)
            {

                return "";
            }


        }


        /// <summary>
        /// 是否是get请求
        /// </summary>
        /// <returns></returns>
        public static bool IsGet(this HttpRequest request)
        {
            return request.Method == "GET";
        }

        /// <summary>
        /// 是否是post请求
        /// </summary>
        /// <returns></returns>
        public static bool IsPost(this HttpRequest request)
        {
            return request.Method == "POST";
        }


        /// <summary>
        /// 获得表单中的值
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns></returns>
        public static int GetFormInt(this HttpRequest request, string key, int defaultValue)
        {
            var value = request.Form[key];
            return StringHelper.StringToInt(value, defaultValue);
        }

        /// <summary>
        /// 获得表单中的值
        /// </summary>
        /// <param name="key">键</param>
        /// <returns></returns>
        public static int GetFormInt(this HttpRequest request, string key)
        {
            return request.GetFormInt(key, 0);
        }


        public static byte GetFormByte(this HttpRequest request, string key, byte defaultValue = 0)
        {
            return StringHelper.StringToByte(request.Form[key], defaultValue);
        }
        public static bool GetFormBool(this HttpRequest request, string key, bool defaultValue = false)
        {
            return StringHelper.StringToBool(request.Form[key], defaultValue);
        }


        /// <summary>
        /// 获得请求客户端的操作系统类型
        /// </summary>
        /// <returns></returns>
        public static string GetOSType(this HttpRequest request)
        {
            string userAgent = request.Headers["User-Agent"];
            if (userAgent == null)
                return "未知";
            userAgent = userAgent.ToLower();
            string type = null;
            if (userAgent.Contains("NT 6.1"))
                type = "Windows 7";
            else if (userAgent.Contains("NT 5.1"))
                type = "Windows XP";
            else if (userAgent.Contains("NT 6.2"))
                type = "Windows 8";

            else if (userAgent.Contains("Mac"))
                type = "Mac";
            else if (userAgent.Contains("NT 6.0"))
                type = "Windows Vista";
            else if (userAgent.Contains("NT 5.2"))
                type = "Windows 2003";
            else if (userAgent.Contains("NT 5.0"))
                type = "Windows 2000";
            else if (userAgent.Contains("98"))
                type = "Windows 98";
            else if (userAgent.Contains("95"))
                type = "Windows 95";
            else if (userAgent.Contains("Me"))
                type = "Windows Me";
            else if (userAgent.Contains("NT 4"))
                type = "Windows NT4";
            else if (userAgent.Contains("Unix"))
                type = "UNIX";
            else if (userAgent.Contains("Linux"))
                type = "Linux";
            else if (userAgent.Contains("SunOS"))
                type = "SunOS";
            else if (userAgent.Contains("android"))
                type = "Android";
            else if (userAgent.Contains("iphone"))
                type = "IPhone";
            else
                type = "未知";

            return type;
        }



        /// <summary>
        /// 获取分页的返回路由
        /// </summary>
        /// <param name="Request"></param>
        /// <returns></returns>
        public static string GetPagerRouteUrl(this HttpRequest Request)
        {
            if (!string.IsNullOrEmpty(Request.QueryString.ToString()))
            {
                if (Request.QueryString.ToString().Contains("pageIndex="))
                {
                    return $"{Request.Path}{Request.QueryString}".Split("pageIndex=")[0] + "pageIndex=";
                }
                else
                {
                    return $"{Request.Path}{Request.QueryString}&pageIndex=";
                }

            }
            else
            {
                return $"{Request.Path}?pageIndex=";
            }
        }





    }
}
