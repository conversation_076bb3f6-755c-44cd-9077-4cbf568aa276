/**
 * 验证码服务
 * 封装验证码初始化、操作和UI交互
 */
import {verifyBlogCommentCaptcha, verifyProductReviewCaptcha} from './captcha-api.js';

// 验证码实例
let captchaInstance = null;

// 验证码配置
let captchaConfig = {};

/**
 * 设置验证码配置
 * @param {Object} config - 验证码配置对象
 * @returns {Object} - 当前模块，用于链式调用
 */
export function setConfig(config) {
    captchaConfig = {...config};

    // 确保全局配置存在
    if (window.AliyunCaptchaConfig === undefined) {
        window.AliyunCaptchaConfig = {
            region: captchaConfig.region || 'cn',
            prefix: captchaConfig.prefix || ''
        };
    }

    return {init, getInstance, show, verify, verifyProduct};
}

/**
 * 初始化验证码
 * @param {Object} options - 初始化选项
 * @returns {Promise<Object>} - 验证码实例
 */
export function init(options) {
    return new Promise((resolve) => {
        // 组合选项
        const defaultOptions = {
            // 验证码模式，popup表示弹出式
            mode: "popup",
            // 获取验证码实例的回调
            getInstance: (instance) => {
                console.log("验证码实例已创建");
                captchaInstance = instance;
                resolve(instance);
            },
            // 滑块验证码样式
            slideStyle: {
                width: 360,
                height: 40,
            },
            // 错误处理配置
            errorTips: {
                // 重试按钮文本
                maskButtonText: "重试",
                // 允许重试
                allowRetry: true,
                // 验证失败提示
                verifyFailed: "验证失败，请重试",
                // 网络错误提示
                networkError: "网络错误，请重试",
                // 点击重试按钮后关闭错误提示框
                closeMaskOnClick: true
            }
        };

        // 合并选项
        const initOptions = {...defaultOptions, ...options};

        // 初始化验证码
        if (typeof window.initAliyunCaptcha === 'function') {
            window.initAliyunCaptcha(initOptions);
            console.log("验证码初始化完成");
        } else {
            console.error("阿里云验证码SDK未加载");
            resolve(null);
        }
    });
}

/**
 * 获取验证码实例
 * @returns {Object|null} - 验证码实例
 */
export function getInstance() {
    return captchaInstance;
}

/**
 * 显示验证框
 */
export function show() {
    if (captchaInstance) {
        captchaInstance.verify();
        captchaInstance.verifyProduct();
    } else {
        console.error("验证码实例未初始化");
    }
}

/**
 * 验证验证码
 * @param {string} captchaVerifyParam - 验证码验证参数
 * @returns {Promise<Object>} - 验证结果
 */
export function verify(captchaVerifyParam) {
    return verifyBlogCommentCaptcha(captchaVerifyParam);
}

export function verifyProduct(captchaVerifyParam) {
    return verifyProductReviewCaptcha(captchaVerifyParam);
}


// 默认导出所有函数
export default {
    setConfig,
    init,
    getInstance,
    show,
    verify,
    verifyProduct
}; 