/**
 * 阿里云验证码核心模块
 * 提供验证码初始化、验证回调等基础功能
 * 可在任何需要验证码的页面中使用
 */

// 创建全局命名空间
window.YseCaptcha = window.YseCaptcha || {};

(function(YseCaptcha) {
    // 验证码实例
    let captchaInstance = null;
    
    // 验证通过标志
    let captchaVerified = false;
    
    // 验证成功后的回调函数
    let successCallback = null;
    
    // 初始化验证码
    YseCaptcha.init = function(options = {}) {
        // 确保验证码容器存在
        ensureCaptchaContainer();
        
        // 保存成功回调
        if (typeof options.onSuccess === 'function') {
            successCallback = options.onSuccess;
        }
        
        // 初始化验证码
        return initCaptcha(options);
    };
    
    // 手动触发验证
    YseCaptcha.verify = function() {
        if (captchaInstance) {
            triggerCaptchaVerify();
            return true;
        }
        return false;
    };
    
    // 检查验证状态
    YseCaptcha.isVerified = function() {
        return captchaVerified;
    };
    
    // 重置验证状态
    YseCaptcha.reset = function() {
        captchaVerified = false;
    };
    
    // 获取验证参数
    YseCaptcha.getVerifyParam = function() {
        const captchaParam = document.getElementById('captchaVerifyParam');
        return captchaParam ? captchaParam.value : '';
    };
    
    // 确保验证码容器存在
    function ensureCaptchaContainer() {
        // 检查验证码容器是否存在
        if (!document.getElementById('captcha-element')) {
            // 创建验证码容器
            const captchaContainer = document.createElement('div');
            captchaContainer.id = 'captcha-element';
            captchaContainer.style.display = 'none'; // 隐藏容器，因为我们使用弹出模式
            
            // 创建隐藏的验证参数输入框
            const captchaParam = document.createElement('input');
            captchaParam.id = 'captchaVerifyParam';
            captchaParam.type = 'hidden';
            captchaParam.name = 'captchaVerifyParam';
            captchaParam.value = '';
            
            // 将容器添加到body
            document.body.appendChild(captchaContainer);
            document.body.appendChild(captchaParam);
        }
    }
    
    // 初始化验证码
    async function initCaptcha(customOptions = {}) {
        try {
            // 检查验证码服务是否已初始化
            if (typeof window.initAliyunCaptcha !== 'function') {
                console.log('阿里云验证码SDK未加载，尝试动态加载');
                return null;
            }
            
            // 设置验证码配置
            if (window.AliyunCaptchaConfig === undefined) {
                window.AliyunCaptchaConfig = {
                    region: 'cn',
                    prefix: 'd8jqyi'
                };
            }
            
            // 检查已有实例
            if (window.captchaInstance) {
                captchaInstance = window.captchaInstance;
                return captchaInstance;
            }
            
            // 默认配置
            const defaultOptions = {
                SceneId: '16bqmuee', // 场景ID
                mode: 'popup',
                element: '#captcha-element',
                language: 'cn',
                captchaVerifyCallback: captchaVerifyCallback,
                onBizResultCallback: onBizResultCallback,
                getInstance: function(instance) {
                    captchaInstance = instance;
                    // 保存到全局，以便其他脚本可以访问
                    window.captchaInstance = instance;
                },
                slideStyle: {
                    width: 360,
                    height: 40,
                },
                errorTips: {
                    maskButtonText: '重试',
                    allowRetry: true,
                    verifyFailed: '验证失败，请重试',
                    networkError: '网络错误，请重试',
                    closeMaskOnClick: true
                }
            };
            
            // 合并自定义选项
            const captchaOptions = {...defaultOptions, ...customOptions};
            
            // 初始化验证码
            window.initAliyunCaptcha(captchaOptions);
            
            return new Promise(resolve => {
                // 5秒内检查实例是否创建成功
                let checkCount = 0;
                const checkInterval = setInterval(() => {
                    checkCount++;
                    if (captchaInstance) {
                        clearInterval(checkInterval);
                        resolve(captchaInstance);
                    } else if (checkCount >= 10) { // 5秒 (10 * 500ms)
                        clearInterval(checkInterval);
                        console.error('验证码初始化超时');
                        resolve(null);
                    }
                }, 500);
            });
        } catch (error) {
            console.error('初始化验证码时出错:', error);
            return null;
        }
    }
    
    // 验证码验证回调
    async function captchaVerifyCallback(captchaVerifyParam) {
        try {
            // 保存验证参数
            document.getElementById('captchaVerifyParam').value = captchaVerifyParam;
            
            // 验证验证码
            const response = await fetch('/api/form/verify-captcha', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ captchaVerifyParam }),
            });
            
            if (!response.ok) {
                return {
                    captchaResult: false,
                    bizResult: false,
                    message: '验证码验证失败，请重试'
                };
            }
            
            const result = await response.json();
            
            // 返回验证结果
            return {
                captchaResult: Boolean(result.success),
                bizResult: Boolean(result.success),
                message: result.success ? undefined : (result.message || '验证失败，请重试')
            };
        } catch (error) {
            return {
                captchaResult: false,
                bizResult: false,
                message: '验证过程中发生错误: ' + error.message
            };
        }
    }
    
    // 业务结果回调
    function onBizResultCallback(bizResult) {
        if (bizResult === true) {
            // 标记验证已通过
            captchaVerified = true;
            
            // 如果有成功回调，执行它
            if (typeof successCallback === 'function') {
                setTimeout(() => {
                    successCallback();
                }, 100);
            }
        }
    }
    
    // 触发验证码验证
    function triggerCaptchaVerify() {
        try {
            // 使用安全的方法调用
            if (typeof captchaInstance.verify === 'function') {
                captchaInstance.verify();
            } else if (typeof captchaInstance.validate === 'function') {
                captchaInstance.validate();
            } else if (typeof captchaInstance.show === 'function') {
                captchaInstance.show();
            } else {
                console.error('验证码实例没有可用的验证方法');
                alert('验证码服务异常，请刷新页面重试');
            }
        } catch (error) {
            console.error('触发验证码验证时出错:', error);
        }
    }
})(window.YseCaptcha); 