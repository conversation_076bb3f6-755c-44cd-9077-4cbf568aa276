/**
 * 价格处理工具类
 * 提供统一的价格提取和格式化功能，支持多种货币格式
 */
(function() {
    'use strict';

    /**
     * HTML实体解码函数
     * @param {string} text - 需要解码的文本
     * @returns {string} 解码后的文本
     */
    function decodeHtmlEntities(text) {
        if (!text) return text;
        const textarea = document.createElement('textarea');
        textarea.innerHTML = text;
        return textarea.value;
    }

    /**
     * 从格式化价格字符串中提取数值
     * 支持多种货币格式：
     * - 美式格式：$1,234.56
     * - 欧洲格式：€1.234,56
     * - 简单格式：¥1234.56
     * @param {string} priceString - 格式化的价格字符串
     * @returns {number} 提取出的数值
     */
    function extractPriceValue(priceString) {
        if (!priceString) return 0;
        
        // 解码HTML实体
        const decoded = decodeHtmlEntities(priceString);
        
        // 移除货币符号和其他非数字字符，只保留数字、小数点和逗号
        const numericString = decoded.replace(/[^\d.,]/g, '');
        
        // 如果没有数字，返回0
        if (!numericString) return 0;
        
        // 智能处理小数分隔符和千位分隔符
        let cleanString = numericString;
        
        // 检查是否包含逗号和点
        const hasComma = numericString.includes(',');
        const hasDot = numericString.includes('.');
        
        if (hasComma && hasDot) {
            // 同时包含逗号和点：需要判断哪个是小数分隔符
            const lastCommaIndex = numericString.lastIndexOf(',');
            const lastDotIndex = numericString.lastIndexOf('.');
            
            if (lastCommaIndex > lastDotIndex) {
                // 逗号在点后面：点是千位分隔符，逗号是小数分隔符
                // 例如：1.420,49 -> 1420.49
                cleanString = numericString.replace(/\./g, '').replace(',', '.');
            } else {
                // 点在逗号后面：逗号是千位分隔符，点是小数分隔符
                // 例如：1,420.49 -> 1420.49
                cleanString = numericString.replace(/,/g, '');
            }
        } else if (hasComma && !hasDot) {
            // 只有逗号：检查是否是小数分隔符
            const commaMatch = numericString.match(/,(\d{1,2})$/);
            if (commaMatch && numericString.indexOf(',') === numericString.lastIndexOf(',')) {
                // 只有一个逗号且在最后1-2位数字前：逗号是小数分隔符
                // 例如：615,99 -> 615.99
                cleanString = numericString.replace(',', '.');
            } else {
                // 多个逗号或不在末尾：逗号是千位分隔符
                // 例如：1,420 -> 1420
                cleanString = numericString.replace(/,/g, '');
            }
        } else if (hasDot && !hasComma) {
            // 只有点：检查是否是小数分隔符
            const dotMatch = numericString.match(/\.(\d{1,2})$/);
            if (dotMatch && numericString.indexOf('.') === numericString.lastIndexOf('.')) {
                // 只有一个点且在最后1-2位数字前：点是小数分隔符
                // 例如：615.99 -> 615.99
                cleanString = numericString;
            } else {
                // 多个点或不在末尾：点是千位分隔符
                // 例如：1.420 -> 1420
                cleanString = numericString.replace(/\./g, '');
            }
        }
        
        // 转换为数字
        const result = parseFloat(cleanString) || 0;
        
        // 调试信息（仅在开发环境输出）
        if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
            if (priceString && priceString !== "0") {
                console.log(`Price extraction: "${priceString}" -> "${decoded}" -> "${numericString}" -> "${cleanString}" -> ${result}`);
            }
        }
        
        return result;
    }

    /**
     * 从格式化价格字符串中提取货币符号
     * @param {string} priceString - 格式化的价格字符串
     * @returns {string} 提取出的货币符号
     */
    function extractCurrencySymbol(priceString) {
        if (!priceString) return '$'; // 默认返回美元符号
        // 解码HTML实体
        const decoded = decodeHtmlEntities(priceString);
        // 提取非数字、非小数点、非逗号、非空格的字符作为货币符号
        const currencyMatch = decoded.match(/[^\d.,\s]/);
        return currencyMatch ? currencyMatch[0] : '$';
    }

    /**
     * 格式化价格显示
     * @param {number} price - 价格数值
     * @param {string} currencySymbol - 货币符号
     * @param {number} decimals - 小数位数，默认2位
     * @returns {string} 格式化后的价格字符串
     */
    function formatPrice(price, currencySymbol = '$', decimals = 2) {
        if (typeof price !== 'number' || isNaN(price)) {
            return currencySymbol + '0.00';
        }
        return currencySymbol + price.toFixed(decimals);
    }

    /**
     * 计算折扣百分比
     * @param {number} originalPrice - 原价
     * @param {number} salePrice - 售价
     * @returns {number} 折扣百分比（整数）
     */
    function calculateDiscountPercent(originalPrice, salePrice) {
        if (!originalPrice || originalPrice <= 0 || !salePrice || salePrice <= 0) {
            return 0;
        }
        if (salePrice >= originalPrice) {
            return 0;
        }
        const discount = (originalPrice - salePrice) / originalPrice * 100;
        return Math.round(discount);
    }

    /**
     * 计算节省金额
     * @param {number} originalPrice - 原价
     * @param {number} salePrice - 售价
     * @returns {number} 节省金额
     */
    function calculateSavedAmount(originalPrice, salePrice) {
        if (!originalPrice || originalPrice <= 0 || !salePrice || salePrice <= 0) {
            return 0;
        }
        if (salePrice >= originalPrice) {
            return 0;
        }
        return originalPrice - salePrice;
    }

    /**
     * 检测价格字符串的格式类型
     * @param {string} priceString - 格式化的价格字符串
     * @returns {object} 格式信息对象
     */
    function detectPriceFormat(priceString) {
        if (!priceString) return { decimalSeparator: '.', thousandSeparator: ',' };

        const decoded = decodeHtmlEntities(priceString);
        const numericString = decoded.replace(/[^\d.,]/g, '');

        const hasComma = numericString.includes(',');
        const hasDot = numericString.includes('.');

        if (hasComma && hasDot) {
            const lastCommaIndex = numericString.lastIndexOf(',');
            const lastDotIndex = numericString.lastIndexOf('.');

            if (lastCommaIndex > lastDotIndex) {
                // 欧洲格式：1.420,49
                return { decimalSeparator: ',', thousandSeparator: '.' };
            } else {
                // 美式格式：1,420.49
                return { decimalSeparator: '.', thousandSeparator: ',' };
            }
        } else if (hasComma && !hasDot) {
            // 检查逗号是否是小数分隔符
            const commaMatch = numericString.match(/,(\d{1,2})$/);
            if (commaMatch && numericString.indexOf(',') === numericString.lastIndexOf(',')) {
                // 欧洲格式：615,99
                return { decimalSeparator: ',', thousandSeparator: '.' };
            } else {
                // 美式格式：1,420
                return { decimalSeparator: '.', thousandSeparator: ',' };
            }
        } else {
            // 默认美式格式
            return { decimalSeparator: '.', thousandSeparator: ',' };
        }
    }

    /**
     * 根据参考价格格式来格式化金额
     * @param {number} amount - 要格式化的金额
     * @param {string} referencePriceString - 参考价格字符串（用于确定格式）
     * @param {string} currencySymbol - 货币符号
     * @param {number} decimals - 小数位数，默认2位
     * @returns {string} 格式化后的价格字符串
     */
    function formatAmountLikeReference(amount, referencePriceString, currencySymbol = '', decimals = 2) {
        if (typeof amount !== 'number' || isNaN(amount)) {
            return currencySymbol + '0' + (decimals > 0 ? '.00' : '');
        }

        const format = detectPriceFormat(referencePriceString);

        // 格式化数字
        let formattedNumber = amount.toFixed(decimals);

        // 如果使用逗号作为小数分隔符，替换点号
        if (format.decimalSeparator === ',') {
            formattedNumber = formattedNumber.replace('.', ',');
        }

        return currencySymbol + formattedNumber;
    }

    // 将工具函数暴露到全局作用域
    window.PriceUtils = {
        decodeHtmlEntities: decodeHtmlEntities,
        extractPriceValue: extractPriceValue,
        extractCurrencySymbol: extractCurrencySymbol,
        formatPrice: formatPrice,
        calculateDiscountPercent: calculateDiscountPercent,
        calculateSavedAmount: calculateSavedAmount,
        detectPriceFormat: detectPriceFormat,
        formatAmountLikeReference: formatAmountLikeReference
    };

    // 为了向后兼容，也将主要函数直接暴露到全局
    window.extractPriceValue = extractPriceValue;
    window.extractCurrencySymbol = extractCurrencySymbol;
    window.decodeHtmlEntities = decodeHtmlEntities;

})();
