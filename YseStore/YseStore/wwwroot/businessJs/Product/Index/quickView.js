/**
 * 产品快速预览功能
 * 用于处理产品列表页面的快速预览功能
 */
(function() {
    // 快速预览功能类
    class ProductQuickView {
        constructor() {
            this.modal = document.getElementById('quickview');
            this.modalBody = this.modal ? this.modal.querySelector('.modal-body') : null;
            this.init();
        }

        /**
         * 初始化快速预览功能
         */
        init() {
            // 确保DOM已加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.bindEvents());
            } else {
                this.bindEvents();
            }
            
            // 添加事件委托，处理动态加载的内容
            document.addEventListener('click', (e) => {
                const button = e.target.closest('[data-bs-target="#quickview"]');
                if (button) {
                    this.handleQuickViewClick(e, button);
                }
            });
        }

        /**
         * 绑定事件处理
         */
        bindEvents() {
            // 查找所有快速预览按钮
            const quickViewButtons = document.querySelectorAll('[data-bs-target="#quickview"]');
            
            // 为每个按钮添加点击事件
            quickViewButtons.forEach(button => {
                button.addEventListener('click', (e) => this.handleQuickViewClick(e, button));
            });
        }

        /**
         * 处理快速预览按钮点击事件
         * @param {Event} e - 事件对象
         * @param {HTMLElement} button - 点击的按钮元素
         */
        handleQuickViewClick(e, button) {
            e.preventDefault();
            
            // 获取产品ID
            const productId = button.getAttribute('data-product-id');
            
            if (!productId) {
                return;
            }
            
            // 显示加载状态
            this.showLoading();
            
            // 发送AJAX请求获取产品数据
            this.fetchProductData(productId);
        }

        /**
         * 显示加载状态
         */
        showLoading() {
            if (!this.modalBody) return;
            
            this.modalBody.innerHTML = '<div class="text-center py-5"><i class="fas fa-spinner fa-spin fa-3x"></i><p class="mt-3">Loading product information...</p></div>';
        }

        /**
         * 获取产品数据
         * @param {string|number} productId - 产品ID
         */
        fetchProductData(productId) {
            const url = `/shop/QuickView?productId=${productId}`;
            
            fetch(url)
                .then(response => response.json())
                .then(result => this.handleProductData(result))
                .catch(error => this.handleError(error));
        }

        /**
         * 处理获取到的产品数据
         * @param {Object} result - 从服务器返回的产品数据
         */
        handleProductData(result) {
            if (!this.modalBody) return;
            
            if (result.success && result.data) {
                const product = result.data;
                this.updateModalContent(product);
            } else {
                this.showError('Failed to load product information. Please try again later.');
            }
        }

        /**
         * 处理错误情况
         * @param {Error} error - 错误对象
         */
        handleError(error) {
            this.showError('An error occurred. Please try again later.');
        }

        /**
         * 显示错误信息
         * @param {string} message - 错误消息
         */
        showError(message) {
            if (!this.modalBody) return;
            
            this.modalBody.innerHTML = `<div class="text-center py-5"><i class="fas fa-exclamation-circle fa-3x text-danger"></i><p class="mt-3">${message}</p></div>`;
        }

        /**
         * 生成评分星星HTML
         * @param {number} rating - 产品评分
         * @returns {string} 星星HTML
         */
        generateStarsHtml(rating) {
            let starsHtml = '';
            const fullStars = Math.floor(rating || 0);
            const halfStar = (rating || 0) - fullStars;
            
            for (let i = 1; i <= 5; i++) {
                if (i <= fullStars) {
                    starsHtml += '<i class="fas fa-star"></i>';
                } else if (halfStar >= 0.5 && i === fullStars + 1) {
                    starsHtml += '<i class="fas fa-star-half-alt"></i>';
                } else {
                    starsHtml += '<i class="far fa-star"></i>';
                }
            }
            
            return starsHtml;
        }

        /**
         * 生成价格HTML
         * @param {Object} product - 产品对象，包含格式化价格字段
         * @returns {string} 价格HTML
         */
        generatePriceHtml(product) {
            if (product.originalPrice && product.originalPrice > product.price) {
                return `<h5><del>${product.originalPriceFormat || '$' + product.originalPrice.toFixed(2)}</del><span>${product.priceFormat || '$' + product.price.toFixed(2)}</span></h5>`;
            } else {
                return `<h5><span>${product.priceFormat || '$' + product.price.toFixed(2)}</span></h5>`;
            }
        }

        /**
         * 更新模态框内容
         * @param {Object} product - 产品数据
         */
        updateModalContent(product) {
            if (!this.modalBody) return;
            
            // 生成评分星星HTML
            const starsHtml = this.generateStarsHtml(product.rating);

            // 生成价格HTML
            const priceHtml = this.generatePriceHtml(product);
            
            // 构建库存状态
            const stockStatus = product.isInStock ? 
                '<span class="stock">Available</span>' : 
                '<span class="stock out">Out of Stock</span>';
            
            // 获取静态资源路径
            const staticPath = window.staticPath || '';
            // 更新模态框内容
            this.modalBody.innerHTML = `
                <div class="row">
                    <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
                        <div class="quickview-img">
                            <img src="${product.picPath ? product.picPath : staticPath + '/assets/img/product/01.png'}" alt="${product.ProductName}">
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
                        <div class="quickview-content">
                            <h4 class="quickview-title">${product.productName}</h4>
                            <div class="quickview-rating">
                                ${starsHtml}
                                <span class="rating-count"> (${product.reviewCount || 0} Customer Reviews)</span>
                            </div>
                            <div class="quickview-price">
                                ${priceHtml}
                            </div>
                            <p>${product.briefDescription || ''}</p>
                            <ul class="quickview-list">
                                <li>Brand:<span>${product.brandName || 'Medica'}</span></li>
                                <li>Category:<span>${product.categoryName || 'Healthcare'}</span></li>
                                <li>Stock:${stockStatus}</li>
                                <li>Code:<span>${product.sku || ''}</span></li>
                            </ul>
                            <div class="quickview-cart">
                                <a href="#" class="theme-btn" ${!product.isInStock ? 'disabled' : ''}>Add to cart</a>
                            </div>
                            <div class="quickview-social">
                                <span>Share:</span>
                                <a href="#"><i class="fab fa-facebook-f"></i></a>
                                <a href="#"><i class="fab fa-x-twitter"></i></a>
                                <a href="#"><i class="fab fa-pinterest-p"></i></a>
                                <a href="#"><i class="fab fa-instagram"></i></a>
                                <a href="#"><i class="fab fa-linkedin-in"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // 创建全局变量，以便可以从外部访问
    window.ProductQuickView = ProductQuickView;
    
    // 自动初始化
    document.addEventListener('DOMContentLoaded', () => {
        new ProductQuickView();
    });
})(); 