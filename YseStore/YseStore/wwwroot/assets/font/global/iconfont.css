@font-face {
  font-family: "iconfont"; /* Project id 4911096 */
  src: url('iconfont.woff2?t=1751530623500') format('woff2'),
       url('iconfont.woff?t=1751530623500') format('woff'),
       url('iconfont.ttf?t=1751530623500') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon_menu_notification:before {
  content: "\e697";
}

.icon_menu_minus:before {
  content: "\e698";
}

.icon_menu_task:before {
  content: "\e6a0";
}

.icon_menu_add-bold:before {
  content: "\e6a6";
}

.icon_menu_ArrowUp:before {
  content: "\e6ee";
}

.icon_menu_eyes:before {
  content: "\e681";
}

.icon_menu_ArrowDown:before {
  content: "\e6ed";
}

.icon_menu_viewgrid:before {
  content: "\eb60";
}

.icon_menu_viewlist:before {
  content: "\e66d";
}

.icon_menu_csv:before {
  content: "\e611";
}

.icon_menu_close:before {
  content: "\e6e9";
}

.icon_menu_listselect:before {
  content: "\e664";
}

.icon_menu_file:before {
  content: "\e639";
}

.icon_menu_exe:before {
  content: "\e63a";
}

.icon_menu_jpg:before {
  content: "\e63b";
}

.icon_menu_pdf:before {
  content: "\e63c";
}

.icon_menu_txt:before {
  content: "\e63d";
}

.icon_menu_zip:before {
  content: "\e63e";
}

.icon_menu_xml:before {
  content: "\e63f";
}

.icon_menu_doc:before {
  content: "\e640";
}

.icon_menu_html:before {
  content: "\e641";
}

.icon_menu_js:before {
  content: "\e642";
}

.icon_menu_mp4:before {
  content: "\e643";
}

.icon_menu_json:before {
  content: "\e644";
}

.icon_menu_ppt:before {
  content: "\e645";
}

.icon_menu_xls:before {
  content: "\e646";
}

.icon_menu_css:before {
  content: "\e647";
}

.icon_menu_mp3:before {
  content: "\e648";
}

.icon_menu_png:before {
  content: "\e649";
}

.icon_menu_svg:before {
  content: "\e64a";
}

.icon_menu_download:before {
  content: "\e60f";
}

.icon_menu_contact:before {
  content: "\e610";
}

.icon_menu_tag:before {
  content: "\e61b";
}

.icon_menu_right:before {
  content: "\e616";
}

.icon_menu_uparrow:before {
  content: "\e76e";
}

.icon_menu_downarrow:before {
  content: "\e772";
}

.icon_menu_rightarrow:before {
  content: "\e775";
}

.icon_menu_leftarrow:before {
  content: "\e779";
}

.icon_menu_list:before {
  content: "\e78e";
}

.icon_menu_logistic:before {
  content: "\e7ae";
}

.icon_menu_pause:before {
  content: "\e613";
}

.icon_menu_active:before {
  content: "\e628";
}

.icon_menu_linechart:before {
  content: "\e655";
}

.icon_menu_cancelstick:before {
  content: "\e660";
}

.icon_menu_stick:before {
  content: "\e663";
}

.icon_menu_delete:before {
  content: "\e60b";
}

.icon_menu_triangledown:before {
  content: "\e662";
}

.icon_menu_triangleleft:before {
  content: "\e626";
}

.icon_menu_email:before {
  content: "\e60a";
}

.icon_menu_print:before {
  content: "\ea28";
}

.icon_menu_image:before {
  content: "\e72a";
}

.icon_menu_video:before {
  content: "\e6b4";
}

.icon_menu_listing:before {
  content: "\e6d6";
}

.icon_menu_takedown:before {
  content: "\e6e0";
}

.icon_menu_hsort:before {
  content: "\e71e";
}

.icon_menu_tree:before {
  content: "\e669";
}

.icon_menu_sortdown:before {
  content: "\e7c6";
}

.icon_menu_sortup:before {
  content: "\e7c7";
}

.icon_menu_updown:before {
  content: "\e770";
}

.icon_menu_eye:before {
  content: "\e609";
}

.icon_menu_hide:before {
  content: "\e625";
}

.icon_menu_edit:before {
  content: "\e6d3";
}

.icon_menu_copy:before {
  content: "\e618";
}

.icon_menu_search:before {
  content: "\e600";
}

.icon_menu_send:before {
  content: "\e6a3";
}

.icon_menu_more:before {
  content: "\e73a";
}

.icon_menu_detail:before {
  content: "\e619";
}

.icon_menu_view:before {
  content: "\e735";
}

.icon_menu_user:before {
  content: "\e73d";
}

.icon_menu_mta:before {
  content: "\e7ce";
}

.icon_menu_products:before {
  content: "\e608";
}

.icon_menu_interact:before {
  content: "\e64f";
}

.icon_menu_plugins:before {
  content: "\e688";
}

.icon_menu_set:before {
  content: "\e6a5";
}

.icon_menu_sales:before {
  content: "\e605";
}

.icon_menu_orders:before {
  content: "\e60e";
}

.icon_menu_index:before {
  content: "\e607";
}

