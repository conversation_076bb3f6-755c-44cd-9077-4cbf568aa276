/*********************************************************************************
    Template Name: <PERSON><PERSON><PERSON> Theme
    Description: Developed by Retevis Company, dedicated theme.
    Version: 1.0
**********************************************************************************/


/* --------------------------------------------------
Table of Content:

1. Fonts
2. Basic Styles
3. Typography
4. Container
5. Promotional Top Popup
6. Pre Loader
7. Headers Style
8. Homepage Demo's
9. Homepage Sections
   9.1 Homepage Slideshow
   9.2 Products With Tab Slider
   9.3 Hero/Parallax Banners
   9.4 Featured Content Section
   9.5 Newletter Section
   9.6 Testimonial Slider
   9.7 Info/Simple Text Section
   9.8 Instagram Section
   9.9 Miniproduct List Section
   9.10 Collection Slider
   9.11 Brands Logo Slider
   9.12 Home Blog Post
   9.13 Store Features
   9.14 Custom Content
   9.15 Newletter Section
   9.16 Promotion Product Popup
10. Collection Banner
11. Breadcrumbs
12. Section
13. Product Grid
14. Product Listview
15. Products Detail Page
16. Sidebar
17. Shop Pages
18. CMS Page
19. Blog Pages
20. Cart Pages
21. Checkout Page Styles
22. Nesletter Popup Styles
23. Footer
24. Cookie Popup
=======================================================================*/


/*======================================================================
  1. Fonts
========================================================================*/

@font-face {
    font-family: "Retevis-Light";
    src: url("../fonts/HarmonyOS_Sans_Light.eot"); /* IE9 Compat Modes */
    src: url("../fonts/HarmonyOS_Sans_Light.eot?#iefix") format("embedded-opentype"), /* IE6-IE8 */
    url("../fonts/HarmonyOS_Sans_Light.woff2") format("woff2"), /* Super Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Light.woff") format("woff"), /* Pretty Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Light.ttf")  format("truetype"), /* Safari, Android, iOS */
    url("../HarmonyOS_Sans_Light.svg#NotoSans") format("svg"); /* Legacy iOS */
    font-stretch: normal;
    font-style: normal;
    font-weight: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
}
@font-face {
    font-family: "Retevis-Bold";
    src: url("../fonts/HarmonyOS_Sans_Bold.eot"); /* IE9 Compat Modes */
    src: url("../fonts/HarmonyOS_Sans_Bold.eot?#iefix") format("embedded-opentype"), /* IE6-IE8 */
    url("../fonts/HarmonyOS_Sans_Bold.woff2") format("woff2"), /* Super Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Bold.woff") format("woff"), /* Pretty Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Bold.ttf")  format("truetype"), /* Safari, Android, iOS */
    url("../fonts/HarmonyOS_Sans_Bold.svg#NotoSans") format("svg"); /* Legacy iOS */
    font-stretch: normal;
    font-style: normal;
    font-weight: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
}
@font-face {
    font-family: "Retevis-Regular";
    src: url("../fonts/HarmonyOS_Sans_Regular.eot"); /* IE9 Compat Modes */
    src: url("../fonts/HarmonyOS_Sans_Regular.eot?#iefix") format("embedded-opentype"), /* IE6-IE8 */
    url("../fonts/HarmonyOS_Sans_Regular.woff2") format("woff2"), /* Super Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Regular.woff") format("woff"), /* Pretty Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Regular.ttf")  format("truetype"), /* Safari, Android, iOS */
    url("../fonts/HarmonyOS_Sans_Regular.svg#NotoSans") format("svg"); /* Legacy iOS */
    font-stretch: normal;
    font-style: normal;
    font-weight: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
}


/*======================================================================
  2. Basic Styles
========================================================================*/
* { -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; }
*, ::before, ::after { box-sizing:border-box; -webkit-box-sizing:border-box; }
html { overflow:hidden; overflow-y:auto; }
body { font-family:'Retevis-Regular',Helvetica,Arial,Tahoma,sans-serif; color:#666; font-weight:300; font-size:15px; letter-spacing:0.02em; line-height:1.7; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; margin:0; padding:0; }
article, aside, details, figcaption, figure, footer, header, nav, section, summary { display: block; }
audio, canvas, video { display: inline-block; }
audio:not([controls]) { display:none; height:0; }
video { width:100%; height:100%; object-fit:cover; }
[hidden] { display:none; }
html, button, input, select, textarea { font-family:'Retevis-Regular',Helvetica,Arial,Tahoma,sans-serif; }
input, textarea, .form-control { padding:10px 18px; }
iframe { border:0; width:100%; }

a { color:#2d68a8; text-decoration:none; outline:none; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
a, a:after, a:before { -ms-transition:all .3s ease-in-out; -webkit-transition:all .3s ease-in-out; transition:all .3s ease-in-out;  }
a:hover, a:active { color:#3367d6; text-decoration:none; outline:0; }
a:hover, a:focus { outline:0; }

p { margin:0 0 15px; }
p:last-child { margin-bottom:0; }
pre { background: #f2f2f2; color: #666; font-family: monospace; font-size: 14px; margin: 20px 0; overflow: auto; padding: 20px; white-space: pre; white-space: pre-wrap; word-wrap: break-word; }
blockquote, q { -webkit-hyphens: none; -moz-hyphens: none; -ms-hyphens: none; hyphens: none; quotes: none; }
blockquote:before, blockquote:after, q:before, q:after { content: ""; content: none; }
blockquote { font-size: 18px; font-style: italic; font-weight: 300; margin: 24px 40px; }
blockquote blockquote { margin-right: 0; }
blockquote cite, blockquote small { font-size: 14px; font-weight: normal; text-transform: uppercase; }
blockquote em, blockquote i { font-style: normal; font-weight: 300; }
blockquote strong, blockquote b { font-weight: 400; }
img { height:auto; max-width:100%; border:0; vertical-align:middle; -ms-interpolation-mode:bicubic; }
svg:not(:root) { overflow:hidden; }
ol, ul { padding:0; margin:0; }
ul { list-style:inside; }
ul.list-items { margin-left: 0; }
.list-items { margin-left:15px; }
.hide { display:none !important; }

/*
.opacity-100 { opacity: 1 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-50 { opacity: 0.50 !important; }
.opacity-25 { opacity: 0.25 !important; }
 */

/*======================================================================
  3. Typography
========================================================================*/
h1 a, .h1 a, h2 a, .h2 a, h3 a, .h3 a, h4 a, .h4 a, h5 a, .h5 a, h6 a, .h6 a { color:inherit; text-decoration:none; font-weight:inherit; }
h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6, address, p, pre, blockquote, dl, dd, menu, ol, ul, table, caption, hr { margin:0; margin-bottom:15px; }
h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 { color:#333; margin:0 0 10px; font-family:'Retevis-Bold',Helvetica,Arial,Tahoma,sans-serif; font-weight:600; line-height:1.2; letter-spacing:.02em; overflow-wrap: break-word; word-wrap: break-word; }

h1, .h1 { font-size:24px; }
h2, .h2 { font-size:19px; }
h3, .h3 { font-size:18px; }
h4, .h4 { font-size:16px; }
h5, .h5 { font-size:15px; }
h6, .h6 { font-size:14px; }


/*
.an-1x { font-size:13px !important; }
.an-2x { font-size:16px !important; }
.an-3x { font-size:18px !important; }
 */

.ff-light {font-family:'Retevis-Light',Helvetica,Arial,Tahoma,sans-serif; font-weight:300;}
.ff-bold {font-family:'Retevis-Bold',Helvetica,Arial,Tahoma,sans-serif;}

input, button, select, textarea, .form-control { font-size:13px; background:#fff; border:1px solid #d7d7d7; -webkit-transition:all 0.4s ease-out 0s; transition:all 0.4s ease-out 0s; color:#424242; }
input:focus, input:active, button:focus, button:active, select:focus, select:active, textarea:focus, textarea:active, .form-control:focus { outline:none; box-shadow:none; border-color:#757575; }
input, select, textarea, .form-control { width: 100%; font-size: 13px; letter-spacing:0.02em; -webkit-box-shadow:none; box-shadow:none; border-radius:6px; }
input, select, .form-control { height:42px; padding:0 15px; }
input[type="checkbox"], input[type="radio"] { width:auto; height:auto; }
input[type="checkbox"]:focus, input[type="radio"]:focus { outline:0; box-shadow:none; }
textarea, textarea.form-control { overflow:auto; resize:vertical; height:auto; padding:6px 10px; }
select { -webkit-appearance:none; -moz-appearance:none; appearance:none; background-position:right center; background-image:url(../images/arrow-select.png) !important; background-repeat:no-repeat !important; background-position:right 10px center !important; line-height:1.2; text-indent:0.01px; text-overflow: ''; cursor:pointer; padding:8px 20px 8px 10px; }

/*
.text-left { text-align:left !important; }
.text-right { text-align:right !important; }
.text-transform-none { text-transform: none !important; }
.bg-black { background-color: #000 !important; }
.text-black { color: #000 !important; }
.border-black { border-color: #000 !important; }

 */

.clr-none { color: inherit !important; }

.ls-normal { letter-spacing:normal !important; }

/*
hr { margin:20px 0; border-bottom:1px solid #e8e9eb; }

 */

.hr-text { display:flex; align-items:center; margin:2rem 0; font-size:.625rem; font-weight:600; text-transform:uppercase; letter-spacing:.04em; line-height:1.6; color:#656d77; height:1px; }
.hr-text:after,
.hr-text:before { flex:1 1 auto; height:1px; background-color:currentColor; opacity:.16; }
.hr-text:before { content:""; margin-right:.5rem; }
.hr-text:after { content:""; margin-left:.5rem; }

.tooltip > .tooltip-inner { font-family:"Retevis-Regular",Helvetica,Tahoma,Arial,serif; font-size:10px; line-height:14px; letter-spacing:0.4px; font-weight:400; padding-left:8px; padding-right:8px; text-shadow:none; height:auto; border-radius:3px; }
.border-bottom { border-bottom:1px solid #e8e9eb !important; }

.hidden { display: none; }
.visuallyhidden { border: 0; clip: rect(0 0 0 0); height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px; }
.poss_relative { position: relative; }
.poss_absolute { position: absolute; }
.visuallyhidden.focusable:active, .visuallyhidden.focusable:focus { clip: auto; height: auto; margin: 0; overflow: visible; position: static; width: auto; }
.invisible { visibility: hidden; }
.clear { clear:both; }
.clearfix:before, .clearfix:after { content:""; display:table; }
.clearfix:after { clear: both; }

/* Table */
/*
table { margin-bottom:15px; width:100%; border-collapse:collapse; border-spacing:0; }
th { font-family:'Retevis-Bold',Helvetica,Arial,Tahoma,sans-serif; font-weight:600; }

 */

/* Text specialized */
/*
.text-italic { font-style: italic; }
.text-normal { font-style: normal; }
.text-underline { text-decoration: underline; }

 */

/* Font specialized */
/*
.body-font { font-family:'Retevis-Regular',Helvetica,Arial,Tahoma,sans-serif !important; }
.heading-font { font-family:'Retevis-Regular',Helvetica,Arial,Tahoma,sans-serif !important; }

.fw-600 { font-weight:600 !important; }
.fw-500 { font-weight:500 !important; }

 */

.list--inline { padding:0; margin:0; }
.list--inline li { display:inline-block; margin-bottom:0; vertical-align:middle; }
.display-table { display:table; table-layout:fixed; width:100%; margin:0 !important; }
.display-table-cell { float:none; display:table-cell; vertical-align:middle; }

.btn, .btn-primary {
    -moz-user-select:none; -ms-user-select:none; -webkit-user-select:none; user-select:none; -webkit-appearance:none; -moz-appearance:none; appearance:none; display:inline-flex;align-items:center;justify-content:center; width:auto; height:auto;
    text-decoration:none; text-align:center; vertical-align:middle; cursor:pointer; border:1px solid transparent; border-radius:0; padding:8px 20px 8px; color:#222; font-family:'Retevis-Regular', sans-serif;
    font-weight:400; letter-spacing:1px; line-height:normal; white-space:normal; font-size:13px; -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out;
}
.btn { color:#ddd; background-color:#222; border-color:#222; text-decoration:none; box-shadow:none; }
.btn:hover, .btn:focus { color:#333; background-color:#fed925; border-color:#fed925; text-decoration:none; box-shadow:none; }

.btn-primary { color:#000;background-color:#fed925;border-color:#fed925;}
.btn-primary:hover, .btn-primary:focus { color:#000; background-color:#ffb700; text-decoration:none; border-color:#ffb700; box-shadow:none; }

.btn:focus, .btn-primary:focus, .btn-secondary:focus, .btn:focus-visible { outline:0; box-shadow:none; }


.btn-secondary { background-color:#e9ecef; color:#222; border:1px solid #e9ecef; }
.btn-secondary:hover, .btn-secondary:focus { color:#222; background-color:#fed925; border-color:#fed925; }
.btn-success { background-color:#198754; color:#fff; border:1px solid #198754; }
.btn-success:hover, .btn-success:focus { color:#222; background-color:#fed925; border-color:#fed925; }
.btn-danger {
    background-color: #ef4444;
    color: #fff;
    border: 1px solid #ef4444;
}
.btn-danger:hover, .btn-danger:focus { color:#222; background-color:#fed925; border-color:#fed925; }
.btn-info { background-color:#0dcaf0; color:#222; border:1px solid #0dcaf0; }
.btn-info:hover, .btn-info:focus { color:#222; background-color:#fed925; border-color:#fed925; }
.btn-warning { background-color:#ffc107; color:#222; border:1px solid #ffc107; }
.btn-warning:hover, .btn-warning:focus { color:#222; background-color:#fed925; border-color:#fed925; }
.btn-dark {color:#e9ecef;}
.btn-dark:hover, .btn-dark:focus {color: #fff;}


.btn--link { background-color:transparent; border:0; margin:0; color:#090a0a; text-align:left; text-decoration:none; outline:none !important;box-shadow:none !important; }
.btn--link:hover, .btn--link:focus { color:#fed925; text-decoration:none; background-color:transparent; }


.link-underline { text-decoration:underline; }
.link-underline:hover { text-decoration:none; }

.btn-link {color:#222; text-decoration:none; border:none; padding:0; margin:0; background-color:transparent;}
.btn-link:hover,
.btn-link:visited,
.btn-link:active { color:#090a0a; background-color:transparent; }
.btn-link.disabled, .btn-link:disabled, .btn-link.disabled:hover { color:#6c757d; pointer-events:none; }
.rounded-start { border-bottom-left-radius: 6px !important; border-top-left-radius: 6px !important; }
.rounded-end { border-top-right-radius: 6px !important; border-bottom-right-radius: 6px !important; }

.btn-outline-primary { color:#111; border:1px solid #fed925; background-color:transparent; }
.btn-outline-primary:hover { color:#111; background-color:#fed925; border-color:#fed925; }
.btn-outline-secondary { color:#111; border:1px solid #222; background-color:transparent; }
.btn-outline-secondary:hover { color:#111; background-color:#e9ecef; border-color:#e9ecef; }
.btn-outline-light { color:#fff; background-color:transparent; border-color:#fff; }
.btn-outline-light:hover { color:#fff; background-color: #222; border-color:#222; }

/*
.btn.btn-primary.btn3d { box-shadow:0 0 0 1px #222 inset, 0 0 0 2px rgba(255,255,255,0.15) inset, 0 8px 0 0 #2f2f2f, 0 8px 8px 1px rgba(0,0,0,0.5); background-color: #222; border-color: #222; }
.btn.btn-primary.btn3d:hover, .btn.btn-primary.btn3d:focus, .btn.btn-primary.btn3d:active { box-shadow:0 0 0 1px #444 inset, 0 0 0 2px rgba(255,255,255,0.15) inset, 0 5px 0 0 #444, 0 5px 5px 1px rgba(0,0,0,0.5); background-color: #444; border-color: #444; }

.btn-secondary.animated { -webkit-animation: pulse 1s infinite ease-in-out alternate; animation: pulse 1s infinite ease-in-out alternate; }
@keyframes pulse { from { transform: scale(0.9); } to { transform: scale(1.1); } }
.btn--small, .btn-small { padding:9px 20px; font-size:13px; line-height:1; }
*/

.btn-xl { font-size:24px; padding:10px 30px; }
.btn-lg { font-size:15px; padding:10px 30px; }
.btn-sm { font-size:13px; padding:8px 25px; }
.btn-xs { font-size:11px; padding:5px 10px; line-height:1; }

input:-moz-placeholder, textarea:-moz-placeholder { opacity:0.7; -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=70)"; }
input::-webkit-input-placeholder, textarea::-webkit-input-placeholder { opacity:0.7; -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=70)"; }
input::-moz-placeholder, textarea::-moz-placeholder { opacity:0.7; -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=70)"; }
input:-ms-input-placeholder, textarea:-ms-input-placeholder { opacity:0.7; -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=70)"; }
label { display:block; margin-bottom:5px; }
label[for] { cursor:pointer; }

.redText { color:#fe254a; }
.clr-555 { color:#555; }
.mb-10 { margin-bottom:10px !important; }
.form-group { margin-bottom:1rem; }
.mask-overlay { content:""; position:fixed; background-color:#000; opacity:0.5; left:0; top:0; width:100%; height:100%; z-index:998; }

.slick-dots { margin:0; width:auto; padding:0; list-style:none; text-align:center; }
.slick-dots li { width:12px; height:12px; vertical-align:middle; position:relative; display:inline-block; padding:0; cursor:pointer; margin-right:8px; }
.slick-dots li button { color:transparent; line-height:0; font-size:0; border:0; background:transparent; display:block; cursor:pointer; color:#fff; width:12px; height:12px; text-indent:-9999px; padding:0; border-radius: 100%; background-color:#000; transition:all 0.2s; -webkit-transition:all 0.2s; -ms-transition:all 0.2s; opacity:0.2; }
.slick-dots li.slick-active button { opacity:1; }

/* Custom Radio */
.customRadio { position: relative; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; margin: 0 0 10px; }
.customRadio input[type="radio"] { position: absolute; overflow: hidden; clip: rect(0 0 0 0); display: none; }
.customRadio input[type="radio"] + label { position: relative; padding: 0 0 0 30px; cursor: pointer; }
.customRadio input[type="radio"] + label:before { content: ''; background: #fff; border: 1px solid #d0d0d0; height: 20px; width: 20px; border-radius: 50%; position: absolute; top: 0; left: 0; }
.customRadio input[type="radio"] + label:after { content: ''; background: #333; width: 10px; height: 10px; border-radius: 50%; position: absolute; top: 5px; left: 5px; opacity: 0; -webkit-transform: scale(2); transform: scale(2); -webkit-transition: transform 0.3s linear, opacity 0.3s linear; transition: transform 0.3s linear, opacity 0.3s linear; }
.customRadio input[type="radio"]:checked + label:after { opacity: 1; -webkit-transform: scale(1); transform: scale(1); }

/* Custom Checkbox */
.customCheckbox { position: relative; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; margin: 0; }
.customCheckbox input[type="checkbox"] { position: absolute; overflow: hidden; clip: rect(0 0 0 0); display: none; }
.customCheckbox input[type="checkbox"] + label { position: relative; padding: 0 0 0 30px; margin-bottom: 0; cursor: pointer; }
.customCheckbox input[type="checkbox"] + label:before { content: ''; background: #fff; border: 1px solid #d0d0d0; border-radius: 0; height: 20px; width: 20px; position: absolute; top: 0; left: 0; }
.customCheckbox input[type="checkbox"] + label:after { content: ''; border-style: solid; border-width: 0 0 2px 2px; border-color: transparent transparent #333 #333; width: 12px; height: 7px; position: absolute; top: 5px; left: 5px; opacity: 0; -webkit-transform: scale(1) rotate(-45deg); transform: scale(1) rotate(-45deg); -webkit-transition: transform 0.3s linear, opacity 0.3s linear; transition: transform 0.3s linear, opacity 0.3s linear; }
.customCheckbox input[type="checkbox"]:checked + label:after { opacity: 1; -webkit-transform: scale(1) rotate(-45deg); transform: scale(1) rotate(-45deg); color: #6d6d6d; }

/* display flex/wrap */
.d-flex-wrap { display:-webkit-flex; display:-ms-flexbox; display:flex; -ms-flex-wrap:wrap; flex-wrap:wrap; }
/* display flex/wrap/align center */
.d-flex-center { display:-webkit-flex; display:-ms-flexbox; display:flex; -ms-flex-wrap:wrap; flex-wrap:wrap; -webkit-box-align:center; -ms-flex-align:center; align-items:center; }
/* display flex/wrap/align center/justify center; */
.d-flex-justify-center { display:-webkit-flex; display:-ms-flexbox; display:flex; -ms-flex-wrap:wrap; flex-wrap:wrap; -webkit-box-align:center; -ms-flex-align:center; align-items:center; -webkit-box-pack:center; -ms-flex-pack:center; justify-content:center; }

/* Hover Effects */
.zoom-scal { position:relative; overflow:hidden; display:block; }
.zoom-scal:before { content:''; position:absolute; top:0; left:0; width:100%; height:100%; background-color:rgba(0,0,0,0.1); overflow:hidden; opacity:0; z-index:2; -webkit-transition:all 0.5s ease; transition:all 0.5s ease; }
.zoom-scal:hover:before, .zoomscal-hov:hover .zoom-scal:before { overflow:visible; opacity:1; }
.zoom-scal img { -webkit-transition:all 0.7s ease; transition:all 0.7s ease; }
.zoom-scal:hover img, .zoomscal-hov:hover .zoom-scal img { opacity:1; -webkit-transform:scale3d(1.1, 1.1, 1); transform:scale3d(1.1, 1.1, 1); }
.zoom-scal-nopb:before { content:none; }

/*
.z-n1 {z-index: -1;}
.z-0 {z-index: 0;}
.z-1 {z-index: 1;}
.z-2 {z-index: 2;}
.z-3 {z-index: 3;}

 */

/*======================================================================
  4. Container
========================================================================*/
.header-retevis-1 .container {max-width:1920px;padding-left:120px;padding-right:120px}
.container-1680.container {max-width:1680px}
.container {max-width:1280px;padding-left:30px;padding-right:30px}
.container-fluid {max-width:1920px;padding-left:60px;padding-right:60px}
.container-fluid:before, .container-fluid:after {content:''; clear:both; display:block}

.grid { *zoom:1; list-style:none; margin:0; padding:0; margin-left:-30px; }
.grid__item { float:left; padding-left:30px; width:100%; }
.grid--no-gutters > .grid__item { padding-left:0; }

.row.g-0 { margin-left:0 !important; margin-right:0 !important; }
.row.g-0 > * { padding-left:0 !important; padding-right:0 !important; }

/* Boxed Layout */
.layout-boxed { max-width:1400px; margin:0 auto 30px; position:relative; background:#fff; box-shadow: 0px 0px 5px 0px rgba(0,0,0, 0.15); }
.layout-boxed .container, 
.layout-boxed .container-fluid { max-width:100%; padding-left:30px; padding-right:30px; }
.layout-boxed .sticky-header { max-width:1400px; margin:0 auto; right:0; }

/* Fullwidth Layout */
.layout-fullwidth .container, 
.layout-fullwidth .container-fluid { max-width:100%; padding-left:30px; padding-right:30px; }

/*======================================================================
  5. Promotional Top Popup
========================================================================*/
.notification-bar { text-align:center; position:relative; z-index:5; background-color:#000; }
.notification-bar__message { color:#fff; letter-spacing:1px; font-size:11px; padding:8px 30px; display:block; }
.notification-bar__message:hover, .notification-bar__message:active, .notification-bar__message:focus, .notification-bar__message:focus-within { color:#fff; text-decoration:none; }
.close-announcement { cursor:pointer; font-size:15px; font-weight:700; position:absolute; right:40px; top:7px; height:25px; width:25px; line-height:22px; color:#fff; }

/*======================================================================
  6. Pre Loader
========================================================================*/
#pre-loader { background-color:#000; height:100%; width:100%; position:fixed; margin-top:0px; top:0px; left:0px; right:0px; bottom:0px; overflow:hidden; z-index:999999; }
#pre-loader img { text-align:center; left:0; position:absolute; right:0; top:50%;transform:translateY(-50%); -webkit-transform:translateY(-50%); -o-transform:translateY(-50%); -ms-transform:translateY(-50%); -moz-transform:translateY(-50%); z-index:99; margin:0 auto; }

/*======================================================================
  7. Headers Style
========================================================================*/
.promotion-header { color:#fe254a; letter-spacing:1px; text-transform:uppercase; padding:10px 35px; background-color:#fff0ef; text-align:center; position:relative; z-index:5; }

/* Top Info Bar */
.top-info-bar { background-color:#000; color:#fff; position:relative; z-index:49; }
.top-info-bar .item { padding:10px; }
.top-info-bar .item a { color:#fff; font-size:11px; }
.top-info-bar .item a:hover { color:#333; opacity:0.8; }
.top-info-bar .item.center { border-left:1px solid #504c4c; border-right:1px solid #504c4c; }
.top-info-bar .slick-slider .slick-track { display: flex; align-items: center; justify-content: center; }

.top-info-bar.style1 { background-color:#5aa2de; color:#333; overflow:hidden; }
.top-info-bar.style1 .item { padding:5px 25px 5px 10px; min-height:42px; }
.top-info-bar.style1 .slick-slide { color:#fff; background-color:#5aa2de; }
.top-info-bar.style1 .slick-slide + .slick-slide { color:#333; background-color:#deeefc; }
.top-info-bar.style1 .alert { padding:0; margin:0; border:0 !important; }
.top-info-bar.style1 .btn-close { color:#fff; cursor:pointer; font-size:15px; font-weight:400; position:absolute; right:10px; top:5px; height:25px; width:25px; line-height:22px; background:none; opacity:1; box-shadow:none; }
.top-info-bar.style1 .slick-slide + .slick-slide .btn-close { color:#222; }
.top-info-bar.style1 .btn-small { padding:3px 8px;border-radius:3px; }
.top-info-bar.style1 .item, .top-info-bar.style1 .item a { font-size:12px; letter-spacing:0.8px; }
.top-info-bar.style1 .item .blueText { color:#2170b1; }

.top-info-bar.style2 { background-color:#56a1b6; color:#fff; overflow:hidden; }
.top-info-bar.style2 .item, .top-info-bar.style2 .item a { font-size:13px; letter-spacing:1px; }
.top-info-bar.style2 .slick-slide { background-color:#56a1b6 !important; color:#fff !important; }
.top-info-bar.style2 .btn { color:#fff; background-color:#78c6dc; border-color:#78c6dc; }
.top-info-bar.style2 .btn:hover { opacity:0.8; }
.top-info-bar.style2 .promo-counter { display:flex; }
.top-info-bar.style2 .promo-counter .ht-count { margin: 0 8px; position: relative; }
.top-info-bar.style2 .promo-counter .ht-count:not(.days):after { content: ":"; position: absolute; left: -10px; color: #85d6fc; font-weight: 400; }
.top-info-bar.style2 .promo-counter span > span span { color: #85d6fc; font-size: 13px; font-weight: 400; }
.top-info-bar.style2 .ht-count.hour span > span:not(.time-count),
.top-info-bar.style2 .ht-count.minutes span > span:not(.time-count),
.top-info-bar.style2 .ht-count.second span > span:not(.time-count) { display:none; }

/* Marquee text */
.marquee-text { box-sizing: border-box; -webkit-box-align: center; -moz-box-align: center; -o-box-align: center; -ms-flex-align: center; -webkit-align-items: center; align-items: center; overflow: hidden; }
.marquee-text .top-info-bar { font-size: 12px; width: 200%; display: flex; -webkit-animation: marquee 25s linear infinite running; -moz-animation: marquee 25s linear infinite running; -o-animation: marquee 25s linear infinite running; -ms-animation: marquee 25s linear infinite running; animation: marquee 25s linear infinite running; }
.marquee-text .top-info-bar:hover { -webkit-animation-play-state: paused; -moz-animation-play-state: paused; -o-animation-play-state: paused; -ms-animation-play-state: paused; animation-play-state: paused; }
.marquee-text .top-info-bar .info-text { padding: 10px 30px; white-space: nowrap; display: inline-flex; align-items: center; justify-content: center; -webkit-transition: all .2s ease; transition: all .2s ease; }
.marquee-text .top-info-bar a { color: #fff; }

@-moz-keyframes marquee {
    0% { -webkit-transform:translateX(0); -moz-transform:translateX(0); -o-transform:translateX(0); -ms-transform:translateX(0); transform:translateX(0); }
    100% { -webkit-transform:translate(-50%); -moz-transform:translate(-50%); -o-transform:translate(-50%); -ms-transform:translate(-50%); transform:translate(-50%); }
}
@-webkit-keyframes marquee {
    0% { -webkit-transform:translateX(0); -moz-transform:translateX(0); -o-transform:translateX(0); -ms-transform:translateX(0); transform:translateX(0); }
    100% { -webkit-transform:translate(-50%); -moz-transform:translate(-50%); -o-transform:translate(-50%); -ms-transform:translate(-50%); transform:translate(-50%); }
}
@-o-keyframes marquee {
    0% { -webkit-transform:translateX(0); -moz-transform:translateX(0); -o-transform:translateX(0); -ms-transform:translateX(0); transform:translateX(0); }
    100% { -webkit-transform:translate(-50%); -moz-transform:translate(-50%); -o-transform:translate(-50%); -ms-transform:translate(-50%); transform:translate(-50%); }
}
@keyframes marquee {
    0% { -webkit-transform:translateX(0); -moz-transform:translateX(0); -o-transform:translateX(0); -ms-transform:translateX(0); transform:translateX(0); }
    100% { -webkit-transform:translate(-50%); -moz-transform:translate(-50%); -o-transform:translate(-50%); -ms-transform:translate(-50%); transform:translate(-50%); }
}

/* Top Bar */
.top-bar { color:#fff; font-size:12px; background-color:#333; position:relative; z-index:49; }
.top-bar .inner { min-height:35px; }
.top-bar a, .top-bar .social-icons li a { color:#fff; }
.top-bar .an { font-size:13px; }
.top-bar a:hover, .top-bar .social-icons li a:hover { color:#fff; opacity:0.8; }
.top-bar .social-icons .tooltip-label { top:25px; }
.top-bar .social-icons .tooltip-label:before { top:-4px; bottom:auto; border-bottom:5px solid #000; border-top:transparent; }
.top-bar .social-icons li:hover .tooltip-label { top:20px; }

.top-header { color:#fff; padding-top:8px; padding-bottom:10px; background:#000; height:38px; }
.top-header a { color:#fff; }

/*Promo Counter Style Days Hr:Min:Sec */
.promo-counter.days-time { display:flex; }
.promo-counter.days-time .ht-count { margin: 0 8px; position: relative; }
.promo-counter.days-time .ht-count:not(.days):after { content: ":"; position: absolute; left: -10px; font-weight: 600; }
.promo-counter.days-time span > span span { font-size: 12px; }
.promo-counter.days-time .ht-count.hour span > span:not(.time-count),
.promo-counter.days-time .ht-count.minutes span > span:not(.time-count),
.promo-counter.days-time .ht-count.second span > span:not(.time-count) { display:none; }

.header-main { position:relative; z-index:49; background-color:#fff; border-bottom:0; transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; -ms-transition:all 0.3s ease-in-out; min-height:60px; }
.header-wrap { min-height:90px; position:relative; }
.sticky-header .header-wrap { min-height:60px; }
.showOverly .modalOverly { display:block; }
.searchact .modalOverly { z-index:45; }
body:not(.searchact) .header-main:not(.sticky-header) { background:none; box-shadow:none; border:0; }
.header-1 .header-main:not(.sticky-header), .header-11.header-main:not(.sticky-header) { position:absolute; left:0; right:0; }
.header-wrap > .row { width:100%; margin:0; }
.sticky-header { position:fixed; top:0; z-index:1000; width:100%; left:0; background-color:#fff; border-bottom:0; }
.menu-outer .container, .menu-outer .container-fluid { position:relative; }
.header-content-wrapper { width:100%; padding:15px 0; }

.mih-50:not(.sticky-header) { min-height:50px !important; }
.mih-55:not(.sticky-header) { min-height:55px !important; }
.mih-70:not(.sticky-header) { min-height:55px !important; }
.mih-80:not(.sticky-header) { min-height:60px !important; }
.mih-90:not(.sticky-header) { min-height:60px !important; }

.logo { margin:0; }
.logo a { position: relative;display: block;font-weight: 700;text-transform: uppercase;font-size: 22px;line-height: 1;text-decoration: none;text-align: center; }
.logo img { display:block; max-width:154px; max-height:32px; margin:0 auto; }

.iconset .icon { font-size:17px; min-width:17px; text-decoration:none; display:inline-block; vertical-align:middle; }
.iconset { position:relative; color:#030505; height:30px; line-height:30px; text-align:center; padding:0 9px; cursor:pointer; display:flex;align-items:center; }
.iconset:hover .icon, .iconset:hover .text, .iconset:hover a { color:#fed925; }
.iconset:hover .tooltip-label { opacity:1; top:-26px; visibility:visible; }
.header .iconset .tooltip-label { top: -30px; }
.header .iconset:hover .tooltip-label { top:-20px; }
.sticky-header .iconset:hover .tooltip-label { top:-17px; }
.counter { font-size:11px; color:#fff; background-color:#000; min-width:16px; height:16px; line-height:16px; top:3px; right:-13px; }

#settingsBox, #cart-drawer, #searchPopup, #userLinks { color:#050000; text-align:left; background-color:#fff; box-shadow:0 0 15px rgba(5,0,0,.1); -webkit-box-shadow:0 0 15px rgba(5,0,0,.1); -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; }
#settingsBox, #userLinks { visibility:hidden; opacity:0; padding:20px; width:300px; position:absolute; top:130%; right:15px; z-index:99; }
#settingsBox.active, #userLinks.active { visibility:visible; opacity:1; top:100%; }
#settingsBox .btn { width:100%; }
#userLinks { width:200px;} 
#userLinks ul { list-style:none; padding:0; margin:0; }
#userLinks ul li { display:block; }
#userLinks li a { padding:5px; display:block; color:#666; }
.icons-col .customer-links { font-size:12px; text-transform:uppercase; }
.icons-col .customer-links .register { text-decoration:underline; }
#settingsBox .ttl { font-size:12px; margin:0 0 10px; display:block; letter-spacing:0.2px; text-transform:uppercase; }
#settingsBox #language { margin-bottom: 0; }
.cnrLangList li { display:inline-block; margin:0 5px 5px 0; cursor:pointer; }
.cnrLangList li a { color:#333; display:block; font-size:12px; text-transform:uppercase; padding:1px 10px; opacity:0.6; border:1px solid rgba(0,0,0,0.3); background:#fafafa; }
.cnrLangList li a:hover, .cnrLangList li a.active, .cnrLangList li a:focus { color:#fff; border:1px solid #000; opacity:1; text-decoration:none; background:#131313; }


.minicart-right-drawer.right .modal-dialog { position:fixed; margin:auto; width:350px; max-width:100%; height:100%; z-index:1024; -webkit-transform:translate3d(0%,0,0); -ms-transform:translate3d(0%,0,0); -o-transform:translate3d(0%,0,0); transform:translate3d(0%,0,0); }
.minicart-right-drawer.right .modal-content { border:none; border-radius:0; height:100%; overflow-y:auto; }
.minicart-right-drawer.right.fade .modal-dialog { right:-350px; -webkit-transition:opacity 0.3s linear, right 0.3s ease-out; -moz-transition:opacity 0.3s linear, right 0.3s ease-out; -o-transition:opacity 0.3s linear, right 0.3s ease-out; transition:opacity 0.3s linear, right 0.3s ease-out; }
.minicart-right-drawer.right.fade.show .modal-dialog { right:0; }


#cart-drawer { color:#050000; padding:20px; width:100%; height:100%; overflow:auto; z-index:9999; }
#cart-drawer.active { right:0; }
#cart-drawer > h4 { color:#666; font-weight:500; text-transform:uppercase; text-align:left; margin:0 0 20px; border-bottom:1px solid #e8e8e8; padding-bottom:15px; }
#cart-drawer .close-cart { color:#050000; font-size:17px; float:right; margin-top:-3px ; opacity:0.8; text-decoration:none; }

.minicart-header { margin:0 0 20px; border-bottom:1px solid #e8e8e8; padding-bottom:15px; }
.minicart-header > h4 { color:#666; font-weight:500; text-transform:uppercase; text-align:left; margin:0 0; }
.minicart-content { padding:0 20px; margin:0; z-index:1001; position:absolute; left:0; overflow:hidden auto; width:100%; height:calc(100% - 290px); }
.minicart-content .item { padding:0 0 10px; margin:0 0 10px; display:block; border-bottom:solid 1px #eee; }
.minicart-content .item .product-image { max-width:23%; flex:1; -webkit-flex:1; -ms-flex:1; }
.minicart-content .item .product-details { width:75%; flex:1; -webkit-flex:1; -ms-flex:1; padding-left:15px; padding-right:10px; text-align:left; }
.minicart-content .item .product-title { color:#333; font-size:13px; white-space:normal; text-decoration:none; display:block; line-height:20px; margin-bottom:0; }
.minicart-content .item .remove { color:#5c5c5c; display:inline-block; font-size:14px; padding:2px 4px 0; }
.minicart-content .item .remove:hover { color:#000; }
.minicart-content .item .remove i { vertical-align:middle; font-size:14px; }
.minicart-content .item .edit-i.remove .icon { font-size:12px; padding-top:0; }
.minicart-content .item .qtyField span { display:inline-block; padding:0; border:0; }
.minicart-content .item .variant-cart { color:#777; font-size:11px; }
.minicart-content .item .wrapQtyBtn { display:block; float:none; }
.minicart-content .item .qtyField { width:77px; }
.minicart-content .item .qtyField .qtyBtn { height:30px; }
.minicart-content .item .qtyField .qty { width:77px; height:30px; padding:0 20px; }

.minicart-bottom { color:#666; position:absolute; bottom:0; left:0; width:100%; padding:10px 20px 20px 20px; }
.minicart-bottom .shipinfo { background-color:#f6f6f6; padding:10px; }
.minicart-bottom .shipinfo p { font-size:11px; }
.minicart-bottom .agree-check { font-size:11px; text-transform:uppercase; }
.minicart-bottom .subtotal { padding:5px 0 10px; }
.minicart-bottom .subtotal:before, .minicart-bottom .subtotal:after { content:''; clear:both; display:block; }
.minicart-bottom .subtotal.list { border:0; margin:0; padding:0;}
.minicart-bottom .subtotal.list > span { font-size:14px; font-weight:400; }
.minicart-bottom .subtotal > span { float:left; text-transform:uppercase; font-size:16px; text-align:left; font-weight:700; }
.minicart-bottom .subtotal .product-price { float:right; }

.modal-open { padding-right:0 !important; }
.modal-open .modal { padding-right:0 !important; }

.search-drawer { padding:40px 50px; background-color:#fff; opacity:0; visibility:hidden; position:fixed; top:0; left:0; z-index:9999; text-align:left; transition:all .3s ease 0s; -webkit-transition:all .3s ease 0s; -ms-transition:all .3s ease 0s; -o-transition:all .3s ease 0s; width:100%; box-shadow:0 0 6px rgba(0,0,0,0.2); -webkit-box-shadow:0 0 6px rgba(0,0,0,0.2);}
.search-drawer .container, .search-drawer .container-fluid { position:relative; }
.search-drawer.search-drawer-open { opacity:1; transform:translate(0,0); -webkit-transform:translate(0,0); -ms-transform:translate(0,0); -o-transform:translate(0,0); visibility:visible; }
.search-drawer.search-drawer-open .block { background-color:transparent; padding:0; }
.search-drawer .title { color:#333; font-size:15px; margin-bottom: 20px; }
.search-drawer .label { border:0; clip:rect(0,0,0,0); height:1px; margin:-1px; overflow:hidden; padding:0; position:absolute; width:1px; }
.search-drawer .control { border-bottom:0; }
.search-drawer .input-text { color:#050000; font-size:13px; padding:5px 35px; background:none; border:0; }
.search-drawer .action.search { font-size:18px; position:absolute; left:0; border:0; margin-top:3px; background:none; cursor:pointer; height:38px; }
.search-drawer .action.search:hover { color:#333 }
.search-drawer .closeSearch { color:#050000; font-size:18px; position:absolute; top:7px; right:0; z-index:10; cursor:pointer; width:25px; height:25px; line-height:25px; text-align:center; }
.search-drawer .searchField { display:flex; align-items:center; width:100%; }
.search-drawer .search-category { display:table-cell; padding-right:15px; }
.search-drawer .search-category select { font-size:13px; border:0; min-width:135px; }
.search-drawer .input-box { display:table-cell; width:auto; -webkit-flex:1; flex:1; position:relative; }

#search-popup { padding:20px 60px 20px 55px; text-align:left; background-color:#f2f2f2; transition:all 0.3s ease-in-out; width:100%; opacity:0; visibility:hidden; position:absolute; top:100%; left:0; z-index:10; }
#search-popup.active { top:100%; opacity:1; visibility:visible; pointer-events:auto; }

.social-icons { list-style:none; padding:0; margin:0; display:flex; flex-wrap:wrap; }
.social-icons li a { position:relative; padding:0 5px; color:#3e3e3e; display:flex;align-items:center; }
.social-icons li a:hover { color:#333; }
.social-icons li:hover .tooltip-label { opacity:1; top:-26px; visibility:visible; }

/* Navigation */
#siteNav { margin:0 auto; padding:0; list-style:none; display:flex; flex-wrap:wrap; }
#siteNav.right { text-align:right; justify-content:flex-end; }
#siteNav.left { text-align:left; justify-content:flex-start; }
#siteNav.center { text-align:center; justify-content:center; }
#siteNav .menubox p .title { color:#222; background-color:#fff; border-radius:2px; padding:6px 15px; margin:0 0 20px; white-space:nowrap; }
#AccessibleNav { padding-left:0; }
.mobile-nav-wrapper, .site-header__logo.mobileview { display:none; }

@media (min-width:990px) {
    .header .container { position:relative; }
    .header .container .d-menu-col { position:static; }
    #siteNav a { text-decoration:none; font-size:13px; display:block; opacity:1; -webkit-font-smoothing: antialiased; letter-spacing:0.05em; position:relative; }
    #siteNav > li { display:inline-block; text-align:left; }
    #siteNav > li > a { color:#111; padding:10px 0 10px; margin-right:30px; }
    #siteNav > li > a:after { content: ""; width:0; height:1px; display:block; background-color:#000; }
    #siteNav > li > a:hover:after, #siteNav > li > a:focus:after { width:100%; }
    #siteNav.medium > li a { font-weight:500; }
    #siteNav.hidearrow > li > a .an { display:none; }
    #siteNav > li > a .an { vertical-align:middle; }
    #siteNav > li:hover > a, #siteNav > li > a:hover:hover, #siteNav > li > a:hover { color:#000; }

    #siteNav > li > a .navLbl{ white-space:nowrap; color:#fff; background-color:#fee671; font-size:10px; font-weight:400; line-height:normal; display:inline-block; padding:1px 5px; border-radius:0; position:absolute; top:-9px; left:50%; }
    #siteNav > li > a .navLbl:after { content:" "; display: block; width:0; height:0; border:3px solid transparent; border-top-color:#fee671; border-left-color:#fee671; position:absolute; bottom:-4px; left:0; }
    #siteNav > li > a .navLbl.new { background-color: #058139; }
    #siteNav > li > a .navLbl.new:after { border-top-color: #058139; border-left-color: #058139; }
    #siteNav > li > a .navLbl.hurryup { background-color: #43b14f; }
    #siteNav > li > a .navLbl.hurryup:after { border-top-color: #43b14f; border-left-color: #43b14f; }
    #siteNav > li > a .navLbl.hot { background-color: #e61711; }
    #siteNav > li > a .navLbl.hot:after { border-top-color: #e61711; border-left-color: #e61711; }

    #siteNav > li .megamenu { opacity:0; visibility:hidden; padding:30px; width:100%; position:absolute; top:auto; left:0; z-index:999; background-color:#fff; box-shadow: 2px 2px 1px 0px rgba(0,0,0,0.3); pointer-events:none; max-height:650px; overflow:auto; box-shadow:0px 0px 15px rgba(0,0,0,0.1); -webkit-transform:translateY(20px); transform:translateY(20px); -webkit-transition:all .3s ease-in; transition:all .3s ease-in; }
    /*
    .index-demo10 #siteNav > li .megamenu.style3 { max-width:900px; }

     */
    #siteNav > li .megamenu ul { padding:0; list-style:none; }
    #siteNav > li:hover > .megamenu { -webkit-transform:translateY(0); transform:translateY(0); opacity:1; visibility:visible; pointer-events:visible; }
    #siteNav > li .megamenu.style1 .lvl-1 { margin-bottom:0; }
    #siteNav > li .megamenu .lvl-1 a.lvl-1 { color:#333; font-size:13px; padding:0 0 8px; font-weight:400; }
    #siteNav > li .megamenu .lvl-1 .site-nav { color:#666; padding:3px 0; font-weight:400; }
    #siteNav > li .megamenu .lvl-1 .site-nav:hover { color:#111; }
    #siteNav > li .megamenu .lvl-1 .site-nav:before { content: ""; display:inline-block; width:0px; height:2px; vertical-align:middle; background-color:#000; -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; }
    #siteNav > li .megamenu .lvl-1 .site-nav:hover:before { width:5px; margin-right:3px; }
    #siteNav > li .megamenu .lvl-1 .site-nav.menu-title:hover:before { display:none; }

    #siteNav > li .megamenu.style1 { overflow:hidden; }
    #siteNav > li .megamenu.style2 .lvl-1 { margin-bottom:0; }
    #siteNav > li .megamenu .menu-title { font-weight:600 !important; }
    #siteNav > li.mdropdown { position:relative; }
    #siteNav > li .megamenu .imageCol { padding-bottom:25px; }

    #siteNav > li .megamenu .grid-products .item { margin: 0 0 20px; }
    #siteNav > li .megamenu .grid-products a, #siteNav > li .megamenu .grid-products .product-price { font-size: 12px; }
    #siteNav > li .megamenu .grid-products .product-review { margin: 0px 0 10px; }
    #siteNav > li .megamenu .grid-products .product-review .an { font-size: 11px; }

    #siteNav > li .offerBanner { padding: 20px; text-align: center; height: 100%; }
    #siteNav > li .gridproduct-banner .grid-products { padding: 20px; }

    #siteNav > li .megamenu .nav-pills .nav-link { color: #666; font-weight: 600; margin: 0; padding: 12px 30px 12px 15px; border-radius: 5px !important; background-color: #fff; border: 0; box-shadow: none; }
    #siteNav > li .megamenu .nav-pills .nav-link:hover, #siteNav > li .megamenu .nav-pills .nav-link.active { color: #333; border-color: #eee; background-color: #f2f2f2; }

    /*
    .index-demo10 #siteNav > li .offerBanner { background-color:#814348; }
    .index-demo10 #siteNav > li .offerBanner h4, .index-demo10 #siteNav > li .offerBanner p { color:#fff; }

     */

    #siteNav a .lbl { color:#fff; font-size:9px; font-weight:400; letter-spacing:0; line-height:1; display:inline-flex; align-items:center; padding:3px 4px; background-color:#0a9339; position:relative; vertical-align:middle; }
    #siteNav a .lbl:after { content:" "; display:block; width:0; height:0; position:absolute; bottom:3px; left:-7px; border:4px solid transparent; border-right-color:transparent; border-right-color:#0a9339; display: none; }
    #siteNav a .lbl.nm_label1 { background-color:#eb5c3c; }
    #siteNav a .lbl.nm_label1:after { border-right-color:#eb5c3c; }
    #siteNav a .lbl.nm_label2 { background-color:#43b14f; }
    #siteNav a .lbl.nm_label2:after { border-right-color:#43b14f; }
    #siteNav a .lbl.nm_label3 { background-color:#d8a4c9; }
    #siteNav a .lbl.nm_label3:after { border-right-color:#d8a4c9; }
    #siteNav a .lbl.nm_label4 { background-color:#fdb818; }
    #siteNav a .lbl.nm_label4:after { border-right-color:#fdb818; }
    #siteNav a .lbl.nm_label5 { background-color:#00b2ed; }
    #siteNav a .lbl.nm_label5:after { border-right-color:#00b2ed; }

    #siteNav > li.dropdown { position:relative; }
    #siteNav > li .dropdown, #siteNav > li .dropdown ul { background-color:#fff; list-style:none; opacity:0; visibility:hidden; width:220px; position:absolute; top:59px; left:0; z-index:999; box-shadow:0px 0px 15px rgba(0,0,0,0.1); -webkit-box-shadow:0px 0px 15px rgba(0,0,0,0.1); -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; padding:10px; }
    #siteNav > li:hover > .dropdown, #siteNav > li .dropdown li:hover > ul { top:40px; opacity:1; visibility:visible; }
    #siteNav > li ul.dropdown li { border-top:1px solid #e7e7e7; position:relative; }
    #siteNav > li ul.dropdown li:first-child { border:0; }
    #siteNav > li ul.dropdown li a { color:#000; font-weight:400; padding:8px 12px; background-color:#fff; }
    #siteNav > li ul.dropdown li:hover > a, #siteNav > li ul.dropdown li a:hover { color:#000; padding-left:15px; }
    #siteNav > li ul.dropdown li a .an { font-size:18px; position:absolute; right:5px; top:8px; }
    #siteNav > li ul.dropdown li ul { top:20px; left:100%; }
    #siteNav > li ul.dropdown li:hover > ul { top:0; }

    #siteNav .menu-brand-logo { width:50%; float:left; text-align:center; padding-right:10px; }
    #siteNav .menu-brand-logo a { display:block; margin-bottom:10px; border:1px solid #ddd; }
    #siteNav .menu-brand-logo a:hover { border-color:#000; }
    #siteNav .menu-brand-logo a img { display:inline-block; vertical-align:middle; }

    .mm-Banners:before, .mm-Banners:after { content: ''; clear:both; display:block; }
    .mm-Banners { clear:both; }
    .mm-Banners .imageCol { padding-bottom:0 !important; }

    .header-3 #siteNav > li .megamenu { max-width:1400px; right:0; margin:0 auto; }

}

@media (min-width:1200px) {
    #siteNav > li .megamenu { padding:30px 60px;}
}

@media (min-width:1401px) {
    #siteNav > li .megamenu { padding:30px 120px;}
}

/*----- Classic Header Style -----*/
.template-index:not(.searchact) .classicHeader:not(.sticky-header) { position:absolute; left:0; right:0; }
.template-index:not(.searchact) .classicHeader:not(.sticky-header) #siteNav > li > a { color:#fff; }
.template-index:not(.searchact) .classicHeader:not(.sticky-header) #siteNav > li > a:hover { color:#fff; opacity:0.8; }
.template-index:not(.searchact) .classicHeader:not(.sticky-header) #siteNav > li > a:after { background-color:#fff; }
.template-index:not(.searchact) .classicHeader:not(.sticky-header) .iconset,
.template-index:not(.searchact) .classicHeader:not(.sticky-header) .iconset a { color:#fff; }
.template-index:not(.searchact) .classicHeader:not(.sticky-header) .iconset a:hover,
.template-index:not(.searchact) .classicHeader:not(.sticky-header) .iconset:hover .icon, 
.template-index:not(.searchact) .classicHeader:not(.sticky-header) .iconset:hover .text { color:#fff; opacity:0.8; }
.template-index:not(.searchact) .classicHeader:not(.sticky-header) .counter { background:#eee; color:#000; }
.template-index:not(.searchact) .classicHeader:not(.sticky-header) .js-mobile-nav-toggle .an { color: #fff; }
.template-index:not(.searchact) .classicHeader:not(.sticky-header) .sticky-logo { display:none; }
.template-index.searchact .classicHeader .default-logo,
.template-index .classicHeader.sticky-header .default-logo { display:none; }

/*-----  7.1 Header Styles ----- */
.header .header-main { min-height:90px; }
.header .social-icons li .an { font-size:14px; }

.search-inline .search-category select { min-width:140px; border-radius:3px 0 0 3px; }
.search-inline .search-category select option,
.search-inline .search-category select optgroup { background-color:#fff; color:#000; }
.search-inline .input-box .action { display:flex; justify-content:center; align-items:center; width:46px; border-radius:0 3px 3px 0; }
.search-inline .input-box .action:hover { color: #eb5c3c; }

.header-retevis-1.header-main, .header-retevis-1 .header-wrap { background-color: #fff; color: #222; }
.header-retevis-1.sticky-header .header-wrap { min-height: 60px !important; }
.header-retevis-1 .menu-outer { background-color: #fed925; padding: 0; }
.header-retevis-1 #siteNav > li > a { color: #333; font-family:'Retevis-Bold',Helvetica,Arial,Tahoma,sans-serif; font-size: 15px; font-weight: 400; }
.header-retevis-1 #siteNav > li > a:hover { color: #000; }
.header-retevis-1 #siteNav > li > a:after { background-color:#444; }
.header-retevis-1 .iconset, .header-retevis-1 .iconset a { color:#333; }
.header-retevis-1 .iconset:hover, .header-retevis-1 .iconset a:hover, .header-retevis-1 .iconset:hover .icon { color: #ffb700; }
.header-retevis-1 .counter { background-color:#fed925; color:#333; }
.header-retevis-1 .js-mobile-nav-toggle .an { color:#333; }
.header-retevis-1 .search-inline { width: 530px; margin: 0 auto; }
.header-retevis-1 .search-inline .search-category select,
.header-retevis-1 .search-inline .input-box .input-text,
.header-retevis-1 .search-inline .input-box .action { background: transparent; color: #333; border-color: rgba(0,0,0,.2); height: 38px; }
.header-retevis-1 .search-inline .input-box .action:hover { color: #fed925; }

/* Sticky Menubar Mobile */
.menubar-mobile { position: fixed; bottom: 0; right: 0; left: 0; z-index: 999; overflow: auto hidden; padding: 5px; height: 47px; background: #fff; box-shadow: 0 0 10px rgba(0,0,0,.12); transition: transform .25s; }
.menubar-mobile .menubar-item { position: relative; -ms-flex: 1 0 20%; flex: 1 0 20%; }
.menubar-mobile .menubar-item > a { position: relative; padding-right: 10px; padding-left: 10px; line-height: 1; display:flex; align-items: center; flex-direction: column; justify-content: center; }
.menubar-mobile .menubar-label { display: block; padding:0; margin: 5px 0 0; text-align: center; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-size: 11px; font-weight: 500; line-height: 1; }
.menubar-mobile .counter { right: -22px; }
.footer .copyright-col img { margin-bottom:50px; }
@media only screen and (min-width: 576px) {.footer .copyright-col img { margin-bottom:50px; } }
@media only screen and (min-width: 767px) {.footer .copyright-col img { margin-bottom:80px; } }
@media only screen and (min-width: 992px) {.footer .copyright-col img { margin-bottom:50px; } }


/*======================================================================
  8. Homepage Demo's
========================================================================*/


/*----- Homepage Demo 12 -----*/
.index-retevis-1 .btn:hover { color: #000; background-color: #fed925; border-color: #fed925; }
.index-retevis-1 .slideshow--large { height: 600px; background-position: center top !important; }
.index-retevis-1 .slideshow .slider2 .bg-size { background-position: center bottom !important; }
.index-retevis-1 .slideshow .ss-mega-title { font-size:67px; }
.index-retevis-1 .slideshow .ss-sub-title { font-size:17px }
.index-retevis-1 .slideshow .yellow { color:#ffb700; }
.index-retevis-1 .slideshow .btn-primary, .index-retevis-1 .btn-primary { color: #222; background-color: #fed925; border-color: #fed925; }
.index-retevis-1 .slideshow .btn-primary:hover, .index-retevis-1 .btn-primary:hover, .index-retevis-1 .btn-primary:focus,
.index-retevis-1 .slideshow .btn-primary:focus { color: #222; background-color: #ffb700; border-color: #ffb700; }
.index-retevis-1 .collection-slider-6items .slick-list { margin: 0 -10px; }
.index-retevis-1 .collection-slider-6items .slick-slide { margin: 0 10px; }
.index-retevis-1 .collection-banners.style8 .details .title { font-size: 30px; }
.index-retevis-1 .collection-banners.style8 .details.top-left { top: 20px !important; left: 20px; }
.index-retevis-1 .collection-banners.style8 .details .btn { color: #111; background-color: #fff; border-color: #fff; padding: 8px 20px; font-size: 13px; text-transform: none; line-height: 1.2; }
.index-retevis-1 .collection-banners.style8 .details .btn:hover { color: #fff; background-color: #111; border-color: #111; }
.index-retevis-1 .section-header h2 { font-size: 30px; font-weight: 700; }
.index-retevis-1 .parallax-banner-style4 .hero .text-small .mega-subtitle { color: #1e2832; font-size: 14px; font-weight: 400; }
.index-retevis-1 .parallax-banner-style4 .hero .text-small .mega-title { color: #1e2832; font-size: 40px; font-weight: 700; }
.index-retevis-1 .parallax-banner-style4 .hero .saleTime { margin: 0 0 20px; }
.index-retevis-1 .parallax-banner-style4 .hero .saleTime span > span span { color: #1e2832; }
.index-retevis-1 .parallax-banner-style4 .hero .saleTime span > .count-inner { color: #1e2832; background: #ffb700; border-color: #ffb700; border-radius: 10px; }
.index-retevis-1 .collection-banners.style8 .details.center-left { left: 10px; }
.index-retevis-1 .collection-banners.style8.two-banner .details .title { font-size: 40px; }
.index-retevis-1 .collection-banners.style8.two-banner .collection-grid-item .details .inner { font-size: 20px; }
.index-retevis-1 .collection-banners.style8.two-banner .details .btn { padding: 9px 20px; font-size: 15px; }
.index-retevis-1 #site-scroll { color: #fff; background: #1e2832; }
.index-retevis-1 #site-scroll:hover { color: #000; background: #fed925; }
.index-retevis-1 .grid-products .product-review { margin: 8px 0 0; }
.index-retevis-1 .collection-banners.style8.two-banner .details .title.yellow { color:#ffb700; }
.index-retevis-1 .store-features { background-color:#fed925; }


/*=====================================================================
  9. Homepage Sections
========================================================================*/
/* 9.1 Homepage Slideshow */
.slideshow-wrapper, .slideshow .slide { position:relative; }
.slideshow .bottom-middle { height:100%; padding-left:60px; padding-right:60px; }
.slideshow .bottom-middle .slideshow-content-in { top:auto; bottom:50px; -ms-transform:none; -webkit-transform:none; transform:none; }
.slideshow .bottom-left .slideshow-content-in { top:auto; left:60px; bottom:60px; -ms-transform:none; -webkit-transform:none; transform:none; }
.slideshow .middle-right .slideshow-content-in { text-align:left; right:17%; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); }
.slideshow .middle-left .slideshow-content-in { text-align:left; left:17%; }
.slideshow .middle-bottom .slideshow-content-in { right: 14%; bottom: 8%; top: auto; -ms-transform: none; -webkit-transform: none; transform: none; }
.slideshow-content-in { position:absolute; max-width:max-content; width:100%; top:50%; z-index:3; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); }
.slideshow .wrap-caption { display:inline-block; padding:25px; }
.slideshow--medium { height: 600px; }
.slideshow--large { height: 700px; }
.slideshow--xlarge { height: 900px; }

/* Slide Caption Animation */
.slideshow .animation.style1 { opacity:0; -ms-transition:1s all 100ms; -webkit-transition:1s all 100ms; transition:1s all 100ms; -webkit-transition:1s all 100ms; transform:scale(0.8); -webkit-transform:scale(0.8); }
.slideshow .slick-active .animation.style1 { opacity:1; transform:scale(1); -webkit-transform:scale(1); -ms-transform:scale(1); }
.slideshow .animation.style2 { opacity:0; margin-bottom:-30px; transition:1s all .1s; -webkit-transition:1s all .1s; -ms-transition:1s all .1s; }
.slideshow .slick-active .animation.style2 { opacity:1; margin-bottom:0; }

.slideshow .animation.style3 { opacity:0; transform: translateY(-15px); transition: .8s all .2s; }
.slideshow .slick-active .animation.style3 { opacity:1; transform: translateY(0); }
.slideshow .animation.style3 .ss-btnWrap { opacity: 0; transition: .6s all .4s; transform: translateY(10px); }
.slideshow .slick-active .animation.style3 .ss-btnWrap { opacity:1; transform: translateY(0); }

.slideshow .slick-active .animation.style4 .ss-small-title { animation-delay: .3s; -webkit-animation-delay: .3s; animation-duration: .3s; -webkit-animation-duration: .3s; animation-fill-mode: both; -webkit-animation-fill-mode: both; animation-name: fadeInDown; -webkit-animation-name: fadeInDown; }
.slideshow .slick-active .animation.style4 .ss-mega-title { animation-delay: .5s; -webkit-animation-delay: .5s; animation-duration: .5s; -webkit-animation-duration: .5s; animation-fill-mode: both; -webkit-animation-fill-mode: both; animation-name: fadeInDown; -webkit-animation-name: fadeInDown; }
.slideshow .slick-active .animation.style4 .ss-sub-title { animation-delay: .4s; -webkit-animation-delay: .4s; animation-duration: .4s; -webkit-animation-duration: .4s; animation-fill-mode: both; -webkit-animation-fill-mode: both; animation-name: fadeInUp; -webkit-animation-name: fadeInUp; }
.slideshow .slick-active .animation.style4 .ss-btnWrap { animation-delay: .5s; -webkit-animation-delay: .5s; animation-duration: .5s; -webkit-animation-duration: .5s; animation-fill-mode: both; -webkit-animation-fill-mode: both; animation-name: fadeInUp; -webkit-animation-name: fadeInUp; }
/* End Slide Caption Animation */

.slideshow .ss-mega-title { color:#000; line-height:1.1; text-shadow:1px 1px 7px rgba(0,0,0,0); margin: 0 0 10px; }
.slideshow .ss-sub-title { color:#000; margin-bottom:20px; text-shadow:1px 1px 4px rgba(0,0,0,0); display:block; }
.slideshow .whiteText .ss-mega-title, .slideshow .whiteText .ss-sub-title, .slideshow .whiteText .ss-small-title { color:#fff; text-transform:none; }
.slideshow-overlay:before { content: ''; position:absolute; top:0; right:0; bottom:0; left:0; opacity:0.5; z-index:3; }
.slideshow .slick-slide img { width:100%; }
.slideshow .btn-primary + .btn-primary { margin-left:20px; }
.slideshow .whiteText .btn-primary { color:#000; background-color:#fff; border-color:#fff; }
.slideshow .whiteText .btn-primary:hover, .slideshow .whiteText .btn-primary:focus { color:#fff; background-color:#000; border-color:#000; }
.slideshow .slick-prev, .slideshow .slick-next { line-height:normal; font-size:0px; padding:0; border:0; opacity:0; visibility:hidden; position:absolute; z-index:4; top:50%; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); width:80px; height:100%; text-align:center; background-color:transparent; -ms-transition:all ease-out 0.2s; -webkit-transition:all ease-out 0.2s; transition:all ease-out 0.2s; }
.slideshow .btn-outline-primary { background-color:transparent !important; }
.slideshow .btn-outline-primary:hover { background-color:#000 !important; }
.slideshow .whiteText .btn-outline-primary { color:#fff; border-color:#fff; background-color:transparent !important; }
.slideshow .whiteText .btn-outline-primary:hover { color:#000; border-color:#fff; background-color:#fff !important; }
.slideshow:hover .slick-prev, .slideshow:hover .slick-next { opacity:1; visibility:visible; }
.slideshow .slick-prev { left:0; }
.slideshow .slick-next { right:0; }
.slideshow .slick-next:before { content:"\ea8c"; font-family:"annimex-icons"; color:#fff; }
.slideshow .slick-prev:before { content:"\ea8b"; font-family:"annimex-icons"; color:#fff; }
.slideshow .slick-prev:before, .slideshow .slick-next:before { font-size:25px; line-height:25px; }
.slideshow .btn { color:#fff; background-color:#000; border-color:#000; }
.slideshow .btn:hover { color:#222; background-color:#fed925; border-color:#fed925; }
.slideshow .slick-dots { margin:0; width:auto; padding:0; list-style:none; position:absolute; bottom:30px; text-align:center; left:50%; transform:translateX(-50%); -webkit-transform:translateX(-50%); -ms-transform:translateX(-50%); }
.slideshow .slick-dots li { width:12px; height:12px; vertical-align:middle; position:relative; display:inline-block; padding:0; cursor:pointer; margin-right:8px; }
.slideshow .slick-dots li button { color:transparent; line-height:0; font-size:0; background:transparent; display:block; cursor:pointer; color:#fff; width:14px; height:14px; text-indent:-9999px; padding:0; border-radius: 100%; border:2px solid #fff; transition:all 0.2s; -webkit-transition:all 0.2s; -ms-transition:all 0.2s; opacity:1; }
.slideshow .slick-dots li.slick-active button { background-color:#fff; }
.slideshow img.mobile-hide { display:none; }

.slideshow .blue-bg { background-color:#92c6ee; }
.slideshow .blue-bg .btn { color:#fff; background:#69a6d5; border-color:#69a6d5; }
.slideshow .blue-bg .btn:hover, .slideshow .blue-bg .btn:focus { opacity:0.8; }
.slideshow .pink-bg { background-color:#e89db4; }
.slideshow .pink-bg .btn { color:#fff; background:#d8839d; border-color:#d8839d; }
.slideshow .pink-bg .btn:hover, .slideshow .pink-bg .btn:focus { opacity:0.8; }

.slideshow .bright-blue-bg { background-color:#3c92bd; }
.slideshow .bright-blue-bg .btn { color:#fff; background:#257096; border-color:#257096; }
.slideshow .bright-blue-bg .btn:hover, .slideshow .bright-blue-bg .btn:focus { opacity:0.8; }
.slideshow .lighter-pink-bg { background-color:#ea895f; }
.slideshow .lighter-pink-bg .btn { color:#fff; background:#d1683b; border-color:#d1683b; }
.slideshow .lighter-pink-bg .btn:hover, .slideshow .lighter-pink-bg .btn:focus { opacity:0.8; }

.mega-subtitle ul { list-style:none; padding:0; margin:15px 0; }
.mega-subtitle li { line-height:1.5; position:relative; padding-left:20px }
.mega-subtitle li:before { position:absolute; left:0; content:"\ea7f"; font-family:"annimex-icons"; font-size:14px; top:50%; margin-top:-10px }

/* Video Elemnets */
.video-sec { position:relative; font-size:0px; }
.video-text { position: absolute; }
.video-text .ttl { color: #fff; font-size: 40px; display: block; margin-bottom: 15px; }
.video-popup-content .mfpbox { padding:0; max-width:100%; -webkit-box-shadow:none; box-shadow:none; }
.video-popup-content .an { font-size:70px; border-radius:50%; opacity:.8; color:#fff; position:absolute; box-shadow:0 0 0 0 rgba(255,255,255,0.7); -webkit-animation:video-button 1.25s infinite cubic-bezier(0.66,0,0,1); -moz-animation:video-button 1.25s infinite cubic-bezier(0.66,0,0,1); animation:video-button 1.25s infinite cubic-bezier(0.66,0,0,1); }
.video-popup-content .an:hover { color:#5aa2de; }
@-webkit-keyframes video-button { to { box-shadow:0 0 0 45px rgba(255,255,255,0); }}
@-moz-keyframes video-button { to { box-shadow:0 0 0 45px rgba(255,255,255,0); }}
@keyframes video-button {to { box-shadow:0 0 0 45px rgba(255,255,255,0); }}

/* 9.2 Products With Tab Slider */
.tab-slider-product.section .section-header { margin-bottom:20px; }
.tab-slider-product .tabs { border:0; text-align:center; margin:0 0 30px; padding:0; }
.tab-slider-product .tabs > li { float:none; display:inline-block; margin:0 2px; cursor:pointer; }
.tab-slider-product .tabs > li { background:none; border:0; text-transform:none; color:#000; font-weight:400; font-size:14px; padding:5px 15px; border-radius:20px; }
.tab-slider-product .tabs > li.active { color:#fff; background:#000; }
.tab-slider-product .tabs li:hover, .tab-slider-product .tabs li:focus { color:#fff; opacity:1; background:#000; }
.tab-slider-product .tab_container { clear:both; width:100%; background:#fff; }
.tab-slider-product .tab_content { display:none; }
.tab-slider-product .tab_drawer_heading { display:none; }
.tab_container .grid-products .slick-arrow { width:30px; }

.tabs-style2.tabs > li { color:#555; font-size:13px; margin:0 12px; position:relative; padding:0; font-weight:600; }
.tabs-style2.tabs > li:after { width:0; content:""; display:block; height:2px; position:absolute; bottom:-6px; left:0; z-index:1; background:#222; transition:all .3s ease-in-out; -webkit-transition:all .3s ease-in-out; -ms-transition:all .3s ease-in-out; }
.tabs-style2.tabs > li.active:after { width:100%; }
.tabs-style2.tabs > li:hover:after { width:100%; opacity:1; }
.tabs-style2.tabs > li.active,
.tabs-style2.tabs > li:hover { color:#000; background-color:transparent; }

.tabs-style3.tabs > li { font-size:16px; border:1px solid transparent; padding:5px 20px; }
.tabs-style3.tabs > li.active { color:#333; border:1px solid #333; background:#fff;  }
.tabs-style3.tabs li:hover, .tab-slider-product .tabs li:focus { color:#fff; opacity:1; background:#000; }

.tab-slider-product-style2 .tabs > li { color:#848484; font-size:14px; font-weight:bold; padding:5px 10px; }
.tab-slider-product-style2 .tabs > li.active { color:#000; background:transparent; }
.tab-slider-product-style2 .tabs > li:hover { color:#000; background-color:transparent; }

/* 9.3 Hero/Parallax Banners */
.background-parallax { background-attachment: fixed !important; }
.parallax-banner-style1 .hero--exlarge, .bgFixed { background-attachment:fixed !important; }
.parallax-banner-style3 .hero .hero__inner .wrap-text { position:relative; background-color:rgba(255,255,255,0.7); padding:40px; }
.parallax-banner-style4 .hero .hero__inner .wrap-text { position: relative; padding: 40px; background-color:rgba(255,255,255,0.9); max-width: 395px; }
.parallax-banner-style4 .hero .text-small .mega-subtitle { color: #444; font-size: 20px; font-weight: 600; letter-spacing: 0.09em; }
.parallax-banner-style4 .hero .text-small .mega-title { color: #444; font-size: 31px; letter-spacing: 0.00em; line-height: 1.2; font-weight: 600; margin-bottom: 20px; }
.parallax-banner-style4 .hero .saleTime span > .count-inner { height: 66px; width: 66px; border-radius: 3px; font-size: 13px; color: #fff; background: #444; border: 1px dashed #444; }
.parallax-banner-style4 .hero .hero__inner span { margin-bottom: 0; }
.parallax-banner-style4 .hero .saleTime { margin: 0 0 20px; }
.parallax-banner-style4 .hero .saleTime span > span span { color: #fff; }
.parallax-banner-style4 .hero .hero__inner .details { color: #444; font-size: 14px; margin: 0 0 20px; }

.parallax-banner-style5 .hero .mega-subtitle { color: #fe254a; font-size: 16px; font-weight: 700; letter-spacing: 0.08em; }
.parallax-banner-style5 .hero .mega-title { font-size: 47px; font-weight: 600; line-height: 1.4; letter-spacing: 0.07em; margin-bottom: 20px; }
.parallax-banner-style5 .hero .saleTime span > .count-inner { height: 70px; width: 70px; color: #090a0a; background: #fafafa; border: 1px dashed #b7b6b6; border-radius: 0; }
.parallax-banner-style5 .hero .saleTime span > span span { color: #090a0a; }
.parallax-banner-style5 .hero .hero__inner .details { font-size: 17px; line-height: 1.5; margin: 15px 0 30px; }

.hero { position:relative; height:475px; display:table; width:100%; background-size:cover; background-repeat:no-repeat; background-position:50% 50%; background-attachment:scroll; }
.hero__inner { position:relative; display:table-cell; vertical-align:middle; padding:35px 0; color:#000; z-index:2; text-align:center; }
.hero[data-stellar-background-ratio] { background-attachment:fixed; }
.hero .text-small .mega-title { font-size:35px; font-weight:700; }
.hero .text-small .mega-subtitle { font-size:15px; font-weight:normal; margin-bottom:20px; }
.hero .hero__inner span { font-size:16px; text-transform:uppercase; letter-spacing:0; margin-bottom:10px; display:inline-block; }
.hero .hero__inner .details { font-size:18px; margin-bottom:25px; }

.hero .saleTime { position:static; }
.hero .saleTime span > .count-inner { color:#2a7467; background:#cde0c0; border:1px dashed #cde0c0; height:75px; width:75px; max-width:inherit;min-width:inherit; border-radius:50%; padding:4px 4px; margin:0 4px; line-height:normal; text-align:center; display:flex;justify-content:center;flex-direction:column; }
.hero .saleTime span > span span { background:none; color:#2a7467; max-width:inherit;min-width:inherit; font-size:13px; font-weight:600; text-transform:uppercase; line-height:normal;padding:0;margin:0; }
.hero .saleTime .time-count { font-size:18px; margin:0;padding:0; }
@media only screen and (min-width:990px) {
    .hero__inner .wrap-text { max-width:500px; }
}

.hero--xlarge { height:800px; }
.hero--large { height:600px; }
.hero--exlarge { height:900px; }
.hero--medium { height:450px; }
.hero--small { height:380px; }
.hero .text-large .mega-title { font-size:50px; }
.hero__inner .center { text-align:center; margin:0 auto; }
.hero .hero__inner .wrap-text.left { float:left; }
.hero .hero__inner .wrap-text.right { float:right; }
.hero .text-large .mega-subtitle { font-size:23px; }
.hero .mega-subtitle { margin-bottom:25px; }
.hero .font-bold .mega-title { font-weight:700; }
.hero__inner .right { float:right; text-align:center; }

.hero .text-medium .mega-title { font-size:35px; }
.hero .text-medium .mega-subtitle { font-size:18px; }
@media (min-width:767px) {
    .hero .hero__inner .wrap-text { max-width:540px; padding:20px; margin:0 auto; }
    .medical-demo .hero .hero__inner .wrap-text { max-width:400px; }
}
.hero .hero__inner .wrap-text:before { position:absolute; height:100%; width:100%; content: ""; left:0px; top:0px; z-index:-1; }
.hero .hero__inner .wrap-text.topleft { position:absolute; left:5%; top:10%; }
.hero .hero__inner .wrap-text.bottomleft { position:absolute; left:5%; bottom:10%; }
.hero .hero__inner .wrap-text.bottomright { position:absolute; right:5%; bottom:10%; }

/* 9.4 Featured Content Section */
.featured-content .featured-text.right { padding-left:60px; }
.featured-content .featured-text h2 { font-size:30px; font-weight:bold; letter-spacing:-0.5px; text-transform:none; }
.featured-content .featured-text p { font-size:16px; margin-bottom:20px; }
.featured-content .featured-content-bg { margin-top:60px !important; }
.featured-content .featured-content-bg .display-table-cell { padding:0; }
.featured-content .featured-content-bg .display-table-cell:first-of-type { background-color:#f4f4f4; text-align:center; padding:60px; }
.f-image { width:100%; display:block; }

.featured-content.style1 .f-text { color:#fff; padding:15px 45px; margin:0 auto; max-width:90%; width:100%; }
.featured-content.style1 .blue-bg { background-color:#609fef; }
.featured-content.style1 .pinch-bg { background-color:#fd6d61; }
.featured-content.style1 .gold-bg { background-color:#d4ae7a; }
.featured-content.style1 .light-blue-bg { background-color:#6878be; }
.featured-content.style1 .light-pinch-bg { background-color:#f09984; }
.featured-content.style1 .f-item:not(.white-bg) h3 { color:#fff; }
.featured-content.style1 .f-item:not(.white-bg) .btn { background-color:#0000004d; border-color:#0000; color:#fff; font-weight:600; }
.featured-content.style1 .f-item:not(.white-bg) .btn:hover { background-color:#0000004d; border-color:#0000; color:#fff; opacity:0.8; }
.featured-content.style1 .green-bg { background-color:#5dcac4; }
.featured-content.style1 .white-bg { color:#000; background-color:#fff; font-size:15px; }
.featured-content.style1 .white-bg .f-text { color:#222; }
.featured-content.style1 .deal-text { font-size:13px; }
.featured-content.style1 .product-review .an { font-size: 14px; }

/* 9.5 Newletter Section */
.section.newsletter-section { background:url(../images/parallax/newsletter-bg.jpg) no-repeat center center/cover; padding:75px 0; background-attachment:fixed; background-color:rgb(245,245,245); }
.newsletter-section .section-header { margin-bottom:20px; color:#fff; }
.newsletter-section .section-header h2 { color:#fff; }
.newsletter-section .newsletter-form .newsletter-input { border:0; background-color:#fff; }
.newsletter-form .input-group__field, .newsletter-form .input-group__btn { display:table-cell; vertical-align:middle; margin:0; }
.newsletter-form .input-group__btn { white-space:nowrap; width:1%; }

/* 9.6 Testimonial Slider */
.testimonial-slider.style1 { background:url(../images/testimonial-bg.jpg) no-repeat; background-size:cover; }
.quote-wraper .slick-list { margin:0 -10px; }
.quote-wraper .slick-slide { margin:0 10px; }
.quote-wraper .quotes-slide { background-color:#fff; padding:30px; }
.quote-wraper .testimonial-image { text-align:center; margin-bottom:20px; }
.quote-wraper .authour { margin-bottom:0; }
.quote-wraper .testimonial-image img { margin:0 auto; max-height:100px; max-width:100px; border-radius:100%; }
.quote-wraper blockquote { border:0; max-width:700px; margin:0 auto; line-height:1.7; }
.quotes-slider__text { color:#666; font-size:14px; line-height:1.7; font-weight:400; font-style:normal; padding:0; }
.quote-wraper .authour { color:#666; font-size:13px; font-weight:700; letter-spacing:1px; line-height:18px; }
.quotes-slider__text p { margin-bottom:10px; }
.quotes-slider__text .cmp-name { margin-bottom: 0; }
.quote-wraper .product-review { color:rgba(210, 135, 108, 0.8); margin-bottom:10px; }
.quote-wraper .slick-arrow { margin-top:-10px; height:35px; line-height:34px; width:35px; text-align:center; font-size:0px; padding:0; opacity:0; visibility:hidden; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.quote-wraper .slick-arrow:before { line-height:35px; }
.quote-wraper:hover .slick-arrow { opacity:1; visibility:visible; }
.quote-wraper .slick-prev { left:-10px; }
.quote-wraper .slick-next { right:-10px; }
.quote-wraper .slick-dots { margin-top:20px; }

/*
.testimonial-style2 { background-color:#f7f7f7; }
.testimonial-style2 .section-header { margin:0; }
.quotes-slider-style2 .slick-list, .quotes-slider-style2 .slick-slide { margin:0; }
.quotes-slider-style2 blockquote { font-size:16px; line-height:30px; border:0; max-width:870px; margin:0 auto; }
.quotes-slider-style2 .quotes-slide { padding:20px; }
.quotes-slider-style2 .rte-setting:before { font-size:60px; }
.quotes-slider-style2 .slick-dots { margin:0; }
.quotes-slider-style2 .slick-prev { left:30px; }
.quotes-slider-style2 .slick-next { right:30px; }

 */

.quotes-slider-1item .quotes-slider__text { font-size: 16px; color: #444; }
.quotes-slider-1item .product-review { color: rgba(242,157,25,0.8); display: flex; justify-content: center; align-items: center; }
.quotes-slider-1item .product-review .an { font-size: 14px; margin: 0 2px; }
.quotes-slider-1item .authour { font-size: 15px; font-weight: 600; color: #444; }

/* 9.7 Info/Simple Text Section */
.section.info-section { padding:15px; }
.info-section { color:#fff; font-size:16px; background-color:#222; padding:20px 10px; }
.info-section a { color:#fff; letter-spacing:0.5px; }
.info-section a:hover { color:#f06543 }
.simple-text-section { font-size:22px; max-width:950px; margin:0 auto; font-weight:400; }

/* 9.8 Instagram Section */
/*
.instagram-slider { margin-bottom:-7px; }
.instagram-section .slick-prev, .instagram-section .slick-next { background-color:rgba(255,255,255,0.6); opacity:0; visibility:hidden; }
.instagram-section:hover .slick-prev, .instagram-section:hover .slick-next { opacity:1; visibility:visible; }
.instagram-section .instagram-item, .instagram-grid .instagram-item { padding:0; }
.instagram-section .instagram-item a, .instagram-grid .instagram-item a { position:relative; height:100%; display:block; overflow:hidden; transition:all 0.4s ease; -webkit-transition:all 0.4s ease; -ms-transition:all 0.4s ease; }
.instagram-section .instagram-item a img, .instagram-grid .instagram-item a img { width:100%; height:100%; -o-object-fit:cover; object-fit:cover; transition:800ms ease 0s; -ms-transition:800ms ease 0s; -webkit-transition:800ms ease 0s; opacity:1 !important; }
.instagram-section .instagram-item .ins-icon, .instagram-grid .instagram-item .ins-icon { font-size:24px; line-height:1; color:#fff; position:absolute; top:50%; left:50%; transform:translate(-50%, -50%) scale(0); -webkit-transform:translate(-50%, -50%) scale(0); -ms-transform:translate(-50%, -50%) scale(0); transition:transform .3s ease; -webkit-transition:transform .3s ease; -ms-transition:transform .3s ease; width:100%; height:100%; display:inline-flex; justify-content:center; -webkit-justify-content:center; -ms-justify-content:center; align-items:center; -webkit-align-items:center; -ms-align-items:center; }
.instagram-grid .instagram-item .ins-icon .icon, .instagram-section .instagram-item .ins-icon .icon { font-size:24px; }
.instagram-section .instagram-item:hover .ins-icon, .instagram-grid .instagram-item:hover .ins-icon { opacity:1; transform:translate(-50%, -50%) scale(1); -webkit-transform:translate(-50%, -50%) scale(1); -ms-transform:translate(-50%, -50%) scale(1); z-index:3; }
.instagram-section .instagram-item:hover a:before, .instagram-grid .instagram-item:hover a:before { content: ''; background-color:rgba(0, 0, 0, 0.5); content: ''; position:absolute; top:0; left:0; z-index:2; width:100%; height:100%; }
.instagram-section .instagram-item:hover img, .instagram-grid .instagram-item:hover img { transform:scale(1.1, 1.1); -webkit-transform:scale(1.1, 1.1); -ms-transform:scale(1.1, 1.1); }

.instagram-grid ul { list-style:none; padding:0; margin:0; }
.instagram-grid ul > li { list-style:none; padding:0; margin:0 0 10px; }
.instagram-grid .instagram-item { width:16%; float:left; }

.instagram-grid-style2.instagram-grid ul { margin: -5px; }
.instagram-grid-style2.instagram-grid ul > li { margin:0; padding:5px; }
.instagram-grid-style2 .instagram-item { width:20%; float:left; padding:0; }
.instagram-grid-style3 .instagram-item { width:16.66667%; padding:0; margin:0; }

 */

/* 9.9 Miniproduct List Section */
.mini-product .column-ttl { font-size:16px; margin:0 0 25px; }
.mini-product .mini-list-item .grid-view-item__link, 
.mini-product .mini-list-item .mini-view_image img { width: 80px; }
.mini-product .mini-list-item { border: 1px solid #e7e7e7; padding-right: 10px; align-items: center; margin-bottom: 15px; -webkit-transition: all .3s ease-in-out; transition: all .3s ease-in-out; }
.mini-product .mini-list-item:hover { box-shadow: 0 0 5px #e7e7e7; }
.mini-list-item .grid-view-item__link,
.mini-list-item .mini-view_image img { width:70px; display:block; }

/* 9.10 Collection Slider */
.collection-slider .collection-grid-item { margin:0 10px; position:relative; text-align:center; overflow:hidden; width:auto !important; }
.collection-slider .collection-grid-item .img { position:relative; overflow:hidden; }
.collection-slider .collection-grid-item .img img { display:block; width:100%; -ms-transition:all 0.4s ease-out; -webkit-transition:all 0.4s ease-out; transition:all 0.4s ease-out; }
.collection-slider .collection-grid-item:hover .img img { -ms-transform:scale(1.1); -webkit-transform:scale(1.1);transform:scale(1.1); }
.collection-slider .slick-arrow { opacity:0; visibility:hidden; width:30px; height:30px; }
.collection-slider:hover .slick-arrow { color:#000; opacity:1; visibility:visible; }
.collection-slider .collection-grid .slick-prev { left:10px; }
.collection-slider .collection-grid .slick-next{ right:10px; }

.collection-slider-full { background-color:#fafafa; }
.collection-slider-full .collection-grid-slider { padding:0; }
.collection-slider-full .slick-prev { left:-20px; }
.collection-slider-full .slick-next{ right:-20px; }
.collection-slider-full .collection-grid-slider .collection-item { position:relative; }
.collection-slider-full .collection-grid-slider .details { padding:30px 10px; width:auto; }
.collection-slider-full .collection-grid-slider .details:before { display:none; }
.collection-slider-full .collection-grid-slider .details .collection-item-title { font-family:'Retevis-Regular',Helvetica,Arial,Tahoma,sans-serif; color:#333; font-weight:500; }
.collection-slider-full .collection-item .img { border-radius:100%; }

.collection-slider-full .collection-slider-4items.caption-on-image .slick-slide { padding:15px; }
.collection-slider-full .collection-slider-4items.caption-on-image .collection-item { position: relative; }
.collection-slider-full .collection-slider-4items.caption-on-image .details { color:#000; position:absolute; bottom:30px; left:0; right:0; width:auto; text-align:center; margin:0 auto; }
.collection-slider-full .collection-slider-4items.caption-on-image .details.wd-70 { width:70%; }
.collection-slider-full .collection-slider-4items.caption-on-image .details .collection-item-title { color:#333; margin-bottom:5px; }
.collection-slider-full .collection-slider-4items.caption-on-image .details .inner { background:#fff; padding: 20px; }
.collection-slider-full .collection-slider-4items.caption-on-image .collection-item .img { position: relative; overflow: hidden; }
.collection-slider-full .collection-slider-4items.caption-on-image .collection-item .img img { display:block; width:100%; -ms-transition:all 0.4s ease-out; -webkit-transition:all 0.4s ease-out; transition:all 0.4s ease-out; }
.collection-slider-full .collection-slider-4items.caption-on-image .collection-item:hover .img img { -ms-transform:scale(1.1); -webkit-transform:scale(1.1);transform:scale(1.1); }

/* 9.11 Brands Logo Slider */
.logo-bar .slick-list { margin:0 -10px; }
.logo-bar .slick-slide { padding:0 10px; }
.logo-bar a { display:block; border:1px solid #e7e7e7; padding:10px; }
.logo-bar a:hover { border-color: #ddd; }
.logo-bar__item:hover { opacity:0.9; }
.logo-bar .slick-arrow { -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.logo-bar:hover .slick-arrow { color:#000; opacity:1; }
.logo-bar .slick-prev { left:-35px; }
.logo-bar .slick-next { right:-35px; }
.logo-bar .slick-slide img { margin:0 auto; }
.logo-bar .slick-prev, .logo-bar .slick-next { text-align:center; font-size:0px; height:30px; width:30px; line-height:29px; border-radius:50%; opacity: 1 !important; }

/* 9.12 Home Blog Post */
.home-blog-post { padding:50px 0; }
.home-blog-post-style1 { background-color:#fff; }
.blogpost-item { margin:0 0 1px; }
.blogpost-item .post-thumb { display:block; margin-bottom:20px; position:relative; overflow:hidden; }
.blogpost-item .post-thumb img { display:block; width:100%; -ms-transition:all 0.4s ease-out; -webkit-transition:all 0.4s ease-out; transition:all 0.4s ease-out; }
.blogpost-item:hover .post-thumb img { -ms-transform:scale(1.1); -webkit-transform:scale(1.1);transform:scale(1.1); }
.blogpost-item .article-excerpt { font-size:14px; margin-top:10px; }
.blogpost-item .publish-detail { list-style:none; padding:0; margin:0; }
.blogpost-item .publish-detail li { display:inline-block; padding:0; }
.blogpost-item .btn-small { background:none; border:0; padding:0; border-radius:0; border-bottom:1px solid #222; }
.blogpost-item .btn-small:hover { border-bottom:1px solid transparent; text-decoration:none; }
.blogpost-item .post-detail .excerpt { margin:15px 0; }

.home-blog-post .slick-arrow { opacity:0; visibility:hidden; width:30px; height:30px; }
.home-blog-post:hover .slick-arrow { color:#000; opacity:1; visibility:visible; }
.home-blog-post .slick-list { margin-right:-10px; margin-left:-10px; }
.home-blog-post .slick-slide { margin-right:10px; margin-left:10px; }
.home-blog-post .slick-prev { left:-30px; }
.home-blog-post .slick-next{ right:-30px; }

/*
.home-blog-post-style1 .blogpost-item .post-detail .post-title { font-weight:normal; font-size:15px; }
.home-blog-post-style1 .blogpost-item .post-thumb { margin-bottom:20px; }
.home-blog-post-style1 .blogpost-item .publish-detail { font-size:13px; }
.home-blog-post-style1 .blogpost-item .publish-detail li { padding:0; }

 */
.post-content h2 { margin-bottom: 10px; }

/* 9.13 Store Features */
.store-features { background-color:#fed925; padding:30px 0; margin-top: 50px; }
.store-features a:hover {text-decoration:none;}
.store-info .an { float:left; margin:0 15px 0 0; font-size:40px; vertical-align:middle; display:block; width:50px; text-align:center; }

/*
.store-features.style1 .col i { background:#e7e7e7; width:75px; height:75px; line-height:72px; }
.store-features.style1 .col:hover i { background:#f2f2f2; }
.store-features.style1 .store-info .an { float:none; margin:0 auto; }
.store-features.style2 { background-color:#f9f9f9; margin-top:-5px; padding:18px 0; }
.store-features.style2 i { width:auto; color:#5aa2de; font-size:28px; }
.store-features.style3 { background-color:#92c6ee; }
.store-features.style3 .detail { font-size:16px; margin:20px 0 0; }
.store-features.style4 .store-info .an { margin:0 0 16px 0; color:#7e7979; font-size:42px; }
.store-features.style4 .detail { font-size:13px; }

 */
.store-features.small-icon .store-info .an { width: 30px; font-size: 20px; }

/* 9.14 Custom Content */
.custom-content-section .col-lg-6 { margin-bottom:30px; }
.custom-content-section .section-header { margin-bottom:10px; }
.custom-content-section .custom-details { text-align:center; max-width:80%; margin:0 auto; } 
.custom-content-section .custom-details p { margin-bottom:20px; }

/* 9.15 Instagram Shop */
.instagram-shop { margin-top:20px; }
.instagram-shop .row { margin-left:-8px; margin-right:-8px; }
.instagram-shop .row .col-6 { padding:0 8px; margin-bottom:16px; }
.instagram-shop .insta-item img { display:block; -ms-transition:all ease-out 0.5s; -webkit-transition:all ease-out 0.5s; transition:all ease-out 0.5s; }
.instagram-shop .insta-item { position:relative; display:block; overflow:hidden; }
.instagram-shop .insta-item:hover img { transform:scale(1.1); -webkit-transform:scale(1.1); -ms-transform:scale(1.1); }
.instagram-shop .insta-item:after { content:'\e931'; font-family:'annimex-icons'; display:inline-block; position:absolute; right:10px; bottom:10px; font-size:15px; background-color:rgba(0,0,0,0.18); border-radius:100px; -webkit-border-radius:100px; width:30px; height:30px; line-height:30px; text-align:center; }
.instagram_gallery img { padding:5px; }

/* 9.16 Promotion Product Popup */
.product-notification { display:block; width:270px; padding:10px; background-color:#fff; -webkit-box-shadow:0px 0 7px 2px rgba(158,158,158,0.2); box-shadow:0px 0 7px 2px rgba(158,158,158,0.2); margin:10px; position:fixed; bottom:0; -webkit-animation:movebottom 15s infinite; animation:movebottom 15s infinite; z-index:999; }
.product-notification img { height:70px; }
.product-notification h5 { color:#666; font-size:10px; }
.product-notification .pname { font-size:12px; font-weight:600; margin-bottom:5px; }
.product-notification .detail { font-size:11px; line-height:1.2; margin:0; color:#666; }
.product-notification .media-body { padding-left:10px; }
.product-notification p { margin:0 0 10px; }
.product-notification .close { font-size:13px !important; cursor:pointer; position:absolute; right:7px; top:6px; z-index:99; }
@-webkit-keyframes movebottom { 0% { display:block; bottom:-200px; } 25% { bottom:0px; } 75% { bottom:0px; } 100% { display:none; bottom:-200px; } }
@keyframes movebottom { 0% { display:block; bottom:-200px; } 25% { bottom:0px; } 75% { bottom:0px; } 100% { display:none; bottom:-200px; } }

/*======================================================================
  10. Collection Banner
========================================================================*/
.collection-banners { margin-top:20px; }
.collection-banners .row { margin-left:-10px; margin-right:-10px; }
.collection-banners .row .banner-item { padding-left:10px; padding-right:10px; }
.collection-banners .collection-grid-item { position:relative; overflow:hidden; }
.collection-banners .collection-grid-item img { display:block; width:100%; -ms-transition:all ease-out 0.5s; -webkit-transition:all ease-out 0.5s; transition:all ease-out 0.5s; }
.collection-banners .collection-grid-item:hover img { transform:scale(1.1); -webkit-transform:scale(1.1); -ms-transform:scale(1.1); }
.collection-banners .details { background-color:#fff; color:#000; width:50%; left:50%; -ms-transform:translateX(-50%); -webkit-transform:translateX(-50%); transform:translateX(-50%); position:absolute; bottom:20px; right:0; padding:20px; text-align:center; }
.collection-banners .details .title { margin:0; font-size:16px; }
.collection-banners .details .btn { font-size:12px; padding:5px 15px; margin-top:10px; }

.banner-item:after, .collection-page-item:after { content:''; display:block; clear:both; }
.banner-item, .collection-page-item { float:left; margin-bottom:20px; }
.banner-item img, .collection-page-item img { display:block; max-width:100%; }
.grid-sizer, .banner-item, .collection-page-item { max-width:50%; }

@media only screen and (min-width:1200px) {
    .grid-sizer.grid-5col, .grid-categorys .grid-5col .cl-item { width:20%; }
    .grid-sizer.grid-7col, .grid-categorys .grid-7col .cl-item { width:14.28571%; }
}

.collection-grid-item__title { font-size:20px; font-weight:600; text-transform:none; margin:0; display:block; }
.collection-grid-item .counts { color:#333; font-size:13px; opacity:0.7; display:block; margin-top:7px; }
.collection-grid-item .details { position:relative; display:flex; flex-direction:column; padding:15px 15px 15px; width:100%; -webkit-transition:all .3s ease-out; transition:all .3s ease-out; }

.collection-grid-item .details { display:block; height:100%; position:absolute; top:0; opacity:0; z-index:1; text-align:center; }
.collection-grid-item .details:before { position:absolute; top:0; left:0; content:""; width:100%; height:100%; background:#000; z-index:-1; }
.collection-grid-item .details.middle { left:0; right:0; margin:0 auto; top:50% !important; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); }
.collection-grid-item .details.top { left:0; right:0; margin:0 auto; top:10px !important; bottom:auto; -ms-transform:none; -webkit-transform:none; transform:none; }
.collection-grid-item .details.bottom { left:0; right:0; margin:0 auto; top: auto; bottom:10px !important; -ms-transform:none; -webkit-transform:none; transform:none; height: auto; padding: 0; }
.collection-grid-item .details .inner { position:relative; top:50%; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); padding:0; }

/*
.collection-banners.style0 .collection-grid-item .details { display:-ms-flexbox; display:flex; -ms-flex-align:center; align-items:center; -webkit-justify-content:center; -ms-justify-content:center;justify-content:center; height:100%; position:absolute; top:0; opacity:0; z-index:1; padding:15px; width:100%; text-align:center; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.collection-banners.style0 .collection-grid-item .details:before { position:absolute; top:0; left:0; content:""; width:100%; height:100%; background:#000; z-index:-1; }
.collection-banners.style0 .collection-grid-item:hover .details { opacity:0.8; }
.collection-banners.style0 .collection-grid-item .details .inner { position:static; }

.collection-banners.style1 .collection-grid-item a { display:block; }
.collection-banners.style1 .collection-grid-item a:before { content:''; position:absolute; left:0; right:0; top:0; bottom:0; width:100%; height:100%; z-index:1; }
.collection-banners.style1 .collection-grid-item .overlay { display:block; width:100%; height:100%; opacity:0; visibility:hidden; position:absolute; top:0; left:0; z-index:4; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.collection-banners.style1 .collection-grid-item:hover .overlay { visibility:visible; opacity:1; }
.collection-banners.style1 .collection-grid-item .details { opacity:1; visibility:visible; height:auto; top:auto; width:80%; }
.collection-banners.style1 .collection-grid-item .details.bottom-right { bottom:20px; right:20px; left:auto; text-align:left; width:auto; transform:none; -webkit-transform:none; -ms-transform:none; }
.collection-banners.style1 .collection-grid-item .details.bottom-left { bottom:20px; left:20px; right:auto; text-align:left; width:auto; transform:none; -webkit-transform:none; -ms-transform:none; }
.collection-banners.style1 .collection-grid-item .title { color:#4e6275; font-size:24px; font-weight:bold; }
.collection-banners.style1 .collection-grid-item .title.large-title { font-size:60px; }
.collection-banners.style1 .collection-grid-item .white-text .btn--link { color:#fff; font-size:14px; border-bottom:2px solid #fff; display:inline-block; padding:0 0 5px 0; }
.collection-banners.style1 .collection-grid-item .white-text .btn--link:hover { opacity:0.8; }
.collection-banners.style1 .collection-grid-item .white-text,
.collection-banners.style1 .collection-grid-item .white-text .title { color:#fff; background-color:transparent; }
.collection-banners.style1 .collection-grid-item .details.transparent { background-color:transparent; }
.collection-banners.style1 .collection-grid-item .details:before { display:none; }
.collection-banners.style1 .collection-grid-item .details .inner { position:static; transform:none; -ms-transform:none; -webkit-transform:none; }
.collection-banners.style1 .collection-banners .details p { margin-top:10px; }

.collection-banners.style2 .collection-grid-item .details { text-align:center; position:absolute; top:inherit; bottom:0; left:0; height:auto; -ms-transform:translateY(100%); -webkit-transform:translateY(100%); transform:translateY(100%); }
.collection-banners.style2 .collection-grid-item:hover .details { -ms-transform:translateY(0); -webkit-transform:translateY(0); transform:translateY(0); }
.collection-banners.style2 .collection-grid-item__title, .collection-banners.style3 .collection-grid-item__title { font-size:15px; }

.collection-banners.style3 .collection-page-item { margin-bottom:30px; }
.collection-banners.style3 .collection-grid-item .details { color:#000; position:relative; opacity:1; visibility:visible; background:#f2f2f2; }
.collection-banners.style3 .collection-grid-item .details:before, .collection-banners.style4 .collection-grid-item .details:before,
.collection-banners.style5 .collection-grid-item .details:before, .collection-banners.style6 .collection-grid-item .details:before { display:none; }
.collection-banners.style3 .collection-grid-item .collection-grid-item__title, .collection-banners.style3 .collection-grid-item .counts { color:#000; }

.collection-banners.style4 .collection-grid-item .details,
.collection-banners.style5 .collection-grid-item .details,
.collection-banners.style6 .collection-grid-item .details { padding:10px; width:auto; height:auto; top:auto; bottom:15px; left:15px; right:15px; opacity:1; visibility:visible; background:rgba(255,255,255,0.8); -ms-transform:translateX(0); -webkit-transform:translateX(0); transform:translateX(0); }
.collection-banners.style4 .collection-grid-item__title,
.collection-banners.style5 .collection-grid-item__title,
.collection-banners.style6 .collection-grid-item__title { color:#000; font-size:15px; }
.collection-banners.style6 .collection-grid-item .details { background:#000; position:static; }
.collection-banners.style6 .collection-grid-item__title { color:#fff; }

.collection-banners.style7 .collection-grid-item .details { opacity:1; visibility:visible; bottom: 30px !important; background-color: transparent; }
.collection-banners.style7 .collection-grid-item .details:before { opacity:0; }
.collection-banners.style7 .collection-grid-item .details .inner { color: #000; background: #fff; display: inline-block; padding: 12px 20px; -ms-transform: none; -webkit-transform: none; transform: none; }
.collection-banners.style7 .collection-grid-item .details .title { font-size: 14px; font-weight: 600; }
.collection-banners.style7 .collection-grid-item .details .btn--link { font-size: 13px; color: #fff; border-bottom: 2px solid #fff; display: inline-block; padding: 0 0 5px 0; }

.collection-banners.style8 .collection-grid-item .details { opacity:1; visibility:visible; background-color: transparent; width: auto; height: auto; top: auto !important; bottom: auto !important; left: auto; right: auto; -ms-transform: none; -webkit-transform: none; transform: none; }
.collection-banners.style8 .collection-grid-item .details:before { opacity:0; }
.collection-banners.style8 .collection-grid-item .details .inner { font-size: 15px; letter-spacing: normal; padding: 0; top: auto; -ms-transform: none; -webkit-transform: none; transform: none; }
.collection-banners.style8 .collection-grid-item .btn--link { color:#fff; font-size:12px; border-bottom:2px solid #fff; display:inline-block; padding:0 0 5px 0; }
.collection-banners.style8 .collection-grid-item .black-link .btn--link { color: #444; border-color: #444; }
.collection-banners.style8 .details.center-left { text-align: left; top: 50% !important; left: 25px; -ms-transform: translateY(-50%); -webkit-transform: translateY(-50%);  transform: translateY(-50%); }
.collection-banners.style8 .details.center-right { text-align: left; top: 50% !important; right: 20px; -ms-transform: translateY(-50%); -webkit-transform: translateY(-50%);  transform: translateY(-50%); }
.collection-banners.style8 .details.center-bottom { width: 100%; bottom: 20px !important; left: 50%; -ms-transform: translateX(-50%); -webkit-transform: translateX(-50%);  transform: translateX(-50%); }
.collection-banners.style8 .details.center-middle { top: 50% !important; left: 50%; -ms-transform: translate(-50%,-50%); -webkit-transform: translate(-50%,-50%); transform: translate(-50%,-50%); }
.collection-banners.style8 .details.top-left { width: 38%; text-align: left; top: 35px !important; left: 35px; }
.collection-banners.style8 .details.top-center { width: 60%; top: 20px !important; left: 50%; -ms-transform: translateX(-50%); -webkit-transform: translateX(-50%);  transform: translateX(-50%); }
.collection-banners.style8 .details.bottom-left { width: 60%; text-align: left; bottom: 20px !important; left: 20px; }
.collection-banners.style8 .details.bottom-right { width: 50%; text-align: left; bottom: 20px !important; right: 20px; }
.collection-banners.style8 .collection-grid-item.banner3 .details .title { font-size: 40px; }
.collection-banners.style8 .collection-grid-item.banner4 .details .title { font-size: 20px; }

 */

.category-6col-page .container-fluid, .category-7col-page .container-fluid { padding:0; }
.collection-banners.style5 .collection-grid-item__title { font-family:'Montserrat',sans-serif; }

/*======================================================================
  10.1 Collection Box Slider
========================================================================*/
.collection-box { background-color:#f2f2f2; }
.collection-grid-slider .slick-list { margin:0 -15px; }
.collection-grid-slider .slick-slide { margin:0 15px; }
.collection-grid-slider .collection-item .img { position:relative; overflow:hidden; }
.collection-grid-slider .collection-item .img img { display:block; width:100%; -ms-transition:all 0.4s ease-out; -webkit-transition:all 0.4s ease-out; transition:all 0.4s ease-out; }
.collection-grid-slider .collection-item:hover .img img { -ms-transform:scale(1.1); -webkit-transform:scale(1.1); transform:scale(1.1); }
.collection-grid-slider .details { z-index:1; position:relative; padding:15px; width:100%; text-align:center; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.collection-grid-slider .details:before { position:absolute; left:0; top:0; content: ""; width:100%; height:100%; background:#222; opacity:1; z-index:-1; }
.collection-grid-slider .details .collection-item-title { color:#fff; font-size:14px; margin:0; }
.collection-grid-slider .slick-arrow { background-color:transparent; opacity:0; visibility:hidden; }
.collection-grid-slider:hover .slick-arrow { opacity:1; visibility:visible; }

.collection-banner-grid { background-color:inherit; }
.collection-banner-grid .collection-item { margin-bottom:30px; }
.collection-banner-grid .details { background-color:rgba(88, 195, 141, 0.7); padding:15px; text-align:center; }
.collection-banner-grid .collection-item-title { font-size:15px; }

.collection-slider-4items .slick-list { margin-left:-5px; margin-right:-5px; }
.collection-slider-4items .slick-slide { padding:5px; }
.collection-slider-4items .slick-arrow { background-color:rgba(255,255,255,0.9); }
.collection-slider-4items .slick-prev { left:0; }
.collection-slider-4items .slick-next { right:0; }

.collection-slider.background-none .details .collection-item-title { font-size:20px; font-weight:700; color:#000; padding-top:10px; }
.collection-slider.background-none .details:before { display:none; }
.collection-slider.background-none .slick-prev { left: -20px; }
.collection-slider.background-none .slick-next { right: -20px; }

/*======================================================================
  10.2 Category Columns Pages
========================================================================*/
.shop-sub-collections .grid-categorys .category-item .details { padding-bottom: 0; }

/* Grid gaping */
.grid-mr-50 { margin:-25px; }
.grid-mr-50 .cl-item, .collection-banners .grid-mr-30 .cl-item { padding:25px; margin-bottom:0 !important; }
.grid-mr-30 { margin:-15px; }
.grid-mr-30 .cl-item, .collection-banners .grid-mr-30 .cl-item { padding:15px; margin-bottom:0 !important; }
.grid-mr-20 { margin:-10px; }
.grid-mr-20 .cl-item, .collection-banners .grid-mr-20 .row .banner-item { padding:10px; margin-bottom:0 !important; }
.grid-mr-15 { margin:-7.5px; }
.grid-mr-15 .cl-item, .collection-banners .grid-mr-15 .row .banner-item { padding:7.5px; margin-bottom:0 !important; }
.grid-mr-10 { margin:-5px; }
.grid-mr-10 .cl-item, .collection-banners .grid-mr-10 .row .banner-item { padding:5px; margin-bottom:0 !important; }
.grid-mr-5 { margin:-2.5px; }
.grid-mr-5 .cl-item, .collection-banners .grid-mr-5 .row .banner-item { padding:2.5px; margin-bottom:0 !important; }
.grid-mr-0 { margin:0px; }
.grid-mr-0 .cl-item, .collection-banners .grid-mr-0 .row .banner-item  { padding:0px 0px; margin-bottom:0 !important; }

.grid-categorys .category-item .category-title { font-size:20px; font-weight:600; text-transform:none; margin:0; display:block; }
.grid-categorys .category-item .counts { color:#333; font-size:13px; opacity:0.7; display:block; margin-top:7px; }
.grid-categorys .category-item .details { position:relative; display:flex; flex-direction:column; padding:15px 15px 15px; width:100%; -webkit-transition:all .3s ease-out; transition:all .3s ease-out; }

/*
.grid-categorys .style1 .category-grid-item .details { background:rgba(0,0,0,0.77); text-align:center; position:absolute; top:inherit; bottom:0; left:0; height:auto; -ms-transform:translateY(100%); -webkit-transform:translateY(100%); transform:translateY(100%); }
.grid-categorys .style1 .category-grid-item:hover .details { -ms-transform:translateY(0); -webkit-transform:translateY(0); transform:translateY(0); }
.grid-categorys .style1 .category-grid-item .category-title, 
.grid-categorys .style1 .category-grid-item .counts { color:#fff; }

.grid-categorys .style2 .category-grid-item .details { color:#333; background:#f2f2f2; justify-content:center; align-items:center; text-align:center; }
.grid-categorys .style2 .category-grid-item .category-title, 
.grid-categorys .style2 .category-grid-item .counts { color:#333; }
.grid-categorys .style2 .category-grid-item:hover .details { background:#f9f9f9; }

.grid-categorys .style3 .category-grid-item .details { background:rgba(255,255,255,0.88); padding:10px; width:auto; height:auto; justify-content:center; align-items:center; text-align:center; position:absolute; top:auto; bottom:10px; left:10px; right:10px; z-index:2; -ms-transform:translateX(0); -webkit-transform:translateX(0); transform:translateX(0); }
.grid-categorys .style3 .category-grid-item .category-title { color:#000; font-size:15px; }
.grid-categorys .style3 .category-grid-item:hover .details { background:#fff; }

.grid-categorys .style4 .category-grid-item .details { align-items:center; justify-content:center; text-align:center; padding:15px; height:100%; position:absolute; top:0; opacity:0; visibility:hidden; z-index:2; }
.grid-categorys .style4 .category-grid-item .details:before { content:""; position:absolute; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.77); z-index:-1; }
.grid-categorys .style4 .category-grid-item:hover .details { opacity:1; visibility:visible; }
.grid-categorys .style4 .category-grid-item .category-title, 
.grid-categorys .style4 .category-grid-item .counts { color:#fff; }

 */

.sub-collection.collection-slider-4items .slick-list { margin-left:0; margin-right:0; }
.sub-collection.collection-slider-4items .slick-slide { padding:0; }

/*======================================================================
  10.3 Image Banners
========================================================================*/
.imgBanners .inner * { -ms-transition:all 0.4s ease-in-out; -webkit-transition:all 0.4s ease-in-out; transition:all 0.4s ease-in-out; }
.imgBanners .row { margin-left:-10px; margin-right:-10px; }
.imgBanners .row .img-banner-item { padding-left:10px; padding-right:10px; }
.imgBanners .inner { position:relative; overflow:hidden; }
.imgBanners .inner img { display:block; width:100%; }
.imgBanners .imgBanner-grid-item { position:relative; overflow:hidden; }
.imgBanners .imgBanner-grid-item img { display:block; width:100%; -ms-transition:all ease-out 0.5s; -webkit-transition:all ease-out 0.5s; transition:all ease-out 0.5s; }
.imgBanners .imgBanner-grid-item:hover img { transform:scale(1.1); -webkit-transform:scale(1.1); -ms-transform:scale(1.1); }
.imgBanners .inner .ttl { background-color: rgba(255,255,255,0.8); color:#000; line-height:25px; font-size:17px; display:inline-block; padding:10px 20px; max-width:80%; position:absolute; z-index:1; }
.imgBanners .inner.btmleft .ttl { left:20px; bottom:20px; text-align:left; }
.imgBanners .inner.topleft .ttl { left:20px; top:20px; text-align:left; }
.imgBanners .inner.topright .ttl { right:20px; top:20px; text-align:right; }
.imgBanners .inner.center .ttl { left:0; right:0; bottom:20px; text-align:center; margin:0 auto; }
.imgBanners .img-banner-item.last .imgBanner-grid-item { margin-bottom:20px; }
.imgBanners .img-banner-item.last .imgBanner-grid-item + .imgBanner-grid-item { margin-bottom:0; }
.imgBanners .details { display:inline-block; position:absolute; z-index:1; padding:15px; width:auto; background-color:#fff; text-align:center; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.imgBanners .details .title { color:#000; margin-bottom:5px; }
.imgBanners .details p { margin-bottom:10px; }
.imgBanners .details .tt-small { display:block; }
.imgBanners .details .ttl { display:inline-block; }
.imgBanners .details.w-60 { width:60%; }
.imgBanners .details.w-70 { width:70%; }
.imgBanners .details.center { left:0; right:0; bottom:20px; text-align:center; margin:0 auto; }
.imgBanners .details.left { left:20px; right:auto; bottom:20px; text-align:left; margin:0 auto; }
.imgBanners .details.right { left:auto; right:20px; bottom:20px; text-align:right; margin:0 auto; }
.imgBanners .details.left-top { left:20px; right:auto; bottom:auto; top:30px; text-align:left; margin:0 auto; }

/*
.imgBanners.style2 { margin-top:10px; }
.imgBanners.style2 .row { margin-left:-5px; margin-right:-5px; }
.imgBanners.style2 .banner-item { padding-right:5px; }
.imgBanners.style2 .banner-item:nth-of-type(1) { padding-left:0; margin-bottom:0; }
.imgBanners.style2 .banner-item:nth-of-type(2) { padding-right:0; padding-left:5px; margin-bottom:0; }
.imgBanners.style2 .details .title { font-size:20px; margin:0 0 5px; }

.imgBanners.style3 .details { background-color:transparent; }
.imgBanners.style3 .details .title { color:#000; font-size:24px; margin:0; }
.imgBanners.style3 .details .tt-small { font-size:14px; margin-bottom:5px; }

.imgBanners.style4 .inner .img { position:relative; overflow:hidden; }
.imgBanners.style4 .details { position:static; text-align:center; width:100%; }
.imgBanners.style4 .details .title { font-size:26px; font-weight:normal; }
.imgBanners.style4 .imgBanner-grid-item:hover img { transform:none; -webkit-transform:none; -ms-transform:none; opacity:0.8; }
.imgBanners.style4 .details p { margin-bottom:15px; }

.imgBanners.style5 .details { padding:0; background-color:transparent; transform:none; -webkit-transform:none; -ms-transform:none; }
.imgBanners.style5 .details .ttl { margin:0; background:#fff; font-size:15px; display:inline-block; padding:10px 20px; }
.imgBanners.style5 .row + .row { margin-top:20px; }
.imgBanners.style5 .row + .row .col-12 { margin-bottom:20px; }

.imgBanners.style6 { margin-top:20px; }
.imgBanners.style6 .row .img-banner-item.last { margin-top:20px; }
.imgBanners.style6 .inner .ttl { font-size:15px; font-weight:bold; background-color:transparent; padding:5px 10px; }

.imgBanners.style7 .ttl { background-color:transparent; line-height:normal; }
.imgBanners.style7 .ttl .tt-small { font-size:14px; display:block; }
.imgBanners.style7 .ttl .tt-big { font-size:23px; line-height:35px; }

.imgBanners.style8 .img { position:relative; overflow:hidden; }
.imgBanners.style8 .details { position:static; width:100%; }

 */

.collection-banners.style2 .imgBanner-grid-item { position: relative; }
.collection-banners.style2 .imgBanner-grid-item .img { position:relative; overflow:hidden; }
.collection-banners.style2 .imgBanner-grid-item img { -ms-transition: all ease-out 0.4s; -webkit-transition: all ease-out 0.4s; transition: all ease-out 0.4s; }
.collection-banners.style2 .imgBanner-grid-item:hover img { transform:scale(1.1); -webkit-transform:scale(1.1); -ms-transform:scale(1.1); }
.collection-banners.style2 .imgBanner-grid-item .details { opacity: 0; visibility: hidden; position: absolute; top: 50%; left: 0; right: 0; text-align: center; margin:-30px auto; background:transparent; width: 100%; transform:none; -webkit-transform:none; -ms-transform:none; }
.collection-banners.style2 .imgBanner-grid-item:hover .details  { opacity: 1; visibility: visible; }

/*======================================================================
  11. Breadcrumbs
========================================================================*/
.breadcrumb-item a {color:#6c757d;}
.breadcrumbs-wrapper { background:#fff; }
.breadcrumbs-wrapper .container, .breadcrumbs-wrapper .container-fluid { padding-top:20px; padding-bottom:20px; }
.breadcrumbs a, .breadcrumbs span { color:#222; display:inline-block; padding:0 3px 0 0; margin-right:3px; font-size:12px; }

.category-text-wrapper {background-color: #f6f6f6;}
.category-title h1 { font-size:18px; font-weight:500; position:relative; margin:0 0 30px 0; }
.page.section-header { background:#f9f9f9; padding:20px 0; margin-bottom:30px; }

.cloud-tags-items a { color:#666; background-color:#fff; font-size:13px; display:inline-block; padding:2px 10px; margin:0 5px 5px 0; border:1px solid #e5e5e5; border-radius:4px; white-space:nowrap; cursor:pointer; }
.cloud-tags-items a:hover { border-color:#000; }
.cloud-tags-items a .an { font-size:13px; line-height: 1.7; margin-left:5px; }

.collection-hero { position:relative; overflow:hidden; }
.collection-hero__image { background-color:#fff; background-position:50% 50%; background-repeat:no-repeat; background-size:cover; height:60px; opacity:1; }
.collection-hero__title-wrapper { position:absolute; left:0; right:0; top:50%; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); text-align:left; }
.collection-hero__title-wrapper:before { position:absolute; top:0; right:0; bottom:0;left:0; background-color:#000;opacity:0; visibility:hidden; }
.collection-hero__title { font-size:18px; font-weight:700; color:#222; width:100%; text-align:left; text-transform:uppercase; margin:0; }
.collection-description { padding-top:10px; padding-bottom:10px; }

.collection-hero.large .collection-hero__image { height: 280px; }
.collection-hero.medium .collection-hero__image { height: 200px; }
.collection-hero .collection-hero__title.medium { font-size: 18px; }

/*======================================================================
  12. Section
========================================================================*/
.section { padding-top:50px; padding-bottom:50px; }
.section-header { position:relative; margin-bottom:35px; text-align:center; }
.section-header h2 { font-size:22px; font-weight:600; line-height:1.3; margin: 0 auto; }
.section-header p { margin:5px 0 0; display:block; }
.pb-section { padding-bottom:50px; }
.pt-section { padding-top:50px; }
.no-pb-section { padding-bottom:0 !important; }
.no-pt-section { padding-top:0 !important; }

.section-header.style2 h2 { font-size:25px; letter-spacing:.06em; }
.section-header.style3 { padding-bottom:15px; }
.section-header.style3:after { content:""; position:absolute; left:0; right:0; bottom:2px; margin:0 auto; height:1px; width:95%; max-width:200px; background-color:#444; }
.section-header.style3:before { content:""; position:absolute; left:0; right:0; bottom:0; margin:0 auto; height:5px; width:55px; background-color:#222; z-index: 1; }

@media only screen and (min-width: 992px) {  #page-content { min-height:600px; }  }

/*======================================================================
  13. Product Grid
========================================================================*/
#pro-addtocart-popup { text-align:center; background:#fff; margin:0 auto; padding:20px; max-width:425px; position:relative; }
#pro-addtocart-popup .mfp-close { opacity:1; position:absolute; top:0; right:0; background-color:#fff; color:#222; width:25px; height:25px; line-height:25px; font-size:18px; }
#pro-addtocart-popup .pro-img { max-width:400px; }
#pro-addtocart-popup .pro-name { font-weight:600; }
#pro-addtocart-popup .sku { color:#888; }
#pro-addtocart-popup .addcart-total { background-color:#f7f7f7; }

.slider-gp30.slick-slider .slick-list { margin:0 -10px; }
.slider-gp30.slick-slider .slick-slide { padding:0 10px; }

.productSlider.slick-slider .slick-list,
.productPageSlider.slick-slider .slick-list,
.productSlider-style2.slick-slider .slick-list { margin:0 -10px; }   

.productSlider.slick-slider .slick-slide,
.productPageSlider.slick-slider .slick-slide,
.productSlider-style2.slick-slider .slick-slide { margin:0 10px; }

.grid-products a { text-decoration:none !important; }
@media only screen and (min-width:1305px) {
    .shop-grid-5 .grid-products .item.col-xxl-2,
    .shop-grid-5 .item.col-xxl-2 { -ms-flex:0 0 20%; -webkit-flex:0 0 20%; flex:0 0 20%; width:20%; }
}
.grid-products .item .product-image .showVariantImg img { opacity:0; visibility:hidden; }
.grid-products .item .product-image .showVariantImg .variantImg { visibility:visible; opacity:1; }
.grid-products .item .product-image .showLoading { transition: .5s; animation: loader-rotate .8s infinite linear; background: none !important; border: 3px solid rgba(100,100,100,.5);     border-top-color: rgba(100, 100, 100, 0.5); border-radius: 100%; border-top-color: #fff; content: ""; height: 34px !important; left: 50%; line-height: 1; margin-left: -17px; margin-top: -17px; pointer-events: none; position: absolute; top: 50% !important; -webkit-animation: loader-rotate .8s infinite linear; width: 34px !important; z-index: 154 !important; }
.grid-products .item .product-image { position:relative; overflow:hidden; margin:0 auto 5px; }
.grid-products .item .product-image > a { display:block; white-space:nowrap; opacity:1; }
    .grid-products .item .product-image img {
        display: inline-block;
        width: 100%;
        height: 100%;
        background-size: 100% 100%;
        margin: 0 auto;
        vertical-align: middle;
        -ms-transition: all ease-out 0.4s;
        -webkit-transition: all ease-out 0.4s;
        transition: all ease-out 0.4s;
    }
.grid-products .item .product-image .hover { visibility:hidden; opacity:0; left:50%; top:50%; position:absolute; -ms-transform:translate(-50%, -50%); -webkit-transform:translate(-50%, -50%); transform:translate(-50%, -50%); }
.grid-products .item .product-image:hover .primary { opacity:0; }
.grid-products .item .product-image:hover .hover:not(.variantImg) { opacity:1; visibility:visible; }
.grid-view_image:hover .primary { opacity:0; visibility:hidden; }
.grid-view_image:hover .hover:not(.variantImg) { opacity:1; visibility:visible; }
.grid-products .item .product-details.text-left .product-name a, 
.grid-products .item .product-details.text-left .product-price .price { font-size:14px; }

.add-to-cart-btn { margin:10px 0; }
.add-to-cart-btn i { vertical-align:middle; padding-right:2px; }
.add-to-cart-btn span { vertical-align:middle; }
.tab_container .grid-products.grid-products-style1 .item { padding:0; }
.grid-products-style1 .slick-slide { margin-right:15px; margin-left:15px; }
.grid-products-style1 .item { border:1px solid #e7e7e7; padding:0; }
.grid-products-style1 .item:hover { border:1px solid #ddd; }
.grid-products-style1 .item .product-details { padding:10px; }
.grid-products-style1 .item .saleTime span { background-color:#efefef; }
.medical-demo .btn.soldOutBtn, .soldOutBtn { background-color:#fe254a; }

.grid-products.style2 { position:relative; text-align:center; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.grid-products.style2 .item { position:relative; margin-bottom:10px; }
.grid-products.style2 .item .overlay { background-color:#f2f2f2; display:block; width:100%; height:100%; opacity:0; visibility:hidden; position:absolute; top:0; left:0; z-index:4; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.grid-products.style2 .item:hover .overlay { visibility:visible; opacity:1; }
.grid-products.style2 .item.product-image { width:100%; overflow:hidden; position:relative; z-index:1; }
.grid-products.style2 .item .product-details { width:100%; height:auto; padding-bottom:30px; opacity:0; visibility:hidden; position:absolute; top:50%; left:0; z-index:5; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.grid-products.style2 .item:hover .product-details { opacity:1; visibility:visible; }
.grid-products.style2 .item .button-set { width:100%; display:block; font-size:0px; position:absolute; bottom:-20px; top:auto; left:0; z-index:444; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.grid-products.style2 .item:hover .button-set { bottom:-10px; top:auto; }
.grid-products.style2 .item .product-review { margin-bottom:10px; }
.grid-products.style2 .item .product-image { margin-bottom:0; }

.button-set-top { font-size:16px; line-height:37px; top:12px; right:8px; z-index:5; transition:all ease-out .3s; -webkit-transition:all ease-out .3s; }
.grid-products .item:hover .button-set-top { top:8px; }
.button-set-top .wishlist { background:none; }
.grid-products .item:hover .button-set-top .btn-icon.wishlist { color:#000; background:#fff; }
.grid-products .item .button-set-top .btn-icon.wishlist:hover { color:#fff; background-color:#111; }
.button-set-top .quick-view, .button-set-top .add-to-compare { visibility:hidden; opacity:0; }
.grid-products .item:hover .button-set-top .quick-view,
.grid-products .item:hover .button-set-top .add-to-compare { opacity:1; visibility:visible; }
.button-set-bottom { transition:all ease-out .3s; -webkit-transition:all ease-out .3s; visibility:hidden; opacity:0; bottom:10px; z-index:5; left:0; right:0; }
.grid-products .item:hover .button-set-bottom { visibility:visible; opacity:1; bottom:15px; }
.button-set-bottom .btn { color:#000; background-color:#fff; border:0; }
.button-set-bottom .btn:hover { color:#fff; background-color:#111; border:0; }
.button-set-bottom .btn i { display:none; }

.grid-products .item:hover .button-set-top.style2 .wishlist { opacity: 1; visibility: visible; color: #fff; background-color: #000; }
.button-set-top.style2 .wishlist { visibility: hidden; opacity: 0; }
.button-set-top.style2 .btn-icon, .button-set-bottom.style2 .btn { color: #fff; background-color: #000; border-radius:4px; }
.button-set-top.style2 .btn-icon:hover, .button-set-bottom.style2 .btn:hover { color: #fff; background-color: #141414; }

.grid-view_image .product-image > a:after { content: ""; display:inline-block;width:0px; height:100%; vertical-align:middle; }

.slick-prev, .slick-next { width:30px; height:30px; text-align:center; position:absolute; z-index:9; display:inline-flex;align-items:center;justify-content:center; line-height:normal; font-size:0px; padding:6px 10px; cursor:pointer; background:transparent; color:transparent; top:50%; -webkit-transform:translate(0, -50%); -ms-transform:translate(0, -50%); transform:translate(0, -50%); padding:0; border:none; opacity:1; }
.slick-prev { left:10px; }
.slick-next { right:10px; }
.slick-prev:before, .slick-next:before { font-family:"annimex-icons"; font-size:20px; color:#222; opacity:.75; -webkit-font-smoothing:antialiased; -moz-osx-font-smoothing:grayscale; vertical-align:middle; display:block; }
.slick-prev:before { content:"\ea8b"; }
.slick-next::before { content:"\ea8c"; }

.arwOut3 .slick-prev { left:-30px; }
.arwOut3 .slick-next { right:-30px; }

.productSlider:hover .slick-arrow, .productPageSlider:hover .slick-arrow, .productSlider-style1:hover .slick-arrow, .productSlider-style2:hover .slick-arrow,
.productSlider-style2:hover .slick-arrow, .productSlider-fullwidth:hover .slick-arrow { opacity:1; }
.grid-products .slick-arrow { margin-top:-10px; width:30px; }
.productPageSlider .slick-arrow { margin-top:-20px; }
.productSlider-style1 .slick-arrow, .productSlider-style2 .slick-prev, .productSlider-fullwidth .slick-prev { margin-top:-10px; }
.productSlider .slick-arrow, .productPageSlider .slick-arrow, .productSlider-style1 .slick-arrow,
.productSlider-style2 .slick-arrow, .productSlider-fullwidth .slick-arrow { padding:6px 10px; opacity:0; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.productSlider .slick-next, .productPageSlider .slick-next, .productSlider-style1 .slick-next,
.productSlider-style2 .slick-next, .productSlider-fullwidth .slick-next { right:-35px; }
.productSlider .slick-prev, .productPageSlider .slick-prev, .productSlider-style1 .slick-prev,
.productSlider-style2 .slick-prev, .productSlider-fullwidth .slick-prev { left:-35px; }
.grid-products .slick-slider .item, .grid-products.slick-slider .item { margin-bottom:0; }

.product-labels { position:absolute; left:5px; top:5px; z-index:1; }
.product-labels.rectangular .lbl { border-radius:0; }
.product-labels.radius .lbl { border-radius:3px; -webkit-border-radius:3px; }
.product-labels.round .lbl { border-radius:100px; -webkit-border-radius:100px; width:45px; height:43px; line-height:44px; padding:0 10px; }
.product-labels .lbl { display: block; white-space:nowrap; color:#fff; font-size:11px; font-weight:400; text-align:center; padding:0 8px; height:20px; line-height:20px; margin-bottom:5px; }
.product-labels .on-sale { right:5px; background:#fe254a; }
.product-labels .pr-label1, .product-labels .new { left:5px; background:#01bad4; }
.product-labels .pr-label2, .product-labels .hot { left:5px; background:#e9a400; }
.product-labels .pr-label3 { left:5px; background:#81d53d; }
.product-labels .pr-label4 { left:5px; background:#fb6c3e; }
.product-labels.rounded .lbl { border-radius:50%; -moz-border-radius:50%; -webkit-border-radius:50%; display:-webkit-box; display:-webkit-flex; display:-moz-flex; display:-ms-flexbox; display:flex; -webkit-box-align:center; -ms-flex-align:center; -webkit-align-items:center; -moz-align-items:center; align-items:center; white-space:nowrap; word-break:break-all;-webkit-box-pack:center; -ms-flex-pack:center; -webkit-justify-content:center; -moz-justify-content:center; justify-content:center; text-align:center; min-height:50px; min-width:50px; }
.grid-view-item--sold-out .grid-view-item__image { opacity:0.5; }
.sold-out { position:absolute; top:0; width:100%; left:0; height:100%; }
.sold-out span { color:#fff; font-size:13px; position:absolute; top:50%; left:0; right:0; text-transform:uppercase; letter-spacing:0.08em; text-align:center; background-color:#fe254a; width:80%; margin:-20px auto; padding:8px; }
.product-image:hover .variants.add { bottom:5px; }
.button-set { position:absolute; right:5px; top:30px; opacity:0; visibility:hidden; z-index: 5; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.button-set.style1 { right:0; left:0; width:100%; text-align:center; bottom:-10px; top:auto; }
.button-set.style2 { right:auto; left:10px; width:35px; text-align:center; bottom:10px; top:auto; }
.button-set.style2 li .btn-icon { -webkit-transform: scale(.9); -moz-transform: scale(.9); transform: scale(.9); }
.button-set.style2 ul li, .button-set.style3 ul li { display:block; }
.button-set ul { list-style:none; padding:0; margin:0; }
.button-set ul li { display:inline-block; vertical-align:middle; }

.button-set li .btn-icon { color:#fff; display:inline-block; outline:none; background-color:#222; position:relative; font-size:14px; padding:0; margin:2px; width:100%; height:33px; width:33px; line-height:32px; text-align:center; border:2px solid transparent; }
.button-set li .btn-icon.btn-square { line-height:30px; }
.button-set li .btn-icon .icon { font-size:14px; }

.grid-products .item:hover .button-set.style1 { bottom:10px; top:auto; -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; }
.grid-products .item:hover .button-set.style2 { bottom:10px; top:auto; -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; }
.grid-products .item:hover .button-set.style3 { bottom:auto; top:10px; -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; }
.grid-products .item:hover .button-set { top:5px; opacity:1; visibility:visible; }

a.quick-view, a.wishlist, .add-to-compare { color: #000; background-color:#fff; border:0; width:35px; height:35px; display:flex;align-items:center;justify-content:center; text-transform:uppercase; text-align:center; padding:0; margin-bottom:5px; text-decoration:none; }
a.quick-view:hover, a.wishlist:hover, .variants.add button:hover, .add-to-compare:hover { color:#fff; background-color:#000; opacity:0.8; }

.cartIcon { color: #000; background-color:#fff; border-radius:1.25rem !important; border: 1px solid #222; width:40px; height:24px; display:flex;align-items:center;justify-content:center; text-transform:uppercase; text-align:center; padding:0; margin-bottom:5px; }
.cartIcon:hover { color:#fff; background-color:#fff; width:44px; border: 1px solid #222;}

.button-set .tooltip-label { height:23px; position:absolute; top:-20px; left:50%; font-size:10px; text-transform:uppercase; line-height:21px; -ms-transition:all 0.2s ease-in-out; -webkit-transition:all 0.2s ease-in-out; transition:all 0.2s ease-in-out; visibility:hidden; opacity:0; background:#000; color:#fff; border-radius:0; padding:1px 7px; white-space:nowrap; -ms-transform:translateX(-50%); -webkit-transform:translateX(-50%); transform:translateX(-50%); border-radius:3px; letter-spacing:0; }
.button-set .tooltip-label:before { content: ""; border:5px solid transparent; border-top:6px solid #000; bottom:-10px; margin-left:-3px; left:50%; position:absolute; }
.button-set .btn-icon:hover .tooltip-label { opacity:1; visibility:visible; top:-33px; }
.button-set.style2 .tooltip-label { left:30px; top:50% !important; height:24px; top:50%; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); }
.button-set.style2 li .btn-icon:hover .tooltip-label { top:50% !important; left:40px; }
.button-set.style2 .tooltip-label:before { border:5px solid transparent; border-right:6px solid #000; bottom:auto; left:-7px; top:7px; }
.button-set .btn-icon.btn-square .tooltip-label { line-height:18px; }

.button-set-top .btn-icon { position:relative; }
.button-set-top .tooltip-label,
.button-set.style3 .left { line-height:20px; right:55px; top:50%; left:auto; height:24px; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); }
.button-set-top .tooltip-label:before,
.button-set.style3 li .btn-icon .left:before { left:100%; top:50%; border:solid transparent; content: ""; height:0; width:0; position:absolute; pointer-events:none; border-color:rgba(0,0,0,0); border-left-color:#000; border-width:3px; margin-top:-3px; bottom:auto; margin-left:0; }
.button-set-top .btn-icon:hover .tooltip-label,
.button-set.style3 li .btn-icon:hover .left { top:50% !important; right:40px; opacity:1; visibility:visible; }
.button-set.style3 { right:10px; left:auto; width:35px; text-align:center; bottom:auto; top:15px; }

/*
.button-style2, .button-style2 .variants.add { position:static; opacity:1; }
.button-style2 .btn-style2 { display:block; float:left; width:25%; }
.button-style2 .cartIcon, .button-style2 .quick-view-popup, .button-style2 .wishlist, .button-style2 .compare { color:#fff; background-color:#000; border-right:1px solid #fff; }
.button-style2 .compare { border-right: 0; }
.button-style2 .wishlist, .button-style2 .compare { width:100%; }
.button-style2 .variants.add button { color:#fff; background-color:#000; }

 */

.button-set.style4 { bottom:15px; top:auto; left:0; right:0; }
.button-set.style4 .btn-icon { font-size:15px; display:inline-block; padding:0; margin:2px; border:0; vertical-align:middle; height:35px; width:35px; line-height:35px; text-align:center; -webkit-transform:scaleX(0); -moz-transform:scaleX(0); transform:scaleX(0); }
.grid-products .item:hover .btn-icon { -webkit-transform:scaleX(1); -moz-transform:scaleX(1); transform:scaleX(1); }
.grid-products .item:hover .button-set.style4 { top:auto; bottom:15px; opacity:1; visibility:visible; }

.button-set li .btn-icon.btn-square { border-radius:0; -webkit-border-radius:0; }
.button-set li .btn-icon.btn-square .tooltip-label { border-radius:0; -webkit-border-radius:0; }
.button-set li .btn-icon.btn-radius { border-radius:50px; -webkit-border-radius:50px; font-size: 14px; }
.button-set li .btn-icon.btn-radius .tooltip-label { border-radius:3px; -webkit-border-radius:3px; }

.button-set.style0 { bottom: 10px; top: auto; left: auto; right: auto; width: 100%; -webkit-transition: all .3s ease-out; transition: all .3s ease-out; }
.grid-products .item:hover .button-set.style0 { top: auto; }
.button-set.style0 ul { display: flex; justify-content: center; }
.button-set.style0 li .btn-icon { background-color:#fff; color:#222; border-color:#fff; border-radius: 4px; -webkit-transform: scale(.9); transform: scale(.9); transition: all .4s ease-out; }
.grid-products .item:hover .button-set.style0 .btn-icon { -webkit-transform: scale(1); transform: scale(1); }
.button-set.style0 .tooltip-label { top:auto; bottom:50px; }
.button-set.style0 .btn-icon:hover .tooltip-label { top:auto; bottom:42px; }

.tooltip-label { opacity:0; visibility:hidden; position:absolute; top:-38px; bottom:auto; left:50%; background:#000; color:#fff; border-radius:3px; padding:2px 6px; white-space:nowrap; font-size:10px; line-height:1.5; transform:translateX(-50%); text-transform:uppercase; text-align:center; z-index:2; -ms-transition:all 0.15s ease-in-out; -webkit-transition:all 0.15s ease-in-out; transition:all 0.15s ease-in-out; }
.tooltip-label:before { content:""; border:5px solid transparent; border-top:5px solid #000; position:absolute; bottom:-9px; left:50%; margin-left:-5px; }
.tooltip-label.left { right: 45px; left: auto; bottom: auto; top: 50%; -webkit-transform:translateY(-50%); transform:translateY(-50%); }
.tooltip-label.left:before { border-top-color: transparent; border-left-color: #000; right: -9px; top: 5px; left: auto; bottom: auto; }

.grid-products .item { margin:0 0 30px; text-align:center; }
.grid-products .item .product-vendor { color: #777; margin-bottom: 5px; }
.grid-products .item .product-name a { color:#222; font-size:13px; line-height:1.2; margin-bottom:0; font-weight:500; }
.grid-products .item .product-name a:hover { color:#222; text-decoration: underline !important; }
.grid-products .item .product-price { margin-top:2px; color:#000; font-weight:400; }
.grid-products .item .product-add-btn { margin-top:3px; }
.product-price .old-price { color:#555; font-size:13px; opacity:0.8; text-decoration:line-through; }
.product-price .old-price + .price { padding-left:5px; color:#fe254a !important; }
.product-price .price { color:#fe254a; font-size:17px; }
.product-price .product-labels-discount { padding: 0 2px 0 3px; border: 1px solid #fe254a; border-radius: 2px; font-size: 12px; height: 16px; line-height: 16px; color: #fe254a; }

.product-club-discount-tag-list { margin-bottom:0; }
.product-club-discount-tag-item { padding:0 6px; margin: 3px 5px 0 0; border-radius:2px; background: rgba(138, 43, 226, 0.3); flex:0 0 auto; display: inline; }
.product-club-discount-tag-item .product-club-discount-vip-icon img { height:17px; width:auto; padding-bottom: 1px; }
.product-club-discount-tag-item .club-discount-vip-discount { font-size:12px; }

.product-review .an { font-size:12px; opacity:1; margin:0 2px; }
.product-review a.reviewLink {color:#666;}
.product-review .spr-header .review-num .an {font-size:24px;}
/*.product-review .spr-review .reviewLink {color: #222; }*/
.product-review .review-num .num { font-size:32px; font-weight:600; margin-right: 5px; }
.fit-item .progress { height: 0.25rem; background-color: #ccc;}

.grid-products .product-review { margin:3px 0 3px; }
.grid-products .item .swatches { margin:0; list-style:none; padding:0; }
.grid-products .item .swatches li { position:relative; display:inline-block; height:17px; width:17px; margin:3px 2px; cursor:pointer; box-shadow:0 0 1px 1px #ddd; -webkit-box-shadow:0 0 1px 1px #ddd; border:2px solid #fff; }
.grid-products .item .swatches li:hover .tooltip-label { opacity:1; top:-28px; visibility:visible; }
.grid-products .item .swatches li img { display:block; border-radius:50%; max-height:30px; margin:0 auto; }
.grid-products .item .swatches li.square img { border-radius:0; }
.grid-products .item .swatches li.radius img { border-radius:5px; }
.grid-products .item .swatches li:hover { box-shadow:0 0 1px 1px #000; }
.grid-products .item .swatches li.rounded { border-radius:50% !important; }
.grid-products .item .swatches li.radius { border-radius:5px !important; }
.grid-products .item .swatches li.rectangle { width:34px; height:24px; }
.grid-products .item .swatches li.medium-xs { height:22px; width:22px; }
.grid-products .item .swatches li.medium { height:28px; width:28px; }
.grid-products .item .swatches li.large { height:35px; width:35px; }
.grid-products .item .swatches li.navy { background-color:navy; }
.grid-products .item .swatches li.green { background-color:green; }
.grid-products .item .swatches li.gray { background-color:gray; }
.grid-products .item .swatches li.aqua { background-color:aqua; }
.grid-products .item .swatches li.orange { background-color:orange; }
.grid-products .item .swatches li.purple { background-color:purple; }
.grid-products .item .swatches li.teal { background-color:teal; }
.grid-products .item .swatches li.black { background-color:black; }
.grid-products .item .swatches li.red { background-color:red; }
.grid-products .item .swatches li.yellow { background-color:yellow; }
.grid-products .item .swatches li.darkgreen { background-color:darkgreen; }
.grid-products .item .swatches li.maroon { background-color:maroon; }

.image-swatches li { width:30px; height:30px; padding:0; background-repeat:no-repeat; background-position:50% 50%; background-size:100% auto; }
.image-swatches li img { max-height:26px; }
.image-swatches li.blue { background-image:url(../images/products/swatches/80x80.jpg); }
.image-swatches li.pink { background-image:url(../images/products/swatches/80x80.jpg); }
.image-swatches li.red { background-image:url(../images/products/swatches/80x80.jpg); }
.image-swatches li.yellow { background-image:url(../images/products/swatches/80x80.jpg); }
.image-swatches li.pink1 { background-image:url(../images/products/swatches/80x80.jpg); }
.image-swatches li.gray { background-image:url(../images/products/swatches/80x80.jpg); }
.image-swatches li.gold { background-image:url(../images/products/swatches/80x80.jpg); }
.image-swatches li.brown { background-image:url(../images/products/swatches/80x80.jpg); }

.grid-products .item .swatches li:hover,
.grid-products .item .swatches li.active { box-shadow: none; border: 2px solid #eb5c3c; transition:all .5s ease-in-out; }
.grid-view-item__title { color:#000; margin-bottom:0; font-weight: 600; }
.grid-view-item__meta { margin:5px 0; }
.product-price__price { color:#000; font-weight:600; display:inline-block; }
.product-price__sale { color: #fe254a; }

.grid-products-hover-btn a.quick-view, .grid-products-hover-btn a.wishlist, .grid-products-hover-btn .variants.add button,
.grid-products-hover-btn .cartIcon, .grid-products-hover-btn .add-to-compare { color:#fff; background-color:#000; }
.grid-products-hover-gry a.quick-view, .grid-products-hover-gry a.wishlist, .grid-products-hover-gry .variants.add button,
.grid-products-hover-gry .cartIcon, .grid-products-hover-gry .add-to-compare { color:#fff; background-color:#555; }

.brand-name a { color:#555; font-size:12px; }
.grid-products .item .brands { margin:10px 0; font-size:13px; }
.grid-products .item .brands p { margin:0; }
.grid-products .item .brands .label { font-weight:600; }

.grid-products .product-tagbanner, .zoompro-wrap .product-tagbanner {
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    -webkit-transition: all .3s ease-out;
    transition: all .3s ease-out;
}
    .grid-products .item .product-tagbanner img.saletagimage, .zoompro-wrap .product-tagbanner img.saletagimage {
        display: flex;
        justify-content: center;
        width: 100%;
        height: 100%;
    }

/* Countdown Timer On listing */
.saleTime { margin-top: 3px; }
.saleTime .ht-count { float:left; }
.saleTime .time-count { color:#141414;}
.saleTime span > span { font-size:10px; min-width:30px; padding:6px 4px 4px; line-height:8px; display:block; text-align:center; }
.grid-products .item span > span span { background-color: #fed925; border-radius: .25rem; }
.grid-products .item:hover span > span span { background-color: #fed925;}
.saleTime span > span span { display:block; font-size:10px; font-weight:400; margin-top:-3px; text-transform:uppercase; line-height:8px }

.saleTime.rounded .ht-count:first-child .count-inner { border-radius: 6px 6px 0 0; }
.saleTime.rounded .ht-count:last-child .count-inner { border-radius: 0 0 6px 6px; }
.timermobile { margin:0 -10px; display:none }
.timermobile .saleTime { position:relative; margin-top:20px }

.countdown-deals { line-height:35px; text-align:center; width:100%; margin-bottom:10px; }
.countdown-deals .cdown { background:#efefef; display:inline-block; height:50px; width:44px; }
.countdown-deals .cdown span { font-size:14px; font-weight:500; }
.countdown-deals .cdown > p { font-size:12px; text-transform:uppercase; line-height:0; margin:0; }
.grid-products .countdown-deals { position:absolute; bottom:-10px; }
.grid-products .countdown-deals .cdown { color:#fff; background-color:#000; }
.product-list .countdown-deals { line-height:40px; text-align:left; }
.product-list .countdown-deals .cdown { font-size:14px; height:59px; width:65px; text-align:center; color:#fff; background-color:#000; }
.product-load-more .list-product, .product-load-more .item { display:none; }

/* List View - Change view */
.grid-products.prd-list .item { width:100%; display:flex; margin-bottom:20px; }
.grid-products.prd-list .product-image { position:relative; flex:0 0 200px; float:left; width:200px; margin:0 20px 0 0; }
.grid-products.prd-list .product-details { text-align:left !important; }
.grid-products.prd-list .product-name a { color:#222; font-size:15px; font-weight:500; margin-bottom:5px; }
.grid-products.prd-list .product-review { justify-content:flex-start !important; margin:3px 0 2px; }
.grid-products.prd-list .product-review .caption { display: block; }
.grid-products.prd-list .swatches.d-flex-justify-center { -webkit-box-pack:flex-start;-ms-flex-pack:flex-start;justify-content:flex-start; }
.grid-products.prd-list .sort-desc { margin: 0; display:block; }
.grid-products.prd-list .button-action .btn { color: #222; background-color: #fed925; border-color: #fed925; }
.grid-products.prd-list .button-action .btn:hover { color: #fff; background-color: #222; border-color: #222; }
.grid-products.prd-list .button-action .btn-icon { color: #fff; background-color: #222; border-color: #222; }
.grid-products.prd-list .button-action .btn-icon:hover { color: #222; background-color: #fed925; border-color: #fed925; }
.grid-products.prd-list .button-set,
.grid-products.prd-list .sold-out,
.grid-products.prd-list .button-set-top,
.grid-products.prd-list .product-add-btn { display:none !important; }
.grid-products.prd-list .saleTime { display: block !important; }
.grid-products.prd-list .product-label-container { margin-top:5px; }
.grid-products.prd-list.style2 .item { margin-bottom: 10px; }
@media only screen and (min-width: 992px){
    .shop-fullwidth .grid-products.prd-list .item.col-xxl-2,
    .shop-no-sidebar .grid-products.prd-list .item.col-xxl-2,
    .shop-left-sidebar .grid-products.prd-list .item.col-xxl-2 { -ms-flex:0 0 50%; -webkit-flex:0 0 50%; flex:0 0 50%; width:50%; }
}

.grid-products.prd-grid .button-action { display: none !important; }
.button-action > div { margin-right:5px; margin-top:8px; }
.button-action .btn { position:relative; padding:6px 15px; text-transform:none; border-radius:6px; margin-bottom:0; /*color:#fff; background-color:#222; border-color:#222;*/ } 
.button-action .btn:hover { color:#fff; background-color:#fed925; border-color:#fed925; }
.button-action .btn-icon { width:35px; height:35px; padding:0; }
.button-action .btn-icon .icon { font-size:13px; }
.button-action .btn-icon:hover .tooltip-label { bottom:auto; top:-28px; line-height:1.5; opacity:1; visibility:visible; }
.button-action .btn.soldOutBtn { background-color:#fe254a; border-color:#fe254a; color:#fff; }

.grid-products.prd-grid .button-action { display: none !important; }
.grid-products.prd-list.style2 .item .overlay { background:transparent; width:auto; height:auto; }
.grid-products.prd-list.style2 .item .product-details { opacity:1; visibility:visible; position:static; padding-bottom:0; -ms-transform:none; -webkit-transform:none; transform:none; -ms-transition:none; -webkit-transition:none; transition:none; }

.shop-hover-info .grid-products.prd-list .col-lg-3 { -ms-flex:0 0 100%; -webkit-flex:0 0 100%; flex:0 0 100%; width:100%; }

/* Quick View Model */
.loading .loadingBox { display:block; position:fixed; }
.loadingBox { display:none; padding:20px; background-color:#fff; border-radius:5px; box-shadow:0 0 5px rgba(0,0,0,0.3); position:fixed; z-index:1050; top:50%; left:50%; -ms-transform:translate(-50%, -50%); -webkit-transform:translate(-50%, -50%); transform:translate(-50%, -50%); }
.loadingBox .an { font-size:40px; opacity:0.9; }
.loading .modalOverly { display:block; }
.modalOverly { display:none; position:fixed; bottom:0; left:0; right:0; top:0; z-index:666; background-color:rgba(0,0,0,0.6); -ms-transition:all 0.45s cubic-bezier(0.29, 0.63, 0.44, 1); -webkit-transition:all 0.45s cubic-bezier(0.29, 0.63, 0.44, 1); transition:all 0.45s cubic-bezier(0.29, 0.63, 0.44, 1); }

.an-spin { animation:an-spin 1.5s infinite linear; -webkit-animation:an-spin 1.5s infinite linear; -ms-animation:an-spin 1.5s infinite linear; }
@-webkit-keyframes an-spin { 0% { -webkit-transform:rotate(0deg); transform:rotate(0deg); } 100% { -webkit-transform:rotate(359deg); transform:rotate(359deg); } }
@keyframes an-spin { 0% {-webkit-transform:rotate(0deg); transform:rotate(0deg); } 100% { -webkit-transform:rotate(359deg); transform:rotate(359deg); } }
@keyframes scaleimg { 0%, 100% { transform:scale(1); -webkit-transform:scale(1); -ms-transform:scale(1); } 50% { transform:scale(1.2); -webkit-transform:scale(1.2) -ms-transform:scale(1.2) } }

.model-thumbnail-img { position:relative; margin:10px auto 0 auto; }
.model-thumbnail-img .list-inline { position:static; width:auto; height:auto; margin:0 -4px; padding:0; }
.model-thumbnail-img .list-inline-item { width:100px; height:auto; opacity:1; text-indent:initial; margin:0; padding:0 4px; border:none; }
.model-thumbnail-img .list-inline-item.active { opacity:0.5; }
.model-thumbnail-img .carousel-arrow { color:#333; font-size:18px; background-color:rgba(255,255,255,0.5); opacity:1; position:absolute; top:50%; margin-top:-10px; width:22px; height:22px; text-align:center; }
.model-thumbnail-img .carousel-control-prev { left:0; right:auto; }
.model-thumbnail-img .carousel-control-next { right:0; left:auto; }

#quickView-modal { max-width:800px; margin:50px auto; background-color:#fff; position:relative; padding:25px;}
#quickView-modal .product-title { font-size:22px; text-transform:capitalize; margin:0 0 10px; }
#quickView-modal .product-review .rating .icon { font-size:14px; padding-right:2px; }
#quickView-modal .pricebox { margin:12px 0; }
#quickView-modal .pricebox .price { display:inline-block; font-size:25px; font-weight:500; line-height:1; }
#quickView-modal .pricebox .old-price { padding-right:10px; font-size:18px; font-weight:500; text-decoration:line-through; opacity:0.6; }
#quickView-modal .sort-description { margin-bottom:15px; padding-bottom:15px; border-bottom:1px dotted #939393; }

.products-grid-section .grid-products .button-set.style3 { position:static; opacity:1; visibility:visible; margin-top:10px; }
.products-grid-section .grid-products .row .item { margin-bottom:40px; }
.products-grid-section .grid-products .row:last-of-type .item { margin-bottom:20px; }

/* Quick Shop Popup */
.quickshop-content { width: 100%; height: 100%; opacity: 0; visibility: hidden; position: absolute; top: 0; left: 0; z-index: 9; background: #f2f2f2; border: 1px solid #f2f2f2; }
.quickshop-content.show { opacity: 1; visibility: visible; }
.quickshop-content .product-form .swatch label { margin-bottom: 4px; }
.quickshop-content .product-form .swatch .swatches li { height: 30px; width: 30px; box-shadow: none; border: none; }
.quickshop-content .product-form .swatch .swatches li,
.quickshop-content .product-form .swatch .swatch-element { margin: 4px; }

/*======================================================================
  14. Product Listview
========================================================================*/
.list-view-item { display:table; table-layout:fixed; margin-bottom:15px; padding-bottom:15px; width:100%; border-bottom:1px solid #e8e9eb; text-decoration:none; }
.list-view-item:hover { text-decoration:none; }
.list-view-item p { color:#555; }
.list-view-item__image-column { display:table-cell; vertical-align:middle; width:230px; }
.list-view-item__image-wrapper { position:relative; margin-right:20px; }
.list-view-item__title-column { display:table-cell; vertical-align:middle; }
.list-view-items .grid-view-item__title { font-size:15px; font-weight:700; font-family:'Retevis-Regular',Helvetica,Arial,Tahoma,sans-serif; margin-bottom:10px; text-align:left; }
.list-view-items .product-price { font-size:	16px; }
.list-view-items .product-price__sale { padding-left:5px; }
.list-view-items .variants { margin-top:10px; display:inline-block; vertical-align:middle; }
.list-view-items .button-box { display:inline-block; vertical-align:middle; margin-top:10px; }
.list-view-items .button-box > div { display:inline-block; }
.list-view-items .button-box > div .btn-icon { color:#fff; font-size:16px; background-color:#000; width:40px; height:30px; line-height:34px; padding:0 10px; display:inline-block; vertical-align:top; margin:0; }

/*======================================================================
  15. Products Detail Page
========================================================================*/
.product-form__item { -webkit-flex:1 1 200px; -moz-flex:1 1 200px; -ms-flex:1 1 200px; flex:1 1 200px; margin-bottom:10px; padding:5px 0 0; }
.product-form__item { -webkit-flex:1 1 200px; -moz-flex:1 1 200px; -ms-flex:1 1 200px; flex:1 1 200px; margin-bottom:10px; }
.product-template__container label .slVariant { font-weight:700; }

.product-form .swatch .product-form__item { margin-bottom:0; padding-bottom:0; padding-top:0; }
.product-form .swatch label { display:block; text-transform:uppercase; font-weight:500; margin-bottom:10px; }
.product-form .swatch label .required { color:#fe254a; }
.product-form .swatch .swatch-element { position:relative; display:inline-block; margin-right:10px; cursor:pointer;  }
.product-form .swatch .swatch-element:hover .tooltip-label { top:-26px; visibility:visible; opacity:1; }
.product-form .swatch .swatch-element .tooltip-label { top:-32px; }
.product-form .swatch .swatch-element.soldout { opacity:0.5; cursor:no-drop; }
.product-form .swatch .swatch-element.soldout:after { content:""; position:absolute; left:0; top:0; bottom:0; display:block; width:100%; height:100%; background: url(../images/soldout.svg) no-repeat 50% 50%/cover; }
.product-form .swatch .active .swatchLbl { border-color:#000; box-shadow:none; }

.pro-stockLbl .stockLbl { font-size:15px; text-transform:capitalize; font-weight:500; }
.pro-stockLbl .instock .icon { color:#61b33e; margin-right:5px; }
.pro-stockLbl .lowstock .icon, .pro-stockLbl .outstock .icon,
.pro-stockLbl .preorder .icon { color:#fe254a; margin-right:5px; }

.feature-list-txt > div { background:#eee; padding:8px 10px; }
.feature-list-txt > div + div { margin-left:5px; }

.product-form .swatch .swatchLbl.color { width:30px; height:30px; }
.product-form .swatch .swatchLbl.color.medium { width:50px; height:50px; }
.product-form .swatch .swatchLbl.large { width:40px; height:40px; }
.product-form .swatch .swatchLbl.xlarge { width:70px; height:70px; }
.product-form .swatch .swatchLbl.rectangle { height:24px; min-width:35px; }
.product-form .swatch .swatchLbl.large:not(.color) { line-height:36px; }
.product-form .swatch .swatchLbl {
    color:#333; font-size:13px; font-weight:500; text-transform:uppercase; margin:0; min-width:30px; height:30px; overflow:hidden; text-align:center; background-color:#f2f2f2; padding:0 10px; border:1px solid #fff; box-shadow:0 0 0 1px #ddd;
    background-repeat:no-repeat;background-position:50% 50%;background-size:100% auto; border-radius:0; display:flex;align-items:center;justify-content:center; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; cursor:pointer;
}
.product-form .swatch.swatches-image .swatchLbl { padding:0; background-repeat:no-repeat;background-position:50% 50%;background-size:100% auto; }
.product-form .swatch.swatches-image .swatchLbl.rectangle { height:45px; min-width:55px; }
.product-form .swatch.swatches-image .swatchLbl.top { background-position:top center; }
.product-form .swatch .swatchInput { display:none; }
.product-form .swatch.swatches-image .swatch-element .green { background-image:url(../images/products/swatches/80x80.jpg); }
.product-form .swatch.swatches-image .swatch-element .peach { background-image:url(../images/products/swatches/80x80.jpg); } 
.product-form .swatch.swatches-image .swatch-element .white { background-image:url(../images/products/swatches/80x80.jpg); }
.product-form .swatch.swatches-image .swatch-element .yellow { background-image:url(../images/products/swatches/80x80.jpg); }

.product-form { display:-webkit-flex; display:flex; -webkit-flex-wrap:wrap; flex-wrap:wrap; -webkit-align-items:flex-end; align-items:flex-end; width:100%; }
.product-form .swatch { margin-bottom:10px; width: 100%; clear:both; }
.product-form .swatch .swatch-element .black { background-color:#000; }
.product-form .swatch .swatch-element .white { background-color:#fff; border:1px solid #ddd; }
.product-form .swatch .swatch-element .red { background-color:#fe0000; }
.product-form .swatch .swatch-element .blue { background-color:#0000fe; }
.product-form .swatch .swatch-element .pink { background-color:#ffc1cc; }
.product-form .swatch .swatch-element .gray { background-color:#818181; }
.product-form .swatch .swatch-element .green { background-color:#027b02; }
.product-form .swatch .swatch-element .orange { background-color:#fca300; }
.product-form .swatch .swatch-element .yellow { background-color:#f9f900; }
.product-form .swatch .swatch-element .blueviolet { background-color:#8A2BE2; }
.product-form .swatch .swatch-element .brown { background-color:#A52A2A; }
.product-form .qtyField, .product-form .qtyField input { width: 92px; }

.product-action .add-to-cart .button-cart { width:100%; padding:6px 15px; min-height:40px; }
.product-action .wishlist-btn .wishlist { width:auto; float:left; padding:0; line-height:normal;  }
.product-action .wishlist-btn .wishlist i { vertical-align:middle; }
.product-action .wishlist-btn .wishlist:hover { color:#555; background-color:transparent; }

.share-icon { clear:both; }
.share-icon span { display:inline-block; font-weight:600; }
.share-icon .social-icons {  display:inline-block; }
.share-icon .social-icons li { margin-right:10px; }

.product-details-img .product-thumb { padding:0 0; width:15%; margin:0; float:left; }
.product-details-img .product-thumb-1 { display:block; padding:0; width:100%; margin:0; float:left; padding-right:0; }
.product-details-img .product-zoom-right { position:relative; padding:0 0 0 10px;  width:85%; float:left; }
.product-details-img.thumb-right .product-zoom-right { padding: 0 10px 0 0; }
.product-details-img.thumb-right .product-wish,
.product-details-img.thumb-right .product-buttons { right: 20px; }

.product-thumb .slick-slide { border:none; }
.product-thumb a.slick-slide { opacity:1; cursor:pointer; border:1px solid transparent; -ms-transition:all .5s ease-in-out; -webkit-transition:all .5s ease-in-out; transition:all .5s ease-in-out; }
.product-thumb a.slick-slide.active { opacity:0.5; }

.product-details-img .product-thumb .slick-arrow { top:auto; background-color:rgba(255,255,255,0.3); padding:5px 10px; opacity:0; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.product-details-img:hover .product-thumb .slick-arrow { opacity:1; }
.product-details-img .product-thumb .slick-prev:before { content:"\ea48"; }
.product-details-img .product-thumb .slick-next:before { content:"\ea45"; }

.product-details-img.product-single__photos.bottom .product-thumb .slick-arrow { background:transparent; }
.product-details-img.product-single__photos.bottom .product-thumb .slick-next { right:0; top:50%; left:inherit; }
.product-details-img.product-single__photos.bottom .product-thumb .slick-prev { left:15px; top:50%; bottom:0; }
.product-details-img.product-single__photos.bottom .product-thumb .slick-prev:before { content:"\ea8b"; font-family:"annimex-icons"; font-size:16px; }
.product-details-img.product-single__photos.bottom .product-thumb .slick-next:before { content:"\ea8c"; font-family:"annimex-icons"; font-size:16px; }

.product-details-img .product-tab-left div.slick-slide { margin-bottom:8px; }
.product-details-img .product-tab-left .slick-prev:before, 
.product-details-img .product-tab-left .slick-next:before { font-size: 16px; }
.product-details-img .product-tab-left .slick-prev,
.product-details-img .product-tab-left .slick-next { background:#eee; bottom:-45px; left:0; right:auto; top:auto; opacity:.9; width:48%; height:30px; margin:0; padding:0; }
.product-details-img .product-tab-left .slick-next { left: auto; right: 0; }
.product-details-img.thumb-left .product-labels { left:15px; }

.product-details-img .social-sharing { text-align:center; clear:both; padding-top:20px; }
.product-details-img .social-sharing .share-title { display:none; }

.product-buttons > a { position:relative; }
.product-buttons .tooltip-label {  }
.product-buttons .btn:hover .tooltip-label { opacity:1; visibility:visible; top:-30px; }

.product-buttons { position:absolute; right:10px; bottom:10px; z-index:99; }
.product-buttons .btn { font-size:19px; height:36px; width:36px; text-align:center; margin-top:5px; clear:both; padding:0; line-height:33px; background:#000; color:#fff; border-color:#000; opacity:0.9; }

.product-wish { position: absolute; right: 10px; top: 10px; z-index: 2; flex-direction: column; display: flex; align-items: center; }
.product-wish a:hover .tooltip-label { right: 40px; opacity: 1; visibility: visible; }

.product-template__container .product-single { margin-bottom:20px; }
.product-template__container .product-single__meta { position:relative; margin-bottom:20px; }

h1.product-single__title, .product-single__title.h1 { color:#222; font-size:22px; font-weight:600; margin-bottom:10px; }
.product-single__subtitle { margin-top:-6px; margin-bottom:12px; }
.product-review .reviewLink span {margin:0 10px;}

.product-template__container .product-nav { position:absolute; right:0; top:10px; }
.product-template__container .product-nav .next { float:right; }
.product-template__container .product-nav .prev, .product-template__container .product-nav .next { font-size:20px; display:block; line-height:22px; text-align:center; height:20px; width:20px; padding:0; color:#000; }

.product-info p { font-size:13px; position:relative; color:#767676; margin-bottom:2px; font-weight:300; }
.product-info p span { padding-left:10px; font-weight:300; text-transform:capitalize; }
.product-info p span.instock { color:#447900; }
.product-info p span a { color:#767676; padding: 0 3px 0; }
.product-info p span a:hover, .product-info p span a:before { color:#2d68a8; }
.product-info .table > :not(caption) > * > * { padding:0; }
.product-info .table th { font-weight:500; }
.product-info .table { --bs-table-bg: transparent; --bs-table-accent-bg: transparent; --bs-table-striped-color: #333; --bs-table-striped-bg: rgba(0, 0, 0, 0.05); --bs-table-active-color: #333; --bs-table-active-bg: rgba(0, 0, 0, 0.1); --bs-table-hover-color: #333; --bs-table-hover-bg: rgba(0, 0, 0, 0.075); color: #333; }

.discount-badge { display:inline-block; vertical-align:middle; margin:-2px 0 0 5px; font-size:13px; }
.discount-badge .off, .discount-badge .save-amount { color:#fe254a; }

.product-single__price { display:inline-block; margin: 15px 0 15px 0; }
.product-single__price .product-price__price { font-size:25px; font-family: "Retevis-Bold", Helvetica, Tahoma, Arial, serif; font-weight:500; line-height:1; padding-right:10px; }
.product-single__price .product-price-old-price { opacity:0.6; font-size:18px; font-weight:500; text-decoration:line-through; }

.countdown-text label { margin: 0 10px 0 0; color: #111; border-bottom: 1px solid rgba(17,17,17,.6); }
.countdown-text .prcountdown > span { color: #333; font-size: 13px; font-weight: 600; position: relative; padding-right: 8px; margin: 0 4px; text-transform: uppercase; letter-spacing: .09em; }
.countdown-text .prcountdown > span + span:before {  content: ":"; position: absolute; left: -10px; }

.countdown-text.style2 .prcountdown > span { color:#fe254a; }
.countdown-text.style3 { background-color: #fed925; padding: 8px 15px; }
.countdown-text.style3 label,
.countdown-text.style3 .prcountdown > span { color:#111; }
.countdown-text.style4 { background-color: #f2f2f2; padding: 8px 15px; }
.countdown-text.style4 label,
.countdown-text.style4 .prcountdown > span { color:#222; }

.orderMsg img, .orderMsg .icon { margin-right:10px; -webkit-animation-name:blinker; -webkit-animation-iteration-count:infinite; -webkit-animation-timing-function:cubic-bezier(.6, 0, 1, 1); -webkit-animation-duration:0.8s; }
.orderMsg .an-fire-l { color: #fe254a; }
@-webkit-keyframes blinker { from { opacity: 1.0; } to { opacity:0.0; } }
@keyframes blinker { from { opacity: 1.0; } to { opacity:0.0; } }

.product-description ul, .product-single__description ul { margin-left:0; }
.product-single__description ul { text-align:left; }
.product-description ul li, .product-single__description ul li { position:relative; margin-left:15px; list-style:disc; }

.product-description a { color:#2d68a8; }
.product-description a:hover,
.product-description a:active { color:#2d68a8; text-decoration:underline; }

.rte { margin-bottom:20px; }
.rte li { margin-bottom:4px; list-style:inherit; }
.rte h1, .rte .h1, .rte h2, .rte .h2, .rte h3, .rte .h3, .rte h4, .rte .h4, .rte h5, .rte .h5, .rte h6, .rte .h6 { margin-top:30px; margin-bottom:15px; }
.rte h1:first-child, .rte .h1:first-child, .rte h2:first-child, .rte .h2:first-child, .rte h3:first-child, .rte .h3:first-child, .rte h4:first-child, .rte .h4:first-child, .rte h5:first-child, .rte .h5:first-child, .rte h6:first-child, .rte .h6:first-child { margin-top:0 }
.rte:last-child { margin-bottom:0; }

.template-product .product-service .icon { height:auto; line-height:1; background:transparent; color:#eb5c3c; }
.template-product .product-service .service-info:hover .icon { background:transparent; color:#eb5c3c; }

.safecheckout .icon { color:#555; font-size:28px; line-height:1; }
.safecheckout .content { color:#555; font-size:13px; line-height:1.7; text-transform:uppercase; font-weight:500; padding-left:10px; }

.infolinks .btn { font-size:13px;border:0; background-color:transparent !important; color:#222 !important; text-transform:none; font-weight:400; width:auto; height:auto; padding:0; margin:0 15px 0 0; display:inline-block; }
.infolinks .btn:hover { color: #eb5c3c !important; }
.infolinks .btn:focus { outline:0; box-shadow:none; }
.infolinks .btn .icon { font-size:13px; vertical-align:middle; }

table { margin-bottom:15px; width:100%; border-collapse:collapse; border-spacing:0; }
#sizechart { position:relative; text-align:center; background:#fff; margin:0 auto; padding:20px; max-width:800px; box-shadow:0 0 20px rgba(255,255,255,.3); }
#sizechart table tr th { background:#000; color:#fff; border:0 !important; }
#sizechart table tr th, #sizechart table tr td { padding:7px 12px; text-align:center; font-size:12px; border:1px solid #e8e9eb; }
#sizechart ul, #ShippingInfo ul { margin:0 0 20px 0px; }

#productInquiry { position:relative; background:#fafafa; margin:0 auto; padding:20px; max-width:600px; }
#productInquiry h3 { font-size:15px; text-transform:uppercase; padding-right:15px; margin-bottom:20px; }
#productInquiry textarea { padding:10px; }

.product-template__container .product-action { width:100%; display:block; margin-bottom:15px; padding:0 5px; }
.product-template__container .product-form__item--quantity { float:left; margin:0 10px 10px 0; }

.product-form__item--submit .btn,
.product-form__item--buyit .btn { width:100%; height:44px; font-size:15px; margin-bottom:10px; }

.product-form__item--submit .btn { color:#222; background-color:#fed925; border-color:#fed925; }
.product-form__item--buyit .btn { color:#333; background-color: transparent; border-color:#333; }

.product-form__item--submit .btn:hover,
.product-form__item--buyit .btn:hover { color:#222; background-color:#fe6d25; border-color:#fe6d25; }

.product-form__pre-order.btn { background-color:#b7fe25; color:#222; border:1px solid #b7fe25; }
.product-form__pre-order.btn:hover, .product-form__pre-order.btn:focus { color:#222; background-color:#fe6d25; border-color:#fe6d25; }

.product-form__item--buyit .btn[disabled] { background-color:#f00; border-color:#f00; color:#fff; opacity:0.8;  cursor:default;  }

.agree-check { margin:15px 0 10px; }
.agree-check .checkbox { margin-right:3px; }

.social-sharing { margin: 0 -5px; }
.social-sharing .sharing-lbl { padding-left: 5px; }
.social-sharing .btn { color:#000; padding:0 5px; margin-bottom:0; background:none !important; border:0; letter-spacing:normal; text-transform:capitalize;text-decoration:none; }
.social-sharing .icon,
.social-sharing .share-title { color:#222; font-size:13px; margin-right:10px; }

.freeShipMsg, .shippingMsg, .userViewMsg { clear:both; margin-bottom:.5rem; }
.freeShipMsg .icon, .shippingMsg .icon, .userViewMsg .icon { min-width:25px; font-size:16px; vertical-align:sub; text-align:left; }
.userViewMsg .uersView { color:#fe254a; }
.sizelink { font-size: 11px; font-weight: 400; }

.review-rating { position: relative; width: 100%; display: flex; justify-content: flex-end; flex-direction: row-reverse; overflow: hidden; }
.review-rating .rating-0 { filter: grayscale(100%); }
.review-rating > input { display: none; }
.review-rating > label { cursor: pointer; width: 25px; height: 25px; margin: 0; background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='126.729' height='126.73'%3e%3cpath fill='%23666666' d='M121.215 44.212l-34.899-3.3c-2.2-.2-4.101-1.6-5-3.7l-12.5-30.3c-2-5-9.101-5-11.101 0l-12.4 30.3c-.8 2.1-2.8 3.5-5 3.7l-34.9 3.3c-5.2.5-7.3 7-3.4 10.5l26.3 23.1c1.7 1.5 2.4 3.7 1.9 5.9l-7.9 32.399c-1.2 5.101 4.3 9.3 8.9 6.601l29.1-17.101c1.9-1.1 4.2-1.1 6.1 0l29.101 17.101c4.6 2.699 10.1-1.4 8.899-6.601l-7.8-32.399c-.5-2.2.2-4.4 1.9-5.9l26.3-23.1c3.8-3.5 1.6-10-3.6-10.5z'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: center; background-size: 20px; transition: .3s; }
.review-rating > input:checked ~ label,
.review-rating > input:checked ~ label ~ label { background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='126.729' height='126.73'%3e%3cpath fill='%23ffb700' d='M121.215 44.212l-34.899-3.3c-2.2-.2-4.101-1.6-5-3.7l-12.5-30.3c-2-5-9.101-5-11.101 0l-12.4 30.3c-.8 2.1-2.8 3.5-5 3.7l-34.9 3.3c-5.2.5-7.3 7-3.4 10.5l26.3 23.1c1.7 1.5 2.4 3.7 1.9 5.9l-7.9 32.399c-1.2 5.101 4.3 9.3 8.9 6.601l29.1-17.101c1.9-1.1 4.2-1.1 6.1 0l29.101 17.101c4.6 2.699 10.1-1.4 8.899-6.601l-7.8-32.399c-.5-2.2.2-4.4 1.9-5.9l26.3-23.1c3.8-3.5 1.6-10-3.6-10.5z'/%3e%3c/svg%3e"); }
.review-rating > input:not(:checked) ~ label:hover,
.review-rating > input:not(:checked) ~ label:hover ~ label { background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='126.729' height='126.73'%3e%3cpath fill='%23d8b11e' d='M121.215 44.212l-34.899-3.3c-2.2-.2-4.101-1.6-5-3.7l-12.5-30.3c-2-5-9.101-5-11.101 0l-12.4 30.3c-.8 2.1-2.8 3.5-5 3.7l-34.9 3.3c-5.2.5-7.3 7-3.4 10.5l26.3 23.1c1.7 1.5 2.4 3.7 1.9 5.9l-7.9 32.399c-1.2 5.101 4.3 9.3 8.9 6.601l29.1-17.101c1.9-1.1 4.2-1.1 6.1 0l29.101 17.101c4.6 2.699 10.1-1.4 8.899-6.601l-7.8-32.399c-.5-2.2.2-4.4 1.9-5.9l26.3-23.1c3.8-3.5 1.6-10-3.6-10.5z'/%3e%3c/svg%3e"); }

.prFeatures { padding:20px 0; }
.prFeatures .feature { margin-bottom:20px; }
.prFeatures img { float:left; }
.prFeatures .details { margin-left:65px; line-height:1.5; }
.prFeatures .details h3, .prFeatures .details .h3 { margin-bottom:5px; text-transform:uppercase; }

.template-product .product-tabs li a { font-size:13px; font-weight:600; position:relative; display:block; padding:10px 0 10px; margin-right:30px; background-color:transparent; color:#666; border:none; cursor:pointer; }
.template-product .product-tabs li.active a, 
.template-product .product-tabs li:hover a { color: #000; text-decoration:none; }
.template-product .product-tabs li a:before { content:""; width:0; height:1px; background-color:#444; position:absolute; bottom:-1px; left:0; right:0; }
.template-product .product-tabs li.active a:before,
.template-product .product-tabs li:hover a:before { width:100%; }

.template-product .product-tabs.style2 li a { color:#000; background-color:#eee; padding:10px 25px; margin-right:10px; }
.template-product .product-tabs.style2 li.active a, 
.template-product .product-tabs.style2 li:hover a { background-color: #000;color: #eee; }
.template-product .product-tabs.style2 li a:before { content:none; }

.template-product .product-tabs.style3 li a { color:#000; background-color:#f2f2f2; border:1px solid transparent; border-bottom:0; padding:10px 25px; margin-right:10px; }
.template-product .product-tabs.style3 li.active a, 
.template-product .product-tabs.style3 li:hover a { background-color:#fff;color:#000; border-color:#e8e9eb; }
.template-product .product-tabs.style3 li a:before { background:#e8e9eb; }
.template-product .product-tabs.style3 li.active a:before { background:#fff; }

.acor-ttl.active { border-bottom:1px solid #000; }
.acor-ttl { display:block; padding:15px 0; position:relative; font-weight:600; letter-spacing:1px; border-bottom:1px solid #e8e9eb; font-family:Retevis-Regular,Helvetica,Tahoma,Arial,sans-serif; margin:0; font-size:12px; cursor:pointer; }

.template-product .tabs-listing .tab-container { padding:30px 0 20px; text-align:left; }
.tab-container .tab-content { display:none; }
.product-template__container .product-single-1 .tab-container .tab-content { padding-top:20px; }
.template-product .prstyle2 .tabs-listing .acor-ttl:before { position:absolute; right:15px; top:15px; content:"\f107"; font-family:'annimex-icons'; font-size:16px; font-weight:normal; }
.template-product .prstyle2 .tabs-listing .acor-ttl.active:before { content:"\f106"; color:#000; }

.spr-header { margin: 0 0 24px; padding: 24px 0 24px; background-color: #f7f8fa; }
.spr-header .progress-bar { background-color: #fed925; }
.product-review-form-wrapper { padding: 30px; background-color: #f7f8fa;  }
.product-review-form { display:none; }
.product-review-form .product-review .an { font-size:15px; }
.spr-reviews .review-inner { max-height:525px; overflow-y:auto; }
.spr-reviews .spr-review:not(:last-of-type) { border-bottom:1px solid #ECECEC; padding-bottom:15px; margin-bottom:15px; }
.spr-review-header-byline { font-size:13px; opacity:0.7; display:block; margin:-4px 0 10px 0; }
.spr-review-header-byline strong { font-weight:normal; }

#size-chart table tr th { background:#000; color:#fff; border:0 !important; white-space:nowrap; }
#size-chart table tr th, #size-chart table tr td { padding:7px 12px; text-align:center; font-size:12px; border:1px solid #e8e9eb; }

.related-product { margin-bottom:40px; }
.related-product .section-header { margin-bottom:20px; }
.related-product .section-header p { margin-left:auto; margin-right:auto; }
.related-product .button-set.style1 li .btn-icon { font-size:14px; margin:1px; width:30px; height:30px; line-height:28px; }
.product-template__container .section-header { margin-bottom:40px; }
.sub-heading { text-align:center; max-width:500px; margin:0 auto; }
.related-product .grid--view-items { overflow:visible; }
.recently-product .grid-products .item { float:left; }

.product-single__photos.bottom .product-dec-slider-1 { padding:8px 0; margin-left:-4px; }
.product-single__photos.bottom .product-dec-slider-1 .slick-list { margin:0 -2px; }
.product-single__photos.bottom .product-dec-slider-1 .slick-slide { margin:0 4px; }

.product-info .lbl { font-weight:700; }

.left-content-product { float:left; width:80%; padding-right:30px; }
.sidebar-product { float:left; width:20%; }
.sidebar-product .prFeatures { padding-top:0; }
.sidebar-product .prFeatures h5 { font-size:1.07692em; font-family:'Retevis-Regular',Helvetica,Arial,Tahoma,sans-serif; font-weight:600; }
.template-product-right-thumb .sidebar-product .prFeatures { padding-top:0; }
.sidebar-product .section-header { margin-bottom:20px; }

.prstyle3 .related-product { margin-bottom:20px; }
.prstyle3 .related-product:before, .prstyle3 .related-product:after { content:''; clear:both; display:block; }
.prstyle3 .related-product .section-header .h2, .prstyle3 .related-product .section-header .sub-heading { text-align:left; }
.prstyle3 .related-product .section-header { margin-bottom:12px; }
.prSidebar .section-header h2, .prSidebar .section-header .h2 { font-size:130%; text-align:left !important; }
.prstyle3 .mini-list-item .mini-view_image img { max-width:110px; }
.prstyle3 .mini-list-item .mini-view_image { width:28%; }
.prstyle3 .mini-list-item .details { margin-left:32%; }

.template-product-right-thumb .product-details-img .product-thumb { padding-right:0; padding-left:5px; }
.template-product-right-thumb .product-thumb .product-dec-slider-2 a { padding-bottom:3px; }
.template-product-right-thumb .prFeatures { padding:40px 0 20px; }

#product-countdown { position:static; margin:15px 0; }
#product-countdown:before, .product-countdown:after { content:''; clear:both; display:block; }
#product-countdown .time-count { font-weight:700; font-size:24px; display:block; width:100%; text-align:center; margin:0; }
#product-countdown { position:static; margin:15px 0; }
#product-countdown .count-inner { background-color:#fed925; min-width:50px; padding:8px; border-radius: 5px; margin-right:5px; }
#product-countdown .count-inner .time-count { color:#222; font-size:15px; line-height:18px; font-weight:bold; }
#product-countdown span > span { margin-top:0; }
#product-countdown span > span span { font-size:11px; line-height:14px; display:block; background-color:transparent; border:0; padding:0; min-width:100%; }

.product-right-sidebar .product-details-img { width:50%; float:left; padding-right:10px; }
.product-right-sidebar .product-information { width:50%; float:left; padding-left:10px; }
.product-right-sidebar .sidebar-product { width:100%; }
.product-right-sidebar .tabs-listing { clear:both; padding-top:30px; }
.product-right-sidebar .sub-heading { text-align:left; }
.product-right-sidebar .related-product { margin-bottom:20px; }

.product-single .product-single__meta { position:relative; margin-bottom:20px; }
.product-single .product-featured-img { width:100%; display:block; margin:0 auto; }
.product-single .grid_item-title { font-size:26px; margin-bottom:25px; }

.mfpbox { margin:0 auto; padding:20px; max-width:800px; position:relative; background:#fff; box-shadow:0 0 20px rgba(51,51,51,0.3); -webkit-box-shadow:0 0 20px rgba(51,51,51,0.3); }
.mfpbox .mfp-close { top:10px; right:10px; opacity:1; color:#333; line-height:30px; height:30px; width:30px }
.mfp-close { font-size:28px !important; }
button.mfp-close { width: 35px; height: 35px; line-height: 35px; }

.product-nav { color:#333; font-size:12px; line-height:1.2; max-width:200px; padding:5px; opacity:0.4; position:fixed; top:45%; z-index:10; background-color:#fff; box-shadow:0 0 10px rgba(0,0,0,0.2); }
.product-nav:hover { color:#333; opacity:1; }
.product-nav.prev-pro { left:-130px; }
.product-nav.next-pro { right:-130px; }
.product-nav .details { width:125px; padding:8px; font-weight:500; }
.product-nav span.img { width:60px; }
.product-nav:hover.prev-pro { left:0; opacity:1; }
.product-nav:hover.next-pro { right:0; opacity:1; }
.product-nav .name:hover { color:#f06543; opacity:1; }
.product-nav .price { margin-top:10px; display:block; }

.product-details-img .product-single-style2 .slick-list { margin-right: -5px; margin-left: -5px; }
.product-details-img .product-single-style2 .slick-slide { padding-right: 5px; padding-left: 5px; }
.product-horizontal-style .product-single-style2 .slick-arrow { top:50%; background-color:rgba(255,255,255,0.8); padding:5px 10px; opacity:0; visibility:hidden; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.product-horizontal-style:hover .product-single-style2 .slick-arrow { opacity:1; visibility:visible; }

.product-horizontal-style .product-horizontal-thumb .slick-list { margin:0 -5px; }
.product-horizontal-style .product-horizontal-thumb div.slick-slide { margin:0 5px; cursor:pointer; }
.product-horizontal-style .product-horizontal-thumb .slick-prev { left:0; right:auto; top:50%; height: 100%; margin:0; }
.product-horizontal-style .product-horizontal-thumb .slick-prev:before { content: "\ea8b"; font-family:"annimex-icons"; font-size:14px; }
.product-horizontal-style .product-horizontal-thumb .slick-next { right:0; left:auto; top:50%; height: 100%; margin:0; }
.product-horizontal-style .product-horizontal-thumb .slick-next:before { content: "\ea8c"; font-family:"annimex-icons"; font-size:14px; }

.product-360-degree-layout .trustseal-img { margin-top:20px; }
.product-single .type-product { display:block; margin:10px 0; }

/* Product Sticky Bottom Cart */
.stickyCart { display:none; background-color:#222; color:#fff; position:fixed; bottom:0; left:0; right:0; z-index:99; width:100%; padding:6px 0;  }
.stickyCart .product-featured-img { display:block; margin:0 auto; max-width:50px; }
.stickyCart .stickyOptions .selectedOpt, .stickyCart .selectbox { position:relative; background-color:#222;color:#f2f2f2;border:1px solid #535353; padding:0 30px 0 15px; height:35px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; width:200px; cursor:pointer; display:flex;align-items:center; }
.stickyCart .stickyOptions .selectedOpt:after { content:"\e905"; font-family:"annimex-icons"; position:absolute; top:8px; right:10px; }
.stickyCart .stickyOptions ul { position:absolute; bottom:115%; left:0; display:none; list-style:none; min-width:100%; max-height:300px; overflow:auto; background-color:#222; margin:0 0; }
.stickyCart .stickyOptions ul li { font-size:90%; overflow:hidden; border-bottom:1px solid #353535; padding:7px 12px; white-space:nowrap; text-overflow:ellipsis; cursor:pointer; }
.stickyCart .stickyOptions ul li.soldout { opacity:0.5; text-decoration:line-through; }
.stickyCart .qtyField .qtyBtn { color:#fff; height:35px; }
.stickyCart .qtyField .qty { border-color:#535353; color:#fff; height:35px; }
.stickyCart .qtyField .qty:focus { border-color:#848484; }
.stickyCart .product-form__cart-submit { padding:6px 20px 6px; }
    .stickyCart .sticky-title .old-price {
        color: #eee;
        opacity: 0.8;
        text-decoration: line-through;
        margin-right: 10px;
    }

    .stickyCart .sticky-title .price {
        color: #ed0f0f;
    }
/* Product Info Bg */
.wrap-product-info-bg { padding:30px 25px; background:#fafafa; text-align:center; }

.tab-accordian-style .accordion-button { font-size: 12px; font-weight: 600; margin: 0; padding: 12px 30px 12px 15px; text-transform: uppercase; border-radius: 5px !important; background-color: #fff; color: #444; border: 1px solid #eee; box-shadow: none; }
.tab-accordian-style .accordion-button:after { content: "\ea45"; font-family: 'annimex-icons'; font-size: 18px; font-weight: normal; position: absolute; top: 50%; right: 12px; background: none; width: auto; height: auto; -webkit-transform: translateY(-50%); transform: translateY(-50%); }
.tab-accordian-style .accordion-button:not(.collapsed):after { content: "\ea48"; }
.tab-accordian-style .accordion-button:not(.collapsed) { color: #222; border-color: #eee; background-color: #f2f2f2; }

.tab-vertical-style .nav-pills .nav-link { font-size: 12px; font-weight: 600; margin: 0 0 .5rem; padding: 12px 30px 12px 15px; border-radius: 5px !important; background-color: #fff; color: #444; border: 1px solid #eee; box-shadow: none; }
.tab-vertical-style .nav-pills .nav-link.active { color: #222; border-color: #eee; background-color: #f2f2f2; }

.row.g-2 { padding:0; margin:-5px; }
.row.g-2 > div { padding:5px; margin:0; }

.variable-select select { height: 37px; }
/*
.product-single-style3 .product-form__item .sizelink { padding-left:10px; }
.product-single-style3 .product-form { margin-left:0; margin-right:0; }
.product-single-style3 .product-form .col-12 { padding:0 5px; }
.product-single-style3 .product-form .row { width:100%; margin:0 -5px; }
.product-single-style3 .infolinks { margin:10px 0 30px; }
.product-single-style3 .orderMsg { color:#000; }
.product-single-style3 .orderMsg .an { font-size:20px; padding-right:5px; }
.product-single-style3 .product-action { margin:10px 0; padding:0; }
.product-single-style3 .storeFeatures { padding-top:10px; color:#111; margin:0 -10px 10px; -webkit-flex-wrap:wrap; -moz-flex-wrap:wrap; -ms-flex-wrap:wrap; flex-wrap:wrap; }
.product-single-style3 .storeFeatures p { padding:5px 10px; margin:0; }
.product-single-style3 .userViewMsg .an { font-size:14px; padding-right:5px; }
.product-single-style3 .userViewMsg .an, .product-single-style3 .userViewMsg .uersView { color:#000; }
.product-single-style3 .product-sticky-style { position:sticky; position:-webkit-sticky; top:70px; }
.product-single-style3 .product-single { margin-bottom:40px; }

 */

.block { background-color:#fbfbfb; padding:20px; }
.block-cart .product-name { padding-right: 15px; }
.block-cart .btn-remove1 { position:absolute; right:5px; top:5px; }

.product-single-center-mode .product-details-img .product-buttons { bottom:18px; right:10px; }
.product-single-center-mode .product-center-style3 .slick-slide:not(.slick-current) { opacity:0.5; }
.product-single-center-mode .product-info-center { position:relative; max-width:550px; margin:0 auto 30px; text-align:center; }
@media only screen and (min-width: 1025px) {
    .product-single-center-mode .product-center-style3 .slick-arrow { opacity:0; visibility:hidden; }
    .product-single-center-mode .product-center-style3:hover .slick-arrow { opacity:1; visibility:visible; }
}

.upsell-bundle .usbImgCall { position:relative; align-self:center; width:auto; flex:none; }
.upsell-bundle .usbImgCall + .usbImgCall:before { content:"\ebe0";font-family:'annimex-icons'; font-size:12px; position:absolute; top:44%; left:-5px; }
.upsell-bundle .usbRow select { width: auto; height: 30px; white-space: nowrap; border-radius: 0; }
.upsell-bundle .usbRow:not(.active) .customCheckbox input[type="checkbox"]:checked + label:after { opacity:0; }
.upsell-bundle .usbRow:not(.active) .customCheckbox input[type="checkbox"] + label::after { opacity: 1; }
.upsell-bundle .usbbtnCall { margin-left: 20px; max-width: 200px; }

.product-single-bordered { border-top:1px solid #e8e9eb; border-bottom:1px solid #e8e9eb; padding:10px 0; margin:10px 0; }

ul.checkmarkList li { list-style:none; position:relative; margin-left:22px; }
ul.checkmarkList li:before { content:"\e954";font-family:'annimex-icons'; position:absolute;left:-22px; text-align:left; font-size:13px; opacity:0.3; }

.product-variable-layout .product-form { border-top:1px dotted #a0a0a0; border-bottom:1px dotted #a0a0a0; padding:20px 0; margin:20px 0 30px; }
.product-variable-layout .infolinks .btn { margin-right:10px; }

.grouped-product-list tr { border-bottom:1px solid #e8e9eb; }
.grouped-product-list td { padding:15px 0px; }
.grouped-product-list .product-thumb { max-width:60px; }
.grouped-product-list .selectbox { position:relative; width:150px; height:35px; }

#threesixty { max-width:400px; background:#fff; margin:0 auto; padding:10px; position: relative; }
.threesixty { overflow:hidden; }
.threesixty .nav_bar { position:absolute; bottom:20px; left:50%; z-index:11; transform:translateX(-50%); -webkit-transform:translateX(-50%); -ms-transform:translateX(-50%); }
.threesixty .nav_bar a { font-size:0; width:40px; line-height:40px; height:40px; float:left; background-color:#fff; text-align:center; }
.threesixty .nav_bar a::before { display:inline-block; font-size:24px; font-family:"annimex-icons"; }
.threesixty .nav_bar a.nav_bar_previous::before { content: "\ea46"; }
.threesixty .nav_bar a.nav_bar_play::before { content:"\ebdf"; }
.threesixty .nav_bar a.nav_bar_next::before { content: "\ea47"; }
.threesixty .nav_bar a.nav_bar_stop::before { content: "\ebd8"; }
.threesixty .spinner { width:60px; display:block; margin:0 auto; height:30px; background:#333; -webkit-border-radius:5px; -moz-border-radius:5px; border-radius:5px; }
.threesixty .spinner span { font-family:Arial, "MS Trebuchet", sans-serif; font-size:12px; font-weight:bolder; color:#FFF; text-align:center; line-height:30px; display:block; }
.threesixty .threesixty_images { display:none; list-style:none; margin:0; padding:0; }
.threesixty .threesixty_images img { position:absolute; top:0; left:50%; height:auto; max-height:500px; transform:translateX(-50%); -webkit-transform:translateX(-50%); -ms-transform:translateX(-50%); }
.threesixty .threesixty_images img.current-image { visibility:visible; }
.threesixty .threesixty_images img.previous-image { visibility:hidden; }

/*======================================================================
  16. Sidebar
========================================================================*/
.sidebar .sidebar_widget { margin-bottom:30px; clear:both; width:100%; }
.sidebar .sidebar_widget:last-of-type { margin-bottom:0; }
.sidebar h2, .sidebar .h2 { font-family:'Retevis-Regular',Helvetica,Arial,Tahoma,sans-serif; margin-bottom:15px; font-size:14px; line-height:1.6; }
.sidebar .sidebar_widget .widget-content ul { margin:0 0 15px; list-style:none; }
.sidebar .sidebar_widget .widget-content .product-tags li { padding: 0; }
.sidebar .sidebar_widget .widget-content ul li { list-style:none; font-size:13px; }
.sidebar .sidebar_widget .widget-content ul li.lvl-1 + li { border-top: 1px solid #e7e7e7; }
.sidebar .sidebar_widget .widget-content ul li.active a { color:#2d68a8; font-weight:500; }

.filterBox ul:not(.filter-color) { list-style:none; }
.filterBox ul:not(.filter-color) input[type="checkbox"] { width:20px; height:auto; margin:0; padding:0; font-size:1em; opacity:0; display:none; }
.filterBox ul:not(.filter-color) input[type="checkbox"] + label { display:block; margin-left:0px; line-height:1.5em; cursor:pointer; margin-bottom:0; }
.filterBox ul:not(.filter-color) li label { font-size:13px; font-weight:400; }
.filterBox ul:not(.filter-color) input[type="checkbox"] + label > span { background:#fff; display:inline-block; width:16px;height:16px; margin:0 10px 0 0; border:1px solid #d0d0d0; vertical-align:middle; }
.filterBox ul:not(.filter-color) input[type="checkbox"]:checked + label > span::before { content:"\ea7f"; font-family:"annimex-icons"; display:block; width:14px; color:#000; font-size:11px; line-height:14px; text-align:center; }
.filterBox .filter-color { display:table; list-style:none; width:100%; }
.filterBox .filter-color ul, .sidebar .sidebar_widget .widget-content.filter-color ul { margin-top:-10px; margin-left:-8px; margin-bottom:0; }
.filterBox .filter-color .swacth-btn { display:block; float:left; margin-top:10px; margin-left:8px; position:relative; height:25px; width:25px; background-color:#f2f2f2; text-align:center; font-size:10px; line-height:21px; color:#000; cursor:pointer; border-radius:100px; background-repeat: no-repeat; background-position: 50% 50%; background-size: 100% auto; }
.filterBox .filter-color .swacth-btn.checked { box-shadow:0 0 0 1px #000,0 0 0 1px #fff inset; }
.filterBox .filter-color .swacth-btn.medium { height:30px; width:30px; }
.filterBox .filter-color .swacth-btn.rectangle { width:32px; height:22px; border-radius:0; }
.filterBox .filter-color .swacth-btn.radius { -webkit-border-radius:5px !important; border-radius:5px !important; }
.filterBox .filter-color .black { background-color:#000; }
.filterBox .filter-color .white { background-color:#fff; border:1px solid #ddd; }
.filterBox .filter-color .red { background-color:#fe0000; }
.filterBox .filter-color .blue { background-color:#0000fe; }
.filterBox .filter-color .pink { background-color:#ffc1cc; }
.filterBox .filter-color .gray { background-color:#818181; }
.filterBox .filter-color .green { background-color:#027b02; }
.filterBox .filter-color .orange { background-color:#fca300; }
.filterBox .filter-color .yellow { background-color:#f9f900; }
.filterBox .filter-color .blueviolet { background-color:#8A2BE2; }
.filterBox .filter-color .brown { background-color:#A52A2A; }
.filterBox .filter-color .darkGoldenRod { background-color:#B8860B; }
.filterBox .filter-color .darkGreen { background-color:#006400; }
.filterBox .filter-color .darkRed { background-color:#8B0000; }
.filterBox .filter-color .khaki { background-color:#F0E68C; }
.filterBox .filter-color .teal { background-color:#007d7d; }
.filterBox .filter-color .blue-red { background-image:url(../images/products/swatches/80x80.jpg); }
.filterBox .filter-color .black-grey { background-image:url(../images/products/swatches/80x80.jpg); }
.filterBox .filter-color .pink-black { background-image:url(../images/products/swatches/80x80.jpg); }
.filterBox .filter-color .yellow-black { background-image:url(../images/products/swatches/80x80.jpg); }
.flby-tlt { color: #000; font-size: 15px; position: relative; width: 100%; margin:10px 0 20px; }
.filterBox.size-swacthes ul { column-count:2; margin:-8px 0 0; } 
.filterBox.size-swacthes ul li { float:none; padding:8px 0 0; }

.shop-fullwidth .filterbar { width:300px; height:100%; padding:0 !important; background-color:#fff; box-shadow:0 0 5px rgba(0,0,0,0.3); position:fixed; top:0; left:-335px; z-index:9999; -ms-transition:0.5s; -webkit-transition:0.5s; transition:0.5s; }
.shop-fullwidth .filterbar.active { left:0; opacity:1; visibility:visible; }
.shop-fullwidth .filterbar .sidebar_tags { position: relative; z-index: 9; background: #fff; padding: 20px; height: 100%; overflow: auto; }
.shop-fullwidth .btn-filter { padding:6px 15px; border-radius: 3px; }
.shop-fullwidth .btn-filter:before { font-family: 'annimex-icons'; font-size: 17px; margin-right: 5px; }
.shop-fullwidth .filterbar .closeFilter { color:#fff; font-size:15px; line-height:32px; height:30px; width:30px; text-align:center; cursor:pointer; position:absolute; top:10px; left:100%; background-color:#000; box-shadow:0 0 5px #ddd; -ms-transition:all 0.4s ease-in-out; -webkit-transition:all 0.4s ease-in-out; transition:all 0.4s ease-in-out; }
.shop-fullwidth .filterbar.active .closeFilter:after { content:''; background:rgba(0,0,0,0.5); position:fixed; top:0; bottom:0; left:0; right:0; z-index:-1; }
.shop-fullwidth .filterbar .sidebar_widget:not(.filterBox), .filterbar .static-banner-block { display:none; }

.full-page-title .breadcrumbs-wrapper { margin-bottom:0; }
.full-page-title .page-title { background:url(../images/collection-banner/clothing-banner.jpg) no-repeat; background-size:cover; position:relative; background-position:50% 50%; padding:110px 0; }
.full-page-title .page-title h1 { margin-bottom:0 !important; }

.sidebar .filterDD, .collection-top-filters .filterDD { overflow:hidden auto; margin-top:15px; }
.sidebar .sidebar_widget.categories .sub-level { position:relative; }
.sidebar .sidebar_widget.categories .sub-level > span:after { content:'\ebe0'; font-family:'annimex-icons'; display:inline-block; position:absolute; right:0; top:10px; }
.sidebar .sidebar_widget.categories .sub-level > span.active:after { content:'\ebd1'; font-family:'annimex-icons'; display:inline-block; }
.sidebar .sidebar_widget.categories .sub-level ul { margin:0 0 10px; display:none; }
.sidebar .sidebar_widget.categories .sub-level ul li a { padding: 7px 25px 7px 15px; }
.sidebar .sidebar_widget.categories .sub-level .sub-sub-level ul li a { padding: 7px 25px 7px 30px; }
.sidebar .sidebar_widget.categories .sub-level .sub-sub-level > span:after { top: 7px; }

.sidebar .sidebar_widget.categories li a { color:#222; font-size:13px; padding:10px 25px 10px 0; display:block; }
.sidebar .sidebar_widget.categories li a:hover,
.sidebar .sidebar_widget.categories li a.active { color:#2d68a8; }
.sidebar .sidebar_widget.categories .filterDD { margin-top:10px; }

.sidebar-noborder .sidebar_widget.no-border ul { margin:-3px 0; }
.sidebar-noborder .sidebar_widget.no-border ul li.lvl-1 + li,
.sidebar-noborder .sidebar_widget.no-border ul li { border:none; }
.sidebar-noborder .sidebar_widget.no-border ul li a { padding:3px 0; }

.filter-widget .widget-title { position:relative; cursor:pointer; }
.filter-widget .widget-title:after { content:'\eb69'; font-family:'annimex-icons'; display:inline-block; position:absolute; right:0; top:-1px; font-size:17px; }
.filter-widget .widget-title.active:after { content:'\eb66'; font-family:'annimex-icons'; display:inline-block; }

/* Sidebar background */
.sidebar-bg .sidebar_widget.filterBox { padding:15px; margin-bottom:10px; background-color:#f9f9f9; border-radius:3px; }
.filterBox.size-swacthes.availability ul, .sidebar-bg .sidebar_widget.filterBox.availability ul { column-count:auto; -webkit-column-count:auto; -ms-column-count:auto; }

/* Sidebar border */
.sidebar-border .sidebar_widget.filterBox { padding: 15px; margin-bottom: 20px; border: 1px solid #e7e7e7; border-radius: 3px; }
.sidebar-border .filterBox .widget-content { border-top: 1px solid #e7e7e7; padding-top: 15px; margin-top: 15px; }
.sidebar-border .sidebar_widget .widget-content ul li.lvl-1 + li { border-top:none; }
.sidebar-border .sidebar_widget.categories .sub-level > a::after,
.sidebar-border .sidebar_widget.categories .sub-level .sub-sub-level > a:after { top: 4px; }
.sidebar-border .sidebar_widget.categories li a { padding: 4px 25px 4px 0; }
.sidebar-border .sidebar_widget.categories .sub-level ul li a { padding: 4px 25px 4px 15px; }
.sidebar-border .sidebar_widget.categories .sub-level .sub-sub-level ul li a { padding: 4px 25px 4px 30px; }

/* Size Swacthes */
/*
.size-swacthes .swacth-list ul { margin-left:0; }
.size-swacthes .swacth-list li { float:left; display:block; }
.size-swacthes .swacth-list .swacth-btn { font-size:11px; display:block; margin-bottom:2px; width:30px; height:30px; line-height:28px; }

 */

/* Price Range */
.price-filter input[type="text"] { background:#fff; height:30px; padding:0 10px; text-align:center; font-size:12px; width:100px; }
#slider-range.ui-slider-horizontal { background:#e9e9e9; border:none; border-radius:0; height:3px; margin:20px 0; }
#slider-range .ui-slider-handle { background:#000; border:2px solid #000; height:12px; outline:none; top:-5px; width:12px; border-radius:50%; cursor:w-resize; margin-left:0px; }
#slider-range .ui-slider-handle + .ui-slider-handle { margin-left:-12px; }
#slider-range.ui-slider-horizontal .ui-slider-range { background:#777; border:0; }
#slider-range.ui-slider-horizontal .ui-slider-range ~ .ui-slider-range { background:#000; }

/* Color Swatches */
/*
.swacth-list li { position:relative; float:left; }
.swacth-list li .tooltip-label { top:-23px; left:50%; transform:translateX(-50%); margin-left:5px; }
.swacth-list li:hover .tooltip-label { opacity:1; top:-18px; visibility:visible; }
.grid-products .item .swatches.color-style li { box-shadow:none; -webkit-box-shadow:none; }
.grid-products .item .swatches.color-style li input[type="checkbox"] { display:none; }
.grid-products .item .swatches.color-style li input[type="checkbox"] + label.color { margin:0; cursor:pointer; border:1px solid #ccc; }
.grid-products .item .swatches.color-style li input[type="checkbox"] + label.color span { display:block; height:25px; width:25px; }
.grid-products .item .swatches.color-style li input[type="checkbox"]:checked + label.color { border:1px solid #000; box-shadow:0 0 1px #000; }
.grid-products .item .swatches.color-style li .black { background-color:#000; }
.grid-products .item .swatches.color-style li .white { background-color:#fff; }
.grid-products .item .swatches.color-style li .red { background-color:#fe0000; }
.grid-products .item .swatches.color-style li .blue { background-color:#0000fe; }
.grid-products .item .swatches.color-style li.rounded { width:25px; height:25px; border-radius:50% !important; -webkit-border-radius:50% !important; }
.grid-products .item .swatches.color-style li.rounded input[type="checkbox"] + label.color,
.grid-products .item .swatches.color-style li.rounded input[type="checkbox"] + label.color span,
.grid-products .item .swatches.color-style li.rounded input[type="checkbox"]:checked + label.color { border-radius:50% !important; -webkit-border-radius:50% !important; }
.grid-products .item .swatches.color-style li.radius input[type="checkbox"] + label.color,
.grid-products .item .swatches.color-style li.radius input[type="checkbox"] + label.color span,
.grid-products .item .swatches.color-style li.radius input[type="checkbox"]:checked + label.color { border-radius:5px !important; -webkit-border-radius:5px !important; }
.grid-products .item .swatches.color-style li.small,
.grid-products .item .swatches.color-style li.small input[type="checkbox"] + label.color span { width:15px; height:15px; }

 */
/* End Color Swatches */

.product-tags { display:flex; flex-wrap:wrap; }
.product-tags li { background:#fff; border-radius:4px; display:inline-block; border:1px solid #e8e9eb; margin-bottom:5px; margin-right:5px; }
.product-tags li a { padding:5px 10px; font-size:11px; display:inline-flex; }
.btnview { background:none; color:#000; padding:5px 0; border-bottom:1px solid #000; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.btnview:hover { background:none; border-color:#fff; color:#eb5c3c; }

.filters-toolbar-wrapper { border:0; margin:0 0 20px 0; }
.filters-toolbar-wrapper .change-view { position:relative; color:#555; font-size:0; cursor:pointer; background:none; border:0; padding:0 5px; }
.filters-toolbar-wrapper .change-view--active { color:#000; }
.filters-toolbar-wrapper .change-view .icon { font-size: 16px; }
.filters-toolbar-wrapper .change-view:hover .tooltip-label { opacity:1; top:-28px; visibility:visible; }
.filters-toolbar__product-count { font-size:12px; font-style: italic; margin-bottom:0; overflow:hidden; text-overflow:ellipsis; white-space:nowrap; }
.filters-toolbar-wrapper select { font-size:12px; }
.filters-toolbar__input { padding:5px 20px 5px 10px; overflow:hidden; text-overflow:ellipsis; white-space:nowrap; max-width:100%; height:36px; opacity:1; -ms-transition: all ease-out 0.15s; -webkit-transition:all ease-out 0.15s; transition:all ease-out 0.15s; }

.toolbar { margin-top:20px; }
.active-filters a { color:#000; background-color:#fff; font-size:12px; display:inline-block; padding:2px 10px; margin:0 5px 5px 0; border:1px solid rgba(0,0,0,0.2); border-radius:4px; white-space:nowrap; cursor:pointer; }
.active-filters a:hover { color:#fff; background-color:#000; border-color:#000; }
.active-filters a .an { font-size:10px; margin-left:5px; }
.btn.brd-link {color:#333;padding:0 0 2px;margin:15px 0 0;border:none;border-bottom:1px solid #333;border-radius:0;font-weight: 600}
.btn.brd-link:hover {border-color:#fe254a;color:#fe254a}

.infinitpagin { clear:both; padding:15px 0 0px; text-align:center; }
.loadMore { color:#fff !important; }

.pagination { width:100%; text-align:center; list-style:none; font-size:1.15385em; }
.pagination ul { display:flex; flex-wrap:wrap; align-items:center; margin:0 auto; list-style:none; }
.pagination li a { font-size:12px; color:#333; height:35px; width:35px; display:flex;align-items:center;justify-content:center; border:2px solid #f2f2f2; margin:0 2px; vertical-align:middle; }
.pagination li:hover a { color:#222; border-color:#222; }
.pagination li.active a { color: #222; border-width:2px; border-color: #222; }

.sideProSlider.grid-products .item .swatches li,
.sideProSlider.grid-products .button-set.style0 ul li { padding:0; }
.sideProSlider .slick-next { right:0; }
.sideProSlider .slick-prev { left:0; }
.sideProSlider .slick-arrow { opacity:0; visibility:hidden; width:32px; height:32px; background:#fff; border-radius:4px; }
.sideProSlider:hover .slick-arrow { opacity:1; visibility:visible; }
.sideProSlider .slick-prev:before, .sideProSlider .slick-next:before { font-size:16px; line-height:32px; }

.sidebar .storeFeatures { margin:10px 0 20px; -webkit-flex-wrap:wrap; -moz-flex-wrap:wrap; -ms-flex-wrap:wrap; flex-wrap:wrap; padding-bottom:15px; border-bottom:1px solid #e7e7e7; }
.sidebar .storeFeatures p { width:100%; }
.sidebar .storeFeatures .an { font-size:16px; min-width:25px; display:inline-block; }

/* Collection top filters */
.collection-top-filters .flTtl { font-size: 13px; font-weight: 600; color: #000; background: transparent; text-transform: uppercase; display: flex; align-items: center; justify-content: space-between; border: none; outline: none; padding: 15px; }
.collection-top-filters .flTtl:after { content: "\eb66"; font: normal normal normal 15px/1 annimex-icons; padding: 0 0 0 10px; margin: 0; border: none; }
.collection-top-filters .flTtl.show:after { content: "\eb69"; }
.collection-top-filters .count-bubble { color: #fff; background-color: #222; display: inline-block; margin:0 5px; height: 15px; width: 15px; font-size: 10px; font-weight: 400; }
.collection-top-filters .count-bubble:empty { display:none; }
@media only screen and (min-width: 992px) {
    .collection-top-filters { background-color: #f2f2f2; border: 1px solid #edecec; border-radius: 2px; padding: 0 5px 0 15px; margin: 0 0 25px; }
    .collection-top-filters .filterDD { padding: 0; width: 305px; color: #000; background-color: #f2f2f2; border: 1px solid #edecec; border-radius: 0 0 6px 6px; box-shadow: 1px 1px 2px rgba(0,0,0,.12); z-index:5; }
    .collection-top-filters .filterDD .bxTtl { position:sticky; position:-webkit-sticky; top:0; z-index:1; padding: 10px 15px; background-color: #f2f2f2; border-bottom: 1px solid #edecec; font-size: 12px; line-height: 1.6; letter-spacing: .02em; }
    .collection-top-filters .filterDD .swacth-list { padding: 15px; }
}

/*======================================================================
  End Sidebar
========================================================================*/

/*======================================================================
  17. Shop Pages
========================================================================*/


.category-banner, .category-description { margin-bottom:20px; }
.shop-listing .page-title h1 { font-size:18px; text-align:left; margin-bottom:20px; }

.small-heading .page-title { background-color:#f2f2f2; padding:45px 0; }
.small-heading .page-title h1 { text-align:center; margin-bottom:0; }
.small-heading .breadcrumbs-wrapper { margin-bottom:0; }

.category-text-banner { background:url(../images/collection-banner/watch-banner.jpg) no-repeat 50% 50%; background-size:cover; width:100%; height:230px; position:relative; }
.category-text-banner .page-title { position:absolute; top:50%; left:0; margin-top:-25px; padding:0 50px; }
.category-text-banner .page-title h1 { margin-bottom:10px; }
.category-text-banner .block-ttl { width:100%; }

.category-banner-slider { margin-bottom:20px; }
.category-banner-slider .slick-arrow { opacity:0; visibility:hidden; width:40px; border-radius:5px; height:40px; line-height:38px; text-align:center; background-color:rgba(255,255,255,0.5); }
.category-banner-slider .slick-arrow:before { line-height:40px; }
.category-banner-slider:hover .slick-arrow { opacity:1; visibility:visible; }
.category-banner-slider .slick-arrow:hover { background-color:rgba(255,255,255,0.5); box-shadow:0 0 4px rgba(0,0,0,0.3); -webkit-box-shadow:0 0 4px rgba(0,0,0,0.3); }
.category-banner-slider .slick-prev { left:10px; }
.category-banner-slider .slick-next { right:10px; }
/*======================================================================
  End Shop Pages
========================================================================*/

/*======================================================================
  18. CMS Page
========================================================================*/
.clr-fa { background-color: #fafafa; }
.clr-f5 { background-color: #f2f2f2; }
.row_text { font-size: 14px; }
.row_text .row-text { padding: 15px 45px; margin: 0 auto; max-width: 90%; width: 100%; }

.social-url { position: absolute; bottom: 5px; right: 5px; z-index: 2; }
.social-url a { color: #333;background-color:rgba(255,255,255,.7); text-align: center; height: 33px; width: 33px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin: 3px; -webkit-transform: translateX(100px); transform: translateX(100px); -webkit-transition: all .5s; transition: all .5s; }
.social-url a:hover { background-color: #fff; }
.team-item:hover .social-url a { -webkit-transform: translateX(0); transform: translateX(0); }
.team-bio h4 { margin-bottom: 10px; }

/* About Page */
/*
.about-pstyle2 .collection-hero.inner .collection-hero__title-wrapper:before { background-color: #222; opacity: 0.4; visibility: visible; }
.about-pstyle2 .collection-hero.inner .collection-hero__title { font-size: 40px; font-weight: 600; text-transform: none; }
.about-pstyle2 .collection-hero.inner .collection-hero__image { height: 450px; background-image: url(../images/slideshow/demo1-banner1.jpg); }
.about-pstyle3 .testimonial-slider.style1 { background:#f2f2f2; }
.about-pstyle3 .collection-hero.inner .collection-hero__title { font-size: 57px; font-weight: 600; text-transform: none; }
.about-pstyle3 .collection-hero.inner .collection-hero__image { height: 246px; background-image:url(../images/about/aboutus-3.jpg); }

 */

.about-service .icon { background: #fff; width: 85px; height: 85px; line-height: 85px; border-radius: 50%; color: #eb5c3c; font-size: 50px; -webkit-transition:all .3s ease-in-out; transition:all .3s ease-in-out; }
.about-service .service-info:hover .icon { background: #848484; color: #fff; }
.checkmark-info .icon { margin-right: 15px; color: #2db858; font-size: 20px; }
.service-info h4 { margin-bottom: 10px; }

.about-review { background-color: #333; color: #fff; }
.about-review .quote-wraper .quotes-slide { background: #222; box-shadow: 0 0 5px rgba(0,0,0,.05); border-radius: 4px; -webkit-transition: all .3s ease-in-out; transition: all .3s ease-in-out; }
.about-review .quote-wraper .quotes-slide:hover { background:rgba(17,17,17,0.7); }
.about-review .quote-wraper blockquote { font-size: 16px; color: #fff; }
.about-review .quote-wraper .product-review { color: rgba(248,212,6,0.8); margin:12px 0; }
.about-review .quote-wraper .authour { font-size: 15px; margin-bottom: 3px; }
.about-review .slick-dots li button { background-color: #fff; }

.error-404-page .page-title h1 { font-size:28px; margin:25px 0 10px; }
.error-404-page #page-content .error-content p { font-size:15px; margin-bottom: 25px; }



/* User Page */

/* User Sidebar */
.user-page,
.user-page .breadcrumbs-wrapper {background: rgb(255, 255, 255);}
.user-page.user-page-light,
.user-page.user-page-light .breadcrumbs-wrapper {background: rgb(247, 248, 250);}
.user-page .sidebar-wrapper {background: #fff;}
    .user-page .sidebar-content, .user-sidebar .sidebar_widget.sidebar-content {
        padding: 25px 20px 30px;
    }
.user-sidebar .sidebar_widget {clear:both; width:100%; }
.user-sidebar .sidebar_widget li a {padding:10px 25px 10px 0; margin: 0; display:block; }
.user-sidebar .widget-title a:hover,
.user-sidebar .widget-title a.active,
.user-sidebar .sidebar_widget li a:hover,
.user-sidebar .sidebar_widget li a.active { color:#222; text-decoration: none; }
.user-sidebar .widget-content ul li { list-style:none; }
.user-sidebar .sidebar_widget .sub-level {position:relative;}
.user-sidebar .sidebar_widget .sub-level > a:after { content:'\ebe0'; font-family:'annimex-icons'; display:inline-block; position:absolute; right:0; top:10px; }
.user-sidebar .sidebar_widget .sub-level > a.active:after { content:'\ebd1'; font-family:'annimex-icons'; display:inline-block; }
.user-sidebar .sidebar_widget .sub-level ul { margin:0 0 10px; display:none; }
.user-sidebar .sidebar_widget .sub-level ul li a { color: #666; font-weight:300; padding: 7px 25px 7px 15px; }
.user-sidebar .sidebar_widget .sub-level ul li a:hover,
.user-sidebar .sidebar_widget .sub-level ul li a:active { color: #222;}
.user-sidebar .sidebar_widget .sub-level .sub-sub-level ul li a { padding: 7px 25px 7px 30px; }
.user-sidebar .sidebar_widget .sub-level .sub-sub-level > a:after { top: 7px; }


/* User Content */
.user-content h1 {font-size:28px;text-transform:uppercase;font-weight:600;text-align:center;margin:0;}
.user-content .user-title {padding-top:20px;}
.user-content .content-wrapper {background: #fff; padding: 20px; margin-bottom: 10px;}
.user-content .user-assets a .tab-extra,
.user-content .user-assets a .tab-title {display: block;}
.user-content .user-assets a .tab-extra {height: 36px;line-height: 36px; color: #222; font-size: 16px;font-weight: 500;}
.user-content .user-assets a .tab-extra.an {font-size: 32px;}
.user-content .user-assets a .tab-title {color: #666;}
.user-content .user-assets a:hover {text-decoration: none; }
.user-content .user-assets a:hover .tab-extra { color:#222; font-weight: 700; }
.user-content .user-assets .alert .col {height: 0;}
.user-content .user-assets .col .an-caret-up {position: relative; top:-33px; font-size: 24px; color:#ffecb5;}

.user-page .user-content .rounded {border-radius:0.5rem !important;}
.cartIcon.rounded,
.user-page .user-content .cartIcon.rounded {border-radius:1rem !important;}
.vip-equity h1 {text-align:left;height:80px;line-height:82px;font-weight:700;padding:0 26px;margin-bottom:10px;background-size:contain;background-repeat:no-repeat;background-position:100%;text-transform:capitalize;}
.vip-equity__user-level-s0 h1 {color:#222;background-color:#6e6e6e;background-image:url(../images/user/vip-bg-top-s0.png);}
.vip-equity__user-level-s1 h1 {color:#97beab;background-color:#626e5b;background-image:url(../images/user/vip-bg-top-s1.png);}
.vip-equity__user-level-s2 h1 {color:#e6aaaa;background-color:#6d5e5e;background-image:url(../images/user/vip-bg-top-s2.png);}
.vip-equity__user-level-s3 h1 {color:#eec887;background-color:#5a544b;background-image:url(../images/user/vip-bg-top-s3.png);}
.vip-equity__user-level-s0 .vip-equity__main {background-image:url(../images/user/vip-bg-main-s0.png);}
.vip-equity__user-level-s1 .vip-equity__main {background-image:url(../images/user/vip-bg-main-s1.png);}
.vip-equity__user-level-s2 .vip-equity__main {background-image:url(../images/user/vip-bg-main-s2.png);}
.vip-equity__user-level-s3 .vip-equity__main {background-image:url(../images/user/vip-bg-main-s3.png);}
.vip-equity__main-img {display:inline-block;position:relative;vertical-align:middle;}
.vip-equity__main-name {display:inline-block;vertical-align:middle;white-space:normal;}
.vip-equity__main-img img {width:84px;height:84px;overflow:hidden;}
.vip-equity__main-level-normal,
.vip-equity__main-process-normal {background-image:url(../images/user/icon_level.png);}
.vip-equity__main-level,
.vip-equity__main-process {background-size:864px 149px;}
.vip-equity__main-level {display:inline-block; width:60px;height:24px;}
.vip-equity__user-level-s0 .vip-equity__main-level {background-position:-250px -45px;}
.vip-equity__es-user-novip .vip-equity__main-process,
.vip-equity__user-level-s0 .vip-equity__main-process {background-position:-646px -93px;}
.vip-equity__main-process {width:192px;height:28px;}
.vip-equity__user-level-s1 .vip-equity__main-level {background-position:-180px -45px;}
.vip-equity__user-level-s1 .vip-equity__main-process {background-position:-444px -93px;}
.vip-equity__user-level-s1 .vip-equity__main-level {background-position:-110px -45px;}
.vip-equity__user-level-s2 .vip-equity__main-process {background-position:-242px -93px;}
.vip-equity__user-level-s1 .vip-equity__main-level {background-position:-40px -45px;}
.vip-equity__user-level-s3 .vip-equity__main-process {background-position:-40px -93px;}
.vip-equity__btm-item-inner {height:73px; position:relative;}
.vip-equity__btm-item-lock, .vip-equity__btm-item-logo {position:absolute;}
.vip-equity__btm-item-logo {top:0;width:80px;height:72px;}
.vip-equity__btm-item-lock {bottom:0;width:46px;height:46px;}
.vip-equity__btm-item-lock, .vip-equity__btm-item-logo {background-size:422px 1176px;}
.vip-equity__btm-item-locknoar, .vip-equity__btm-item-logonoar {background-image:url(../images/user/vip-icon.png);}
.vip-equity__btm-item-logo {left:0;}
.vip-equity__btm-item-lock {right:0;}
.vip-equity__btm-item .vip-icon__1 {background-position:-34px -905px;}
.vip-equity__btm-item .vip-icon__2 {background-position:-124px -905px;}
.vip-equity__btm-item .vip-icon__3 {background-position:-214px -905px;}
.vip-equity__btm-item .vip-icon__4 {background-position:-304px -905px;}
.vip-equity__btm-item .vip-icon__5 {background-position:-34px -987px;}
.vip-equity__btm-item .vip-icon__6 {background-position:-124px -987px;}
.vip-equity__btm-item .vip-icon__7 {background-position:-214px -987px;}
.vip-equity__btm-item .vip-icon__8 {background-position:-304px -987px;}
.vip-equity__btm-item .vip-icon__9 {background-position:-124px -1069px;}
.vip-equity__btm-item .vip-icon__10 {background-position:-214px -1069px;}
.vip-equity__btm-item-lock.vip-icon__lock-s1 {background-position:-356px -54px;}
.vip-equity__btm-item-lock.vip-icon__lock-s2 {background-position:-300px -54px;}
.vip-equity__btm-item-lock.vip-icon__lock-s3 {background-position:-244px -54px;}
.vip-equity__btm-item-txt {position:absolute;top:50%;color:#9b9b9b;font-weight:700;}
.vip-equity__btm-item-txt {transform:translateY(-50%);}
.vip-equity__btm-item-txt {left:90px;right:8%;}

.vip-exclu-birth__title {background-size: cover;background-repeat: no-repeat;background-position: center top;}
.vip-exclusive .vip-exclu-birth__title {background-image:url(../images/user/bg_exclusive.png);}
.vip-exclu-birth__title h3 {text-transform:none;font-size:28px;line-height:32px;font-weight:700;-webkit-text-fill-color:transparent;}
.vip-exclu-birth__title h3 {margin-bottom:6px;-webkit-background-clip:text;background-clip:text}
.vip-exclu-birth__title h3 {background-image:linear-gradient(171deg,#bb9738,#d6ba7a 44%,#c4993f);}
.vip-exclusive-txt {display:inline-block;padding:0 18px;height:21px;line-height:21px;background-color:#222;border-radius:40px 0 40px 0}
.vip-exclusive-txt span {  background:linear-gradient(171deg,#d8bb49 0,#e9d99b 44%,#cfa346 100%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;font-weight:400;font-size:14px}
.vip-exclusive .btn-dark,
.vip-exclusive .btn-dark:hover,
.vip-exclusive .btn-dark:focus {color: #eec887;font-weight:300;}
.vip-faq .accordion-item {border:0;}
.vip-faq .accordion-button:focus {border:0;box-shadow:0 0 0 0 rgba(13,110,253,0);}
.vip-faq .accordion-button:not(.collapsed) {background-color:#fff;}

.user-content h4.title-cursor span {color:#767676; font-size:14px;font-weight:300;}
.user-content h4.title-cursor .points {color:#ff7300;}
.user-content .active-filters a.active { color:#fff; background-color:#222; }
.deactivate-account .card .an {font-size: 48px;}
.user-content .coupon-tabs li a { font-size:16px; font-weight:600; position:relative; display:block; padding:10px 0 10px; margin-right:48px; background-color:transparent; color:#666; border:none; cursor:pointer; }
.user-content .coupon-tabs li.active a,
.user-content .coupon-tabs li:hover a {font-size: 16px; color: #000; text-decoration:none;}
.user-content .coupon-tabs li a:before {font-size: 16px; content:""; width:0; height:1px; background-color:#444; position:absolute; bottom:-1px; left:0; right:0; }
.user-content .coupon-tabs li.active a:before,
.user-content .coupon-tabs li:hover a:before { width:100%; }
.user-content .coupon-tabs.style2 li a { color:#000; background-color:#eee; padding:10px 25px; margin-right:10px; }
.user-content .coupon-tabs.style2 li.active a,
.user-content .coupon-tabs.style2 li:hover a { background-color: #000;color: #eee; }
.user-content .coupon-tabs.style2 li a:before { content:none; }
.user-content .coupon-tabs.style3 li a { color:#000; background-color:#f2f2f2; border:1px solid transparent; border-bottom:0; padding:10px 25px; margin-right:10px; }
.user-content .coupon-tabs.style3 li.active a,
.user-content .coupon-tabs.style3 li:hover a { background-color:#fff;color:#000; border-color:#e8e9eb; }
.user-content .coupon-tabs.style3 li a:before { background:#e8e9eb; }
.user-content .coupon-tabs.style3 li.active a:before { background:#fff; }
.unused-coupons .tab-slider-product .tabs {text-align:left;}
.unused-coupons .tabs-style3.tabs > li { font-size:12px; line-height:14px; border:1px solid #e5e5e5; padding:6px 12px; }
.unused-coupons .tabs-style3.tabs > li.active { color:#333; border:1px solid #333; background:#fff;  }
.unused-coupons .tabs-style3.tabs li:hover, .tab-slider-product .tabs li:focus { color:#fff; opacity:1; background:#000; }
.coupon-item {position:relative;display:inline-block;vertical-align:top;width:calc(50% - 16px);}
.coupon-item-wrapper {margin-bottom:16px;background:#fff6f3;background:var(--bgColor,#fff6f3);position:relative;-webkit-mask-position:-6px;mask-position:-6px;overflow:hidden; }
.coupon-item-wrapper .coupon-head {border-top:5px solid #fa6338; border-left:.5px solid #ffe2cf;border-left:.5px solid var(--borderColor,#ffe2cf);border-right:.5px solid #ffe2cf;border-right:.5px solid var(--borderColor,#ffe2cf)}
.coupon-item-wrapper .coupon-head {padding:20px 16px 8px;}
.coupon-item-wrapper .coupon-head,
.coupon-item-wrapper .coupon-head h4 {color:#fa6338;}
.coupon-title h4,
.coupon-title p {margin-bottom:0;}
.coupon-title p {font-size: 12px;}
.coupon-link {font-size: 12px;}
.coupon-item-wrapper .coupon-mid-line div {flex:1;height:2px; background-size:8px 2px;background-repeat:repeat-x;}
.coupon-item-wrapper .coupon-mid-line div {background-image:linear-gradient(90deg,#fff 0,#fff 50%,transparent 0);background-image:linear-gradient(90deg,var(--midLineColor,#fff) 0,var(--midLineColor,#fff) 50%,transparent 0);}
.coupon-item-wrapper .coupon-head-wrap {position:relative;}
.wrap_left_top, .wrap_right_top, .wrap_left_bottom, .wrap_right_bottom {position:absolute;width:7px;height:7px;border: 1px solid #fff; z-index: 1;background: #fff;}
.wrap_left_bottom {left:-1px;bottom:-1px;border-radius:0 7px 0 0;border-top:1px solid #ffe2cf; border-right:1px solid #ffe2cf;}
.wrap_right_bottom {right:-1px;bottom:-1px;border-radius:7px 0 0 0;border-top:1px solid #ffe2cf; border-left:1px solid #ffe2cf;}
.wrap_left_top {top:-1px;left:-1px;border-radius:0 0 7px 0;border-bottom:1px solid #ffe2cf; border-right:1px solid #ffe2cf;}
.wrap_right_top {top:-1px;right:-1px;border-radius:0 0 0 7px;border-bottom:1px solid #ffe2cf; border-left:1px solid #ffe2cf;}
.coupon-item-wrapper .coupon-body {position:relative;transition:all .2s;}
.coupon-item-wrapper .coupon-body {padding:7px 16px 12px;}
.coupon-item-wrapper .coupon-body {border-bottom:.5px solid #ffe2cf;border-bottom:.5px solid var(--borderColor,#ffe2cf);border-left:.5px solid #ffe2cf;border-left:.5px solid var(--borderColor,#ffe2cf);border-right:.5px solid #ffe2cf;border-right:.5px solid var(--borderColor,#ffe2cf);}
.coupon-body li {font-size:12px;line-height:14px;color:#666;}
.coupon-item-wrapper .coupon__newly-get {position:absolute;top:6px;z-index:1;width:100px;height:17px;font-size:12px;line-height:17px;color:#fff;}
.coupon-item-wrapper .coupon__newly-get {text-align:center;box-shadow:0 2px 2px rgba(12,62,16,.12);background-color:#3cbd45;}
.coupon-item-wrapper .coupon__newly-get {left:-30px;transform:rotate(-45deg) scale(.8333);}

.expired-coupons .coupon-item-wrapper {margin-bottom:16px;background:#f9f9f9;background:var(--bgColor,#f9f9f9);}
.expired-coupons .coupon-item-wrapper .coupon-head {border-top:5px solid #bbb; border-left:.5px solid #e7e7e7;border-left:.5px solid var(--borderColor,#e7e7e7);border-right:.5px solid #e7e7e7;border-right:.5px solid var(--borderColor,#e7e7e7)}
.expired-coupons .coupon-item-wrapper .coupon-head,
.expired-coupons .coupon-item-wrapper .coupon-head h4,
.expired-coupons .coupon-body li {color:#bbb;}
.expired-coupons .wrap_left_bottom {left:-1px;bottom:-1px;border-radius:0 7px 0 0;border-top:1px solid #e7e7e7; border-right:1px solid #e7e7e7;}
.expired-coupons .wrap_right_bottom {right:-1px;bottom:-1px;border-radius:7px 0 0 0;border-top:1px solid #e7e7e7; border-left:1px solid #e7e7e7;}
.expired-coupons .wrap_left_top {top:-1px;left:-1px;border-radius:0 0 7px 0;border-bottom:1px solid #e7e7e7; border-right:1px solid #e7e7e7;}
.expired-coupons .wrap_right_top {top:-1px;right:-1px;border-radius:0 0 0 7px;border-bottom:1px solid #e7e7e7; border-left:1px solid #e7e7e7;}
.expired-coupons .coupon-item-wrapper .coupon-body {border-bottom:.5px solid #e7e7e7;border-bottom:.5px solid var(--borderColor,#e7e7e7);border-left:.5px solid #e7e7e7;border-left:.5px solid var(--borderColor,#e7e7e7);border-right:.5px solid #e7e7e7;border-right:.5px solid var(--borderColor,#e7e7e7);}
.expired-coupons .btn-dark {color:#e9ecef;}
.expired-coupons .btn-dark:hover, .btn-dark:focus {color: #fff;}

.page-points-title {padding-top:20px;}
.points-program .points-num {text-align:center;padding:15px 0;background:#fafafa;}
.points-program .points-num .col-12 > div {padding:12px 0;}
.points-program .points-num .total-point .num {font-size:64px;color:#fa6338;font-weight:700;line-height:74px;}
.points-program .points-num .expire-point .num {font-size:36px;color:#fa6338;font-weight:700;line-height:69px;}

.orders-program .input-text.rounded-0 {border-radius:3px 0 0 3px !important;}
.orders-program .coupon-tabs li a {font-size:14px; font-weight:400; position:relative; display:block; padding:10px 0 0; margin-right:24px; background-color:transparent; color:#444; border:none; cursor:pointer; }
.orders-program .coupon-tabs li.active a,
.orders-program .coupon-tabs li:hover a {font-size: 14px;color:#000;text-decoration:none;}
.orders-program .coupon-tabs li a:before {font-size: 14px; content:""; width:0; height:1px; background-color:#444; position:absolute; bottom:-1px; left:0; right:0; }
.orders-program .coupon-tabs li.tab-end a {font-size:12px; font-weight:300;color:#999;padding:13px 0 0;}

.wishlist-program .product-wishlist .filters-toolbar-wrapper {margin-top:30px;padding:10px;background:#f6f6f6;}

/*.table > :not(caption) > * > * {padding:20px;}*/


    /* My Account page */

.myaccount-page,
.myaccount-page .breadcrumbs-wrapper { background: rgb(247, 248, 250); }
.myaccount-page .sidebar-wrapper { background: #fff; }
.dashboard-upper-info { border-bottom:1px solid #ebebeb; border-top:1px solid #ebebeb; margin-bottom:40px; }
.dashboard-upper-info p { font-size:14px; margin-bottom:0; }
.dashboard-upper-info .d-single-info { font-size: 15px; border-right:1px solid #ebebeb; padding:20px; }
.dashboard-upper-info [class*="col-"]:last-child .d-single-info { border-right:0; }
.dashboard-upper-info .d-single-info .icon { font-size: 17px; }
.dashboard-list li a { border-bottom:1px solid #ebebeb; color:#000; display:block; font-size:14px; font-weight:500; padding:10px 15px; text-transform:uppercase; text-decoration: none; }
.dashboard-list li a.active, .dashboard-list li a:hover, .dashboard-list li a:focus { color:#fed925; background-color:#000; }
.dashboard-content { height:100%; }
.dashboard-content .tab-pane-wrapper { background: #fff; padding: 20px; }
.dashboard-content h3 { font-size:16px; line-height:24px; text-transform:uppercase; font-weight:600; letter-spacing:0.8px; }
.dashboard-content p { margin:0; }
.product-order .table tbody tr td a:hover { text-decoration:underline; }
.dashboard-content .billing-address {  font-size:14px; font-weight:600; line-height:normal; text-transform:uppercase; margin:15px 0 10px; }
.dashboard-content .address .view:hover { text-decoration:underline; }
.dashboard-content .profile-img { display:flex; align-items:center; margin:0 0; background-color:rgb(255, 255, 255); box-shadow:rgba(3, 0, 71, 0.09) 0px 1px 3px; border-radius:8px; padding:1rem; }
.dashboard-content .profile-img .lbl { margin-left:auto; text-align:right; letter-spacing:0.2em; color:rgb(125, 135, 156); }
.dashboard-content .profile-order { list-style:none; display:flex; flex-wrap:wrap; text-align:center; margin:0; padding:0; }
.dashboard-content .profile-order li { background-color:rgb(255, 255, 255); box-shadow:rgba(3, 0, 71, 0.09) 0px 1px 3px; border-radius:8px; padding:1rem; -webkit-flex:1; flex:1;  margin:0 0.4rem 0.4rem; font-size:12px; line-height:1.5; }


.tracking-detail ul li { display: -webkit-box; display: -ms-flexbox; display: flex; margin-bottom: 5px; }
.tracking-detail ul li .left { min-width: 220px; text-transform: capitalize; font-weight: 600; }
.tracking-detail ul li .right { color: #555; }
.tracking-map iframe { border: 5px solid #fff; -webkit-box-shadow: 0px 5px 15px rgba(0,0,0,0.09); box-shadow: 0px 5px 15px rgba(0,0,0,0.09); border-radius: 8px; }
.tracking-steps .step { text-align: center; margin: 0 5px 8px; padding: 10px 10px 10px 30px; min-width: 180px; position: relative; background-color: #f0f0f0; min-height: 40px; -webkit-transition: background-color 0.2s ease; transition: background-color 0.2s ease; }
.tracking-steps .step span { position: relative; font-size: 13px; font-weight: 500; text-transform: capitalize; }
.tracking-steps .step:before, .tracking-steps .step:after { content:" "; position: absolute; top: 0; right: -17px; width: 0; height: 0; border-top: 20px solid transparent; border-bottom: 20px solid transparent; border-left: 17px solid #f0f0f0; z-index: 2; -webkit-transition: border-color 0.2s ease; transition: border-color 0.2s ease; }
.tracking-steps .step:before { right: auto; left: 0; border-left: 17px solid #fff; z-index: 0; }
.tracking-steps .step:first-child { border-top-left-radius: 4px; border-bottom-left-radius: 4px; }
.tracking-steps .step:first-child:before { border: none; }
.tracking-steps .step.done { color: #222; background-color: #d7d7d7; }
.tracking-steps .step.done:after { border-left: 17px solid #d7d7d7; }
.tracking-steps .step.current { color: #fff; background-color: #eb5c3c; }
.tracking-steps .step.current:after { border-left: 17px solid #eb5c3c; }



/* Contact Page */
.mailsendbtn { display: inline-block; position: relative; }
.mailsendbtn .loading { display: none; position: absolute; right: -40px; top: 6px; width: 28px; height: 28px; box-shadow: 0 0 3px #999; text-align: center; }
.error_msg { display: block; color: #fe254a; }
.contactus-page .contact-inner { max-width: 550px; padding: 40px 55px; margin: 0 auto; }
.contact-pstyle2 .collection-hero .collection-hero__image { height: 210px; background-image:url(../images/about/aboutus-3.jpg); }
.contact-pstyle2 .collection-hero .collection-hero__title { font-size: 40px; font-weight: 600; margin-bottom: 5px; text-transform: none; }
.contact-pstyle2 .contact-details { padding: 30px; margin-top: 20px; }
.contact-pstyle2 .map-section { height: 350px; margin-top: 50px; }



/* Social Icon */
.site-footer__social-icons li { padding:0 10px; }
.social-icons .icon { color:#222; font-size:16px; }
.site-footer__social-icons .icon { width:16px; }

/* FAQ's Style 1 */
.faqs-style1 .accordion .accordion-item { border:0; margin:0; }
.faqs-style1 .accordion .accordion-header:before { display:none; }
.faqs-style1 .accordion .accordion-header .accordion-button:after { content:"\eafb"; font-family:'annimex-icons'; position:absolute; top:50%; right:0px; background: transparent; width: auto; height: auto; transform:translateY(-50%); font-size: 13px; font-weight: normal; }
.faqs-style1 .accordion .accordion-header .accordion-button[aria-expanded="true"]:after { content:"\ead8"; }
.faqs-style1 .accordion .accordion-header .accordion-button { background: transparent; color:#444; position:relative; font-size: 16px; font-weight: 600; padding: 18px 40px 18px 0; border: none; border-bottom: 1px solid #e7e7e7; box-shadow: none; outline: none; }
.faqs-style1 .accordion .accordion-body { padding: 1rem 0 0; }
.faqs-style1 .accordion .collapse.show { margin:0; } 


/* FAQ's Style 2 */
/*
.faq-page .faqttl { background: #333; color: #e7e7e7; font-weight: 500; padding: 9px 20px; margin: 15px 0 5px; border-radius: 5px; }
.faq-style2 .panel-title { position: relative; font-size:105%; text-transform:uppercase; background:none; padding:14px 0 14px 0; margin:0; border-bottom:1px solid #e7e7e7; }
.faq-style2 .panel-content { padding:14px 0; }

 */

/* FAQ's Style 3 */
/*
.faq-agldown-style .accordion-button { position: relative; color: #444; letter-spacing: normal; padding: 18px 0; margin: 0; background-color: transparent; border-bottom: 1px solid #e7e7e7; box-shadow: none; }
.faq-agldown-style .accordion-button:after { content: "\eb66"; font: normal normal normal 20px/1 annimex-icons; font-size: 20px; background: none; height: auto; width: auto; }

 */

/* Coming soon page */
.coming-soon-page { background: url(../images/coming-soon.jpg) no-repeat center center/cover #eee; }
.password-modal .modal-dialog { background: url(../images/coming-soon.jpg) no-repeat center center/cover #eee; }
.password-header { color: #111; background-color: rgba(255, 255, 255,0.7); }
.password-header .clr-header { padding: 10px 0; min-height: 90px; display: flex; justify-content: space-between; align-items: center; margin: 0 auto; }
.password-main .passCnt { position: relative; z-index: 2; padding: 50px 0; max-width: 600px; margin: auto; }
.password-main .password__title { color: #fe254a; font-size: 67px; text-transform: none; font-weight: 700; line-height: 1.1; margin-bottom: 40px; }
.password-main .pwd-timer > span { font-size: 13px; line-height: 1.2; border-radius: 12px; text-transform: uppercase; font-weight: 600; padding: 10px 12px; color: #444; background: #f9f9f9; border: 1px dashed #fe254a; margin: 0 4px; min-width: 16px; display: block; }
.password-main .time-count { font-size: 22px; font-weight: 700; min-width: 40px; display: block; }
.password-main .input-group, .password-modal .input-group { max-width: 400px; margin-left: auto; margin-right: auto; }
.password-footer { color: #111; padding: 20px 15px; background: rgba(255,255,255,0.7); }
.password-modal.modal .modal-dialog { transform:none; transition:none; }
.password-modal .modal__close { padding: 0; position: fixed; top: 20px; right: 20px; z-index: 5; background-image: none; border: 0 !important; opacity: 1 !important; width: 43px; height: 40px; }
.password-modal .modal__close .icon { font-size:18px; }
.coming-soon-page .modal-backdrop { display:none; }

/* Wishlist Page & Compare page */
.text-in-stock { color:#090; text-transform:uppercase; font-weight:600; }
.text-out-stock { color:#DD0101; text-transform:uppercase; font-weight:600; }
.btn.remove-icon { position:absolute; right:3px; top:-10px; z-index:1; height:22px; width:22px; line-height:22px; text-align:center; padding:0; border-radius:50%; }
.btn.remove-icon .icon { font-size:12px; margin-left:1px; }

.compare-page table, .wishlist-table table { margin-bottom: 0; }
.compare-page .table th { background-color:#f8f9fa; min-width:130px; }
.compare-page .table .featured-image { max-width:185px; margin:0 auto; display:block; }
.compare-page2 .table .featured-image { max-width:215px; }
.compare-page2 .btn.remove-icon { top:8px; right:8px; }

/* LookBook Pages */
/*.lookbook { margin:0 -5px; }*/
/*
.lookbook .lookbook-item { position:relative; overflow:visible; }
.lookbook .lookbook-item.gallery { overflow:hidden; }
.lookbook .zoom-img { text-align:center; border-radius:2px; display:inline-flex;align-items:center;justify-content:center; width:35px; height:35px; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; cursor:pointer; position:absolute; left:10px; top:10px; z-index:9; background-color:#fff; box-shadow:0 0 3px rgba(0,0,0,0.15); }
.lookbook .zoom-img:hover { background-color:#222; color:#fff; }
.lookbook .zoom-img:hover .an { color:#fff; }
.lookbook .zoom-img .an { color:#222; font-size:15px; }
.lookbook .grid-lookbook img { width:100%; display:block; margin:0 auto; }
.lookbook .lookbook-caption { background:#fdfdfd; font-size:20px; text-align:center; bottom:-100px; position:absolute; left:0; right:0; margin:0; padding:12px 18px; border-radius:0 0 6px 6px; transition-duration:0.5s; -webkit-transition-duration:0.5s; }
.lookbook .lookbook-caption a { color:#222; }
.lookbook .lookbook-caption .text-1 { font-size:15px; }
.lookbook .lookbook-caption .text-2 { font-size:13px; display:block; }
.lookbook .grid-lookbook:hover .lookbook-caption { bottom:0; }
.lookbook-shop-page .lookbook-item.gallery { overflow:visible; }
.lookbook-shop-page .lookbook .grid-lookbook { z-index:inherit; }
.lookbook .lookbook-item:hover img { transform:none; -webkit-transform:none; -ms-transform:none; }
@media only screen and (min-width:992px) {
    .lookbook .zoom-img { opacity: 0; visibility: hidden; -webkit-transform: translateZ(0) scale(0.1); transform: translateZ(0) scale(0.1); }
    .lookbook .grid-lookbook:hover .zoom-img { opacity: 1; visibility: visible; -webkit-transform: translateZ(0) scale(1); transform: translateZ(0) scale(1); }
}

 */

/* Lookbook Style 2 */
/*
.grid-lookbook.style2 .lookbook .lookbook-caption { background:rgba(0,0,0,0.77); }
.grid-lookbook.style2 .lookbook .lookbook-caption a { color:#fff; }
.grid-lookbook.style3 .lookbook .lookbook-caption { background:rgba(253,253,253,0.88); display:flex; align-items:center; justify-content:center; }
.grid-lookbook.style3 .lookbook .grid-lookbook:hover .lookbook-caption { top:0; bottom:0; }
.grid-lookbook.style4 .lookbook .lookbook-caption { background:rgba(255,255,255,0.88); }

 */

/* Lookbook Style 5 */
/*
.lookbook5 .grid-lookbook .insta-share { transition: transform 0.2s ease-out, opacify 0.2s ease-out; background: rgba(0,0,0,0.7); color: #fff; border-radius: 50%; padding: 15px; position: absolute; top: 50%; left: 33%; }
.lookbook5 .grid-lookbook .insta-share:hover { background:rgba(0,0,0,0.9); }
.lookbook5 .grid-lookbook .insta-shop { position: absolute; background-color: rgba(0,0,0,0.3); color: #fff; padding: 8px; border-radius: 50%; font-size: 12px; box-shadow:0 0 2px 0 rgba(255,255,255,0.5); -webkit-transition: all .225s .01s ease-out; transition: all .225s .01s ease-out; bottom: 3%; right: 3%; }
.lookbook5 .grid-lookbook .insta-shop:hover { background-color:rgba(0,0,0,0.9); }
.lookbook .products .btn-shop { transition: transform 0.2s ease-out, opacify 0.2s ease-out; backface-visibility: hidden; margin: 5px; cursor: pointer; background: #fff; color: #212529; border-radius: 50%; height: 24px; width: 24px; display: flex; align-items: center; justify-content: center; box-shadow:0 1px 1px 0 rgba(0,0,0,0.1); }
.lookbook .products .btn-shop:before { content: ""; position:absolute; -webkit-animation: box-shadow 1.5s linear infinite; animation: box-shadow 1.5s linear infinite; left:auto; top:auto; border-radius:50%; height:24px; width:24px; color:rgba(0,0,0,0.3); }
.lookbook .products .btn-shop .icon { font-size:12px; }
.lookbook .grid-lb { visibility:hidden; z-index:2; box-shadow: 0 0 2px #bbb; -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; opacity:0; display:none; position:absolute; width:260px; background:#fff; border-radius:0; padding:10px; }
.lookbook .grid-lb.active { opacity:1; visibility:visible; display: flex; }
.lookbook .grid-lb.left { right:-100%; }
.lookbook .grid-lb .btn-shop-close { background: #131313; color: #fff; position: absolute; right: -8px; top: -8px; z-index: 5; cursor: pointer; width: 20px; height: 20px; border-radius: 4px; display: flex; justify-content: center; align-items: center; box-shadow: 0 0 2px rgba(255, 255, 255,0.5); }
.lookbook .grid-lb .btn-shop-close .icon { font-size: 13px; }
.lookbook .grid-lb .pro-img { width:80px; }
.lookbook .grid-lb .detail .title { font-size:13px; font-weight:500; line-height:1.2; margin-bottom:0; display:block; }
.lookbook .grid-lb .detail .btn { margin-top:5px; font-size:12px; padding:6px 10px; border-radius:4px; opacity: 1; visibility: visible; }
@keyframes box-shadow { 0%, 100% { -webkit-box-shadow:0 0 0 0; -moz-box-shadow:0 0 0 0; box-shadow:0 0 0 0; } 50% { -webkit-box-shadow:0 0 0 4px; -moz-box-shadow:0 0 0 4px; box-shadow:0 0 0 4px; } }

.position1 { top:25%; left:45%; }
.position2 { top:8%; left:29%; }
.position3 { top:56%; left:20%; }
.position4 { top:5%; left:27%; }
.position5 { top:27%; left:32%; }

.look-position1 { bottom:15%; left:45%; }
.look-position2 { bottom:25%; right:55%; }
.look-position3 { top:38%; left:25%; }
.look-position4 { bottom:10%; left:35%; }
.look-position5 { bottom:30%; left:42%; }
.look-position6 { top:10%; left:62%; }
.look-position7 { top:60%; right:27%; }
.look-position8 { top:50%; left:25%; }
.look-position9 { top:20%; right:55%; }
.look-position10 { bottom:50%; right:56%; }
.look-position11 { top:8%; left:24%; }
.look-position12 { bottom:30%; left:40%; }
.look-position13 { bottom:30%; left:50%; }
.look-position14 { top:20%; left:65%; }

.custom-text-masonry-item { float:left; margin-bottom:10px; }
.custom-text-masonry-item .btn { text-decoration:none !important; padding:10px 20px; }

 */

/* Brands Page */
/*
.letter-title { color:#000; font-size:16px; font-weight:600; background-color:#efefef; padding:6px 20px; margin:20px 0; }
.brands-list .brands-row { margin-top: -10px; }
.brands-list .brands-logo { padding-top: 10px; }
.brands-list .brands-logo a { display:block; border: 1px solid #ddd; padding: 0; }
.brands-list .brands-logo a:hover { border-color:#000; }
.brands-search .alphaBets { background-color:#efefef; padding:10px; }
.brands-search .alphaBets .listing { list-style:none; padding:0; margin:0; }
.brands-search .alphaBets .listing li { display:inline-block; margin:0 10px 0 0; }
.brands-search .alphaBets .listing li .alpha { border:0; background-color:#fff; padding:3px 11px; border:1px solid transparent; }
.brands-search .alphaBets .listing li .alpha.active { color:#fff; background-color:#000; border:1px solid #000; }
.brands-search .alphaBets .listing li .alpha:not(.active):hover { color:#000; background-color:#fff; border-color:#000; }
.brands-search-logo { border:4px solid #efefef; padding:15px; margin-bottom:40px; }

.filterbrand a { font-weight:600; width: auto; min-width: 42px; padding: 8px; background-color: #efefef; border: 1px solid #fff; margin-left: -1px; flex: 1 0 0%; }
.filterbrand a:hover { box-shadow: 0 0 8px rgba(0,0,0,.1); }
.brandList .ttl { width: 100px; font-size: 20px; font-weight: 700; text-transform: lowercase; }
.brandList .list, .brandList .ttl { padding: 15px 10px; border-top: 1px solid #eee; }
.brandList ul a { display: inline-block; padding: 5px; }
.brandList ul a:hover { font-weight:500; }

.home-instagram .instafeed .insta-img { padding:0; float:left; }
.home-instagram .instafeed .insta-img a { position:relative; padding-bottom:100%; display:block; overflow:hidden; }

 */

/* My Account Pages */
.login-register { max-width: 900px; margin: 0 auto; }
.login-register .inner { width: 100%; height: 100%; display: table; border: 1px solid #f2f2f2; padding: 40px; }
.login-page .box h3, .register-page .box h3 { font-size:16px; font-weight:600; text-transform:uppercase; margin:0 0 20px; }

#register-popup, #login-popup { text-align:center; background:#fff; margin:0 auto; padding:20px; max-width:480px; position:relative; }
#register-popup .register-popup-customeremail, #login-popup .login-popup-customeremail { background-color: #f2f2f2; color: #222; }

/*-----------------------------------------
* Login Page Style 2
* -----------------------------------------*/
.login-wrapper .login-inner { position: relative; background: #f8f9fa; width: 100%; /*max-width: 600px;*/ margin: 0 auto; padding: 30px; overflow: hidden; text-align: center; }
.login-wrapper .user-form-login { -webkit-transition: opacity .5s ease, transform .5s ease; transition: opacity .5s ease, transform .5s ease; -webkit-transform: translateX(-400px); transform: translateX(-400px); opacity: 0; visibility: hidden; }
.login-wrapper .user-form-login.login-active { -webkit-transform: translateX(0px); transform: translateX(0px); opacity: 1; visibility: visible; }
.login-wrapper .user-form-forgot { position: absolute; top: 83px; left: 400px; opacity: 0; visibility: hidden; -webkit-transition: all .5s ease; transition: all .5s ease; width: 100%; padding: 0 30px; }
.login-wrapper .user-form-forgot.forgot-active { -webkit-transform: translateX(-399px); transform: translateX(-399px); visibility: visible; opacity: 1; }
.login-wrapper .user-form-signup { position: absolute; top: 83px; left: 400px; opacity: 0; visibility: hidden; -webkit-transition: all .5s ease; transition: all .5s ease; width: 100%; padding: 0 30px; }
.login-wrapper .user-form-signup.signup-active { -webkit-transform: translateX(-399px); transform: translateX(-399px); visibility: visible; opacity: 1; }
.login-wrapper .login-inner.signup-active { height: 485px; }
.login-wrapper .login-inner.signup-active .socialbottom { -webkit-transform: translateX(150px); transform: translateY(150px); opacity: 0; visibility: hidden; }
.login-wrapper .user-registered { position: absolute; top: 70px; left: -400px; opacity: 0; visibility: hidden; -webkit-transition: all .5s ease; transition: all .5s ease; width: 100%; padding: 0 30px; }
.login-wrapper .user-registered.registered-active { -webkit-transform: translateX(399px); transform: translateX(399px); visibility: visible; opacity: 1; }
.login-wrapper .user-registered .successtext { margin-top: -15px; }
.login-wrapper .user-registered .check path { stroke: #fff; stroke-linecap:round; stroke-linejoin:round; stroke-width: .85px; stroke-dasharray: 60px 300px; stroke-dashoffset: -166px; fill: rgba(0,0,0,.5); -webkit-transition: stroke-dashoffset 2s ease .5s, fill 1.5s ease 1.0s; transition: stroke-dashoffset 2s ease .5s, fill 1.5s ease 1.0s; }
.login-wrapper .user-registered .check.checked path { stroke-dashoffset: 33px; fill: rgba(0,0,0,.9); }
.login-wrapper .use-logined { position: absolute; top: 88px; left: 400px; opacity: 0; visibility: hidden; -webkit-transition: all .5s ease; transition: all .5s ease; width: 100%; padding: 0 30px; }
.login-wrapper .use-logined.logined-active { -webkit-transform: translateX(-399px); transform: translateX(-399px); visibility: visible; opacity: 1; }
.login-wrapper .use-forgoted { position: absolute; top: 70px; left: -400px; opacity: 0; visibility: hidden; -webkit-transition: all .5s ease; transition: all .5s ease; width: 100%; padding: 0 30px; }
.login-wrapper .use-forgoted.forgoted-active { -webkit-transform: translateX(399px); transform: translateX(399px); visibility: visible; opacity: 1; }
.login-wrapper .use-forgoted .successtext { margin-top: -15px; }
.login-wrapper .use-forgoted .check path { stroke: #fff; stroke-linecap:round; stroke-linejoin:round; stroke-width: .85px; stroke-dasharray: 60px 300px; stroke-dashoffset: -166px; fill: rgba(0,0,0,.5); -webkit-transition: stroke-dashoffset 2s ease .5s, fill 1.5s ease 1.0s; transition: stroke-dashoffset 2s ease .5s, fill 1.5s ease 1.0s; }
.login-wrapper .use-forgoted .check.checked path { stroke-dashoffset: 33px; fill: rgba(0,0,0,.9); }
.login-wrapper .login-inner .btn-link { display: inline-flex; }

.socialbottom { -webkit-transition: opacity .5s ease, transform .5s ease; transition: opacity .5s ease, transform .5s ease; }
.socialbottom .btn-social .btn { position: relative; padding: 8px 8px 8px 32px; min-height: 40px; }
.socialbottom .btn-social .btn > :first-child { position: absolute; left: 0; top: 0; bottom: 0; width: 33px; font-size: 14px; text-align: center; border-right: 1px solid rgba(0,0,0,0.2); display: flex; align-items: center; justify-content: center; }
.socialbottom .btn-twitter { color: #fff; background-color: #55acee; border-color: rgba(0,0,0,0.2); }
.socialbottom .btn-twitter:hover { color: #fff; background-color: #2795e9; border-color: rgba(0,0,0,0.2); }
.socialbottom .btn-facebook { color: #fff; background-color: #3b5998; border-color: rgba(0,0,0,0.2); }
.socialbottom .btn-facebook:hover { color: #fff; background-color: #2d4373; border-color: rgba(0,0,0,0.2); }
.socialbottom .btn-google { color: #fff; background-color: #dd4b39; border-color: rgba(0,0,0,0.2); }
.socialbottom .btn-google:hover { color: #fff; background-color: #c23321; border-color: rgba(0,0,0,0.2); }

.form-slider { border-radius: 4px; -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2); box-shadow: 0px 0px 10px rgba(0,0,0,0.2); }
.form-slider .slideshow, .form-slider .slideshow * { height: 100%; }
.form-slider .slideshow .slick-arrow { display: none !important; }
.form-slider .slideshow .slick-dots { height: auto; }
.form-slider .slideshow .wrap-caption { background-color:rgba(255,255,255,.88); }

.page-login .login-page-container { padding-bottom: 100px; min-height:767px; }
.page-login .btn-outline-light { color:#222; background: #f6f6f6; font-size: 11px; font-weight: 300; text-transform: inherit; border: 0; border-radius:20px;  }
.page-login .btn-outline-light:hover { color:#222; background: #f6f6f6; }
.page-login .privacy-tips a { color: #2d68a8; font-size: 12px; }

.tab-slider-page .tabs { border:0; text-align:center; padding:0; }
.tab-slider-page .tabs > li { list-style-type: none;float:none; cursor:pointer; }
.tab-slider-page .tabs > li .tab-item { background:#f8f9fa; border:0; text-transform:none; color:#000; font-weight:400; font-size:14px; border-radius:5px; }
.tab-slider-page .tabs > li.active .tab-item { color:#333; background:#fed925; }
.tab-slider-page .tabs > li .tab-item:hover, .tab-slider-product .tabs > li .tab-item:focus {opacity:1; background:#fed925; }
.tab-slider-page .tab_container { clear:both; width:100%;}
.tab-slider-page .tab_content { display:none; }
.tab-slider-page .tab_drawer_heading { display:none; }


    /*======================================================================
      19. Blog Pages
    ========================================================================*/
.loadmore-post { text-align:center; }
/*.blog--grid-load-more .article { display:none; }*/
.blog-list-view .article { padding:0 0 25px; margin-bottom:25px; border-bottom:1px solid #e8e9eb; }
.blog-grid-view .article { margin-bottom:25px; }
.no-border .article { border-bottom:0 !important; padding-bottom:0 !important; }

.custom-search .input-group .input-group__field, 
.custom-search .input-group .btn { height: 45px; }

.article_featured-image { display:-webkit-box; display:-moz-box; display:-ms-flexbox; display:-webkit-flex; display:flex; align-items:center; justify-content:center; /*min-height:140px;*/ margin-bottom:20px; }
.article_featured-image img {  }
.blog-list-view .article_featured-image img {  }

.publish-detail { margin:0 0 10px 0; }
.publish-detail li { list-style:none; display:inline-block; }
.publish-detail > li:after { content:'|'; display:inline-block; padding:0 10px; vertical-align:middle; }
.publish-detail > li:last-of-type:after { content:none; }
.publish-detail .icon { font-size:13px; margin-right:8px; }

.featured-content .list-items { margin-left:10px; }
#comment_form { background:#faf9f9; padding:45px 50px 50px; margin-top:30px; }

.tags-clouds li { display:inline-block; margin-bottom:6px; margin-right:6px; }
.tags-clouds li a { display:block; border:1px solid #ddd; padding:5px 9px !important; text-transform:uppercase; border-radius:4px; }
.tags-clouds li a:hover { background-color:#efefef; }

.blog-single-page .article blockquote { background:#f8f8f8; font-size:13px; font-style:normal; font-weight:600; padding:20px; margin:20px 0; }
.blog-single-page .comment__avatar { width:75px; margin-right:20px; }
.blog-single-page .comments-list--level--1 { border-top: 1px solid #ebebeb; margin-top:20px; padding-top:20px; }
.blog-single-page .comments-list__item + .comments-list__item { border-top: 1px solid #ebebeb; margin-top:20px; padding-top:20px; }
.blog-single-page .comments-list--level--1 > .comments-list__item { margin-left:40px; }
.blog-single-page .comment__author { font-size:14px; font-weight:600; margin-bottom:10px; }
.blog-single-page .comment__reply .btn { color:#555; height:auto; padding:0; display:block; background-color:transparent; border:0; }
.blog-single-page .comment__reply .btn:hover { color:#2d68a8; }

.blog-single-page .rte .main-col a { color:#2d68a8; }
.blog-single-page .rte a:hover,
.blog-single-page .rte a:active { color:#2d68a8; text-decoration:underline; }

.blog-masonry.shop-fullwidth .btn-filter { font-size:13px; }
.blog-masonry.shop-fullwidth .btn-filter:before { font-size:22px; }

/*======================================================================
  20. Cart Pages
========================================================================*/
.cart-col h5 { font-size:14px; font-weight:600; text-transform:uppercase; margin: 0 0 10px; }
.cart__row { position:relative; }
.cart th { padding:10px; background:#fafafa; }
.cart td { padding:10px; }

.cart__meta-text { color:#222; font-size:11px; font-style:italic; line-height:1.6; padding:5px 0; }
.cart__image-wrapper { width:100px; }
.cart td.cart-delete { width:30px; }

.cart-alert.alert-dismissible .btn-close { padding:12px; background-size:10px; box-shadow:none; }
.cart__footer .solid-border { border:1px solid #e8e9eb; padding:20px; margin-bottom:20px; }
.cart__footer .cart__subtotal-title { text-transform:uppercase; font-size:14px; }
.cart__subtotal { font-weight:700; padding-left:15px; display:inline-block; }
.cart__shipping { font-style:italic; font-size:13px; padding:15px 0; }
.cart_info { padding:20px 25px; background:#fafafa; }

#cartCheckout { width:100%; padding:12px; }
.cart-variant1 .cart .cart__price-wrapper{ text-align:center; }
.cart-variant1 .cart table { border:1px solid #f2f2f2; }
.cart-variant1 .cart table td { border:1px solid #f2f2f2; }
.cart-variant1 .cart th.text-right,
.cart-variant1 .cart .text-right.cart-price { text-align:center !important; }

.qtyField { position:relative; display:flex; align-items:center; width:86px; margin:0; }
.qtyField input { margin:0; padding:0 25px; color:#222; width:86px; height:40px; text-align:center; background:none; border-radius:5px; border:1px solid #ddd; }
.qtyField input:focus { border-color:#b7b7b7; }
.qtyField .qtyBtn { margin:0; padding:8px; color:#222; width:30px; height:40px; display:flex; align-items:center; justify-content:center; text-align:center; background:none; border-radius:0; border:none; position:absolute; left:0; }
.qtyField .qtyBtn:hover { color:#fe254a; }
.qtyField .qtyBtn .icon { font-size:10px; }
.qtyField .qtyBtn.plus { left:auto; right:0; }

.qtyField.sm, .qtyField.sm input { width: 85px; }
.qtyField.sm .qtyBtn, .qtyField.sm .qty { height: 35px; }

.input-group .input-group__field, .input-group > .form-control, .input-group .btn { height: 40px; }
.required, .required-f { color: #fe254a; }

/*======================================================================
  21. Checkout Page Styles
========================================================================*/
.login-title, .order-title, .payment-title { font-size:16px; font-weight:700; }
.customer-box h3 { color:#fff; font-size:13px; font-weight:400; line-height:normal; margin:0; padding:10px 15px; text-transform:uppercase; background-color:#000; }

.order-table .table thead th { background:#fbfbfb; font-size:12px; }
.order-table .table tfoot td { background:#fbfbfb; }
.order-table .table .thumbImg,
.order-table .table .thumb { width:60px; }
.order-table.style1 .table tbody td { font-size:13px; padding:5px 5px; }

.payment-accordion .card { background:transparent; border:1px solid #ddd; border-radius:4px; }
.payment-accordion .card .card-header { background-color:transparent; padding:0; border:none; }
.payment-accordion .card .card-header .card-link { background-color:transparent; color:#000; padding:10px 15px 8px; display:block; font-size:13px; font-weight:600; text-transform:uppercase; width:100%; text-align:left; margin:0; border:none; border-bottom:1px solid transparent; }
.payment-accordion .card .card-header .card-link:hover,
.payment-accordion .card .card-header .card-link[aria-expanded="true"] { border-color:#ddd; color:#f06543; }
.payment-accordion .card .card-header .card-link:before { right:15px; }

.customer-box .discount-coupon, .customer-box .customer-info { background-color:#f7f7f7; padding:20px; }
.create-ac-content, .your-order-payment { border:1px solid #ddd; border-radius:4px; padding:20px; }

.card.card--grey { background-color:#fbfbfb; }
.card { background-color:#fff; border-color:#f7f7f7; border-radius:0; margin-bottom:10px; }
.card-header { position:relative; padding:10px 15px; border-color:#f7f7f7; }
.card-header .card-link { position:relative; text-decoration: none; }
.card-header .card-link:before{ content:"\ea45"; font-family:'annimex-icons'; font-size:14px; position:absolute; right:0; top:50%; transform:translateY(-50%); -webkit-transform:translateY(-50%); }
.card-header .card-link[aria-expanded="true"]:before { content:"\ea48"; }

/*======================================================================
  21.1 Checkout Success Page
========================================================================*/
.checkout-scard { background: #fff; -webkit-box-shadow:0 0 3px rgba(0,0,0,.3); box-shadow:0 0 3px rgba(0,0,0,.3); margin-bottom:30px; }
.checkout-scard .card-body { padding:25px; }
.checkout-scard .card-title { font-size:30px; margin-bottom:15px; }
.checkout-scard .card-icon { color:#198754; font-size:90px; line-height:90px; margin:0 0 10px; }
.checkout-scard .text-order { padding:10px 20px; font-size:13px; ont-weight:400; }
.ship-info-details { margin:0 0 20px; padding:0 0 12px; border:1px solid #ddd; border-radius:4px; }
.ship-info-details h3 { background-color:#fbfbfb; font-size:15px; font-weight:600; padding:10px 15px; margin:-1px 0 15px; border-bottom:1px solid #ddd; border-top:1px solid #ddd; }
.ship-info-details p { padding:0 15px; margin:0 0 5px; }

/*======================================================================
  22. Nesletter Popup Styles
========================================================================*/
.newsletter-section .input-group { position:relative; display:table; width:100%; border-collapse:separate; max-width:500px; margin:0 auto; }
#newsletter-modal { text-align:center; width:90%; position:fixed; left:50%; top:50%; background:#fafafa; color:#111; margin:20px auto; z-index:444; transform:translate(-50%, -50%) scale(0.9); -webkit-transform:translate(-50%, -50%) scale(0.9); -ms-transform:translate(-50%, -50%) scale(0.9); }
#newsletter-modal .newsltr-text { position: relative; padding:30px 30px; }
#newsletter-modal .newsltr-text p.sub-text { max-width:400px; margin:0 auto 20px; font-size:13px; line-height:1.3; }
#newsletter-modal .newsltr-text .btn.mcNsBtn { width:100%; height:42px; }
#newsletter-modal .newsltr-text .social-icons { margin:0 0 20px; }
#newsletter-modal .newsltr-text .social-icons li { display:inline-block; margin:0 5px; list-style:none; }
#newsletter-modal .newsltr-text .social-icons li a { color:#222; display:inline-block; width:26px; height:26px; line-height:26px; text-align:center; }
#newsletter-modal .newsltr-text .social-icons li a:hover { opacity:0.5; }
#newsletter-modal .newsltr-text .social-icons li a .an { font-size:14px; }
#newsletter-modal .newsltr-text #Subscribe { width:100%; border:0; background:#222; color:#fff; }
#newsletter-modal .newsltr-text #Subscribe:hover { opacity:0.9; }
#newsletter-modal .checkboxlink { font-size:10px; text-transform:uppercase; }
#newsletter-modal .wraptext { max-width:350px; margin:0 auto; } 
#newsletter-modal .title { font-size:40px; }
#newsletter-modal.style1 { max-width:500px; }
#newsletter-modal.style1 .newsletter__submit { width:100%; }

/*
#newsletter-modal.style2 { max-width: 650px; }
#newsletter-modal.style2 .newsltr-text { padding: 30px 40px; }
#newsletter-modal.style2 .title { font-size: 33px; line-height: 1.3; }
#newsletter-modal.style2 .checkboxlink label,
#newsletter-modal.style3 .checkboxlink label { padding-top: 2px; }

#newsletter-modal.style3 { max-width: 500px; color: #ff5d86; }
#newsletter-modal.style3 .newsltr-img { background-position: left center; background-size: cover; background-repeat: no-repeat; background-image: url(../images/newsletter-s3-img.jpg); }
#newsletter-modal.style3 .newsltr-text { padding: 60px 50px; margin: 0 auto; }
#newsletter-modal.style3 .wraptext { max-width: 330px; }
#newsletter-modal.style3 .title { font-size: 45px; line-height: 1.2; color: #ff5d86; }
#newsletter-modal.style3 p { font-size: 18px; line-height: 1.4; color: #7d7487; }
#newsletter-modal.style3 .newsltr-text .btn { background-color: #ff5d86; border-color: #ff5d86; height: auto; padding: 10px 30px; font-size: 16px; }
#newsletter-modal.style3 .newsltr-text .btn:hover { background-color: #f77999; border-color: #f77999; }
#newsletter-modal.style3 .newsltr-text .social-icons li a { color: #ff5d86; width: auto; }

 */

.mfp-ready #newsletter-modal { transform:translate(-50%, -50%) scale(1); -webkit-transform:translate(-50%, -50%) scale(1); -ms-transform:translate(-50%, -50%) scale(1); }
#newsletter-modal .mfp-close { color:#fff; font-size:30px; opacity:1; }

.mfp-hide { display:none !important; }
.mfp-zoom-in .mfp-with-anim { opacity:0; transform:scale(0.9); -webkit-transform:scale(0.9); -ms-transform:scale(0.9); transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; -ms-transition:all 0.3s ease-in-out; }
.mfp-zoom-in.mfp-ready .mfp-with-anim { opacity:1; transform:scale(1); -webkit-transform:scale(1); -ms-transform:scale(1); }
.mfp-zoom-in.mfp-removing .mfp-with-anim { opacity:0; transform:scale(0.9); -webkit-transform:scale(0.9); -ms-transform:scale(0.9); }
@-webkit-keyframes ad-spin { 0% { -webkit-transform:rotate(0deg); transform:rotate(0deg); } 100% { -webkit-transform:rotate(359deg); transform:rotate(359deg); } }
@keyframes ad-spin { 0% { -webkit-transform:rotate(0deg); transform:rotate(0deg); } 100% { -webkit-transform:rotate(359deg); transform:rotate(359deg); } }
.ad-spin { animation:ad-spin 1.5s infinite linear; -webkit-animation:ad-spin 1.5s infinite linear; }
@keyframes scaleimg { 0%, 100% { transform:scale(1); -webkit-transform:scale(1); -ms-transform:scale(1) } 50% { transform:scale(1.2); -webkit-transform:scale(1); -ms-transform:scale(1); } }

.featuredContent .d-flex { background-color:#67b0ee; }
.featuredContent .row-text { padding:20px; }
.featuredContent h3 { color:#fff; font-size:22px; margin-bottom:15px; }
.featuredContent p { color:#fff; font-size:18px; margin-bottom:15px; }
.featuredContent .btn { color:#67b0ee; background-color:#fff; }

/*
.featuredContentStyle2 { background-color:#fafafa; }
.featuredContentStyle2.featuredContent .d-flex { color:#000; background-color:#fafafa; }
.featuredContentStyle2.featuredContent h3,
.featuredContentStyle2.featuredContent p { color:#000; }

.section.featuredContentStyle3 { padding:0; color:#000; }
.featuredContentStyle3.featuredContent .container-fluid { padding:0; }
.featuredContentStyle3.featuredContent .d-flex { background-color:#fff; }
.featuredContentStyle3 p { color:#000; font-size:15px; }
.featuredContentStyle3 h3 { color:#000; font-size:28px; }
.featuredContentStyle3 .btn { color:#fff; background-color:#000; }
.featuredContentStyle3 .btn:hover { color:#fff; background-color:#333; }
.featuredContentStyle3 img { width:100%; }
.featuredContentStyle3.featuredContent .row-text { padding:30px 7%; }

 */

/*======================================================================
  23. Footer
========================================================================*/
#site-scroll { color:#000; background:#fff; line-height:35px; cursor:pointer; font-size:13px; width:40px; height:40px; line-height:44px; right:30px; position:fixed; border-radius:50px; text-align:center; transition:all 0.3s ease 0s; -moz-transition:all 0.3s ease 0s; -webkit-transition:all 0.3s ease 0s; bottom:40px; z-index:444; display:none; box-shadow:0 0 1px #ddd; }
#site-scroll:hover { color:#fff; background:#222; }
#site-scroll.textbase { width:auto; height:auto; line-height:normal; position:fixed; right:10px; -webkit-transform:rotate(270deg); transform:rotate(270deg); font-size:10px; bottom:120px; background:transparent !important; box-shadow:none; padding: 0;color: #000 !important;}
#site-scroll.textbase:hover { opacity:0.8; }

.template-index .footer { margin-top:0; }
.footer { margin-top:0px; }
.footer-above { color:#999; background-color:#0f0f0f; padding-top:80px; }
.footer-newsletter .newsletter-input { background:#fff; border-color:#fff; margin-bottom:10px; font-size:13px; }
.footer-newsletter .newsletter-input:focus { border-color:#e7e7e7; }
.footer-newsletter .btn { font-size:15px; padding:0 15px; }
.footer-newsletter .input-group { flex-wrap:inherit; -webkit-flex-wrap:inherit; -ms-flex-wrap:inherit; }
.footer-newsletter i {font-size:12px;}
.footer .social-icons li { margin-right:15px; }
.footer .social-icons li a { color:#fed925; }
.footer .social-icons li i { font-size:30px; }
.footer .social-icons li a:hover { color:#e7e7e7; }
.footer .social-icons li a:before { display:none; }
.ft-payment .bank-img img { margin-right:10px; max-height: 24px;  }

.footer-top { color:#999; background-color:#0f0f0f; padding-top:80px; }
.footer-top hr {margin-bottom:30px; }
.footer .footer-about img { max-width:180px; }
.footer .footer-top .h4 { color:#ddd; font-family:"Retevis-Regular",Helvetica,Tahoma,Arial,serif; font-size:18px; letter-spacing:0.5px; margin:0 0 15px; text-transform: revert; }
.footer .about-us-col img { margin-bottom:20px; }

.footer-links ul { list-style:none; padding:0; margin:0; }
.footer-links li { margin:12px 0; }
.footer-links a { color:#999; font-size:15px; padding: 12px 0; }
.footer-links a:hover { color:#ddd; text-decoration:underline; }
.footer-links a:before { content: ""; display:inline-block; width:0px; height:3px; vertical-align:middle; background-color:#e7e7e7; }
.footer-links a:hover:before { width:4px; margin-right:3px; }

.footer-bottom { color:#e7e7e7; background-color:#0f0f0f; clear:both; }
.footer-bottom .protection img { max-height: 24px; margin-left:15px; }
.footer .payment-icons svg { width:35px; margin:0 3px; }
.footer-contact i { font-size:20px; padding-right:10px; }
.footer-contact p { font-size:15px;}

.footer .social-icons-col ul { list-style:none; padding:0; margin:0; }
.footer .social-icons-col li { margin-bottom:5px; }
.footer .social-icons-col li a { font-size:13px; padding:0; }
.footer .social-icons-col a:before { display:none; }
.footer .social-icons-col i { font-size:13px; width:28px; display:inline-block; text-align:center; }

.newsletterbg { background-position: center; background-color: #fed925; padding: 20px 0; }
.newsletterbg .footer-newsletter .btn { font-size: 13px; }
.ft-newsletter ul { list-style:inside; font-size: 11px; padding:0; margin:0; }

.footer .worldwide a {color:#fed925; font-size:15px; padding: 12px 0;}
.footer .worldwide a:hover { color:#ddd; text-decoration:underline; }
.footer .copytext-col { font-size:12px; color: #999 !important; }
.footer .copytext-col ul { list-style:none; }
.footer .copyright-links > li { display:inline-block; padding:0 10px; margin-bottom:10px; border-right:1px solid #ccc; line-height:12px; }
.footer .copyright-links > li.start { padding-left: 0; }
.footer .copyright-links > li.end { border:0; }
.footer .copyright-links, .footer .copyright-links a { color:#999; text-decoration:underline; }
.footer .copyright-links a:hover { color:#ccc; }
.footer .copyright-links .an { font-size: 12px; }

.rounded-pill-start { border-bottom-left-radius:50rem !important; border-top-left-radius:50rem !important; }
.rounded-pill-end { border-top-right-radius:50rem !important; border-bottom-right-radius:50rem !important; }

.blur-up.lazyloaded { -webkit-filter:blur(0); filter:blur(0); }
.blur-up { -webkit-filter:blur(5px); filter:blur(5px); transition:filter 400ms, -webkit-filter 400ms; -webkit-transition:filter 400ms, -webkit-filter 400ms; }


/*======================================================================
  25. Cookie Popup
========================================================================*/
.cookie-popup { color:#fff; background-color:#000; padding:20px; position:fixed; z-index:1000; max-width:100%; bottom:0; left:0; right:0; text-align:center; }
.cookie-popup .btn { background-color:#fff; color:#000; margin-right:10px; }

/* Cookie Meassage */
#cookie-message{ text-align:center; position:fixed; top:auto; bottom:0; left:0; right:0; margin:0; z-index:99; background-color:#000; color:#fff; padding:10px 0; border-radius:0; border:none; }

/* Christmas Shower */
/*
.falling-snow { position:relative; }
.falling-snow.style1:before {
    background-color: transparent; position: absolute; width: 100%; content: ''; height: 100%; top: 0; left: 0; z-index: 1; pointer-events:none; background-image: url(../images/snowballs-sm.png),url(../images/snowballs-lg.png),url(../images/snowballs-md.png); 
    height:100%; left:0; position:absolute; top:0; width:100%; -webkit-animation:falling-down-snow 10s linear infinite; -moz-animation:falling-down-snow 12s linear infinite; -ms-animation:falling-down-snow 10s linear infinite; animation:falling-down-snow 10s linear infinite; 
}
.falling-snow.style2:before {
    background-color: transparent; position: absolute; width: 100%; content: ''; height: 100%; top: 0; left: 0; z-index: 1; pointer-events:none; background-image: url(../images/snow-1.png),url(../images/snow-2.png),url(../images/snow-2.png); 
    height:100%; left:0; position:absolute; top:0; width:100%; -webkit-animation: snow 10s linear infinite; -moz-animation: snow 10s linear infinite; -ms-animation: snow 10s linear infinite; animation: snow 10s linear infinite;
}
@keyframes falling-down-snow { 0% { background-position:0px 0px, 0px 0px, 0px 0px; } 100% { background-position:0 700px, 0 500px, 0 300px; } }
@keyframes snow { 0% { background-position:0px 0px, 0px 0px, 0px 0px; } 100% { background-position:500px 1000px, 400px 400px, 300px 300px; } }
@-moz-keyframes snow { 0% { background-position:0px 0px, 0px 0px, 0px 0px; } 100% { background-position:500px 1000px, 400px 400px, 300px 300px; } }
@-webkit-keyframes snow { 0% { background-position:0px 0px, 0px 0px, 0px 0px; } 100% { background-position:500px 1000px, 400px 400px, 300px 300px; } }
@-ms-keyframes snow { 0% { background-position:0px 0px, 0px 0px, 0px 0px; } 100% { background-position:500px 1000px, 400px 400px, 300px 300px; } }

 */


/*======================================================================
  26. Vendor Page
========================================================================*/
/*
.vendor-profile-page .tabs-style2.tabs > li { padding: 0 0 10px; }
.vendor-profile-page .tabs-style2.tabs > li:after { bottom: -1px; }
.vendor-profile-page .grid-categorys .category-item .category-title { font-size: 17px; }
.vendor-profile-page .coupon { position: relative; font-size: 14px; text-align: center; padding: 10px 10px; width: 100%; background-color: #f2f2f2; border-radius: 3px; display: inline-block; border: 1px dashed #666; }
.vendor-profile-page .coupon .tooltip-label { top: auto; bottom: -70px; border: 1px solid #575757; background-color: #fff; color: #222; padding: 8px; }
.vendor-profile-page .coupon .tooltip-label:before { bottom: auto; top: -9px; border-top-color: transparent; border-bottom: 5px solid #000; }
.vendor-profile-page .coupon:hover .tooltip-label { opacity: 1; bottom: -60px; visibility: visible; }
.vendor-profile-page .btn.btn-filter:before { margin-right: 6px; }

.main-dashboard .prod-tlt { min-width: 125px; }

.dashboard-links .list-group-item { background-color: #fbfbfb; padding: 10px 20px; font-size: 14px; }
.dashboard-links .list-group-item.active { background-color: #fe877b; color: #fff; border-color: #fe877b; }
.dashboard-links .list-group-item:not(.active):hover { background-color: #eaeaea; color: #222; }
.dashboard-card .icontext .icon { font-size: 24px; color: #fe6d25; width: 45px; height: 45px; position: relative; background-color: #ffebe6; text-align: center; }
.dashboard-card .icontext .text-white,
.dashboard-card .icontext .text {margin-bottom:.25rem;}
.dashboard-card .icontext .text { font-weight: 700; display: block; color: #fff; }
.dashboard-card .icontext .text-sm { font-size: 11px; font-weight: 400; color: #fbfbfb; }
.dashboard-card .card:after { content: ""; width: 90px; height: 90px; position: absolute; right: -15px; top: -50px; background: #fff; border-radius: 50%; opacity: 0.3; }
.dashboard-card .card:before { content: ""; width: 90px; height: 90px; position: absolute; right: -50px; top: -30px; background: #fff; border-radius: 50%; opacity: 0.3; }
.dashboard-card .bg-blue { background-image: linear-gradient(145deg, #3e5ffe 0%, #7189fe 100%); overflow: hidden; }
.dashboard-card .bg-yellow { background-image: linear-gradient(145deg, #fe8f58 0%, #fea071 100%); overflow: hidden; }
.dashboard-card .bg-pink { background-image: linear-gradient(145deg, #fdb8c3, #fe877b); overflow: hidden; }
.dashboard-card .bg-green { background-image: linear-gradient(145deg, #d1afa3 0%, #b6966f 100%); overflow: hidden; }
.dashboard-card .bg-orange { background-image: linear-gradient(145deg, #f9c899, #f79165); overflow: hidden; }
.dashboard-profile .bg-blue { background-image: linear-gradient(145deg, #fe8c82 0%, #fe9a97 100%); padding-bottom: 275px; overflow: hidden; }
.dashboard-profile .bg-blue:after { content: ""; width: 90px; height: 90px; position: absolute; left: -50px; top: -30px; background: #fff; border-radius: 50%; opacity: 0.3; }
.dashboard-profile .bg-blue:before { content: ""; width: 90px; height: 90px; position: absolute; right: -50px; top: -30px; background: #fff; border-radius: 50%; opacity: 0.3; }
.dashboard-profile .img-thumbnail:after { content: ""; width: 90px; height: 90px; position: absolute; left: -50px; bottom: -80px; background: #fff; border-radius: 50%; opacity: 0.3; }
.dashboard-profile .img-thumbnail:before { content: ""; width: 90px; height: 90px; position: absolute; right: -50px; bottom: -80px; background: #fff; border-radius: 50%; opacity: 0.3; }

.form-color-box input { width: 40px; height: 30px; }
.tox .tox-dialog__body-nav { display: none !important; }
.tox .tox-notifications-container { display: none !important; }
.tox.tox-tinymce { border-radius: 6px; border-color: #d7d7d7; }


 */

/* Bootstrap tagsinput */
/*
.bootstrap-tagsinput { display: inline-flex; flex-wrap: wrap; align-items: center; height: auto; min-height: 42px; padding: 4px 10px; width: 100%; font-size: 13px; letter-spacing: 0.02em; box-shadow: none; border-radius: 6px; border: 1px solid #d7d7d7; -webkit-transition: all 0.4s ease-out 0s; transition: all 0.4s ease-out 0s; }
.bootstrap-tagsinput input { border: none; box-shadow: none; outline: none; background-color: transparent; padding: 0 6px; margin: 0; width: auto; height: auto; max-width: inherit; }
.bootstrap-tagsinput .tag { padding: 4px 8px; margin: 0 3px 3px 0; color: #fff; background: #222; border-radius: 4px; font-size: 12px; }
.bootstrap-tagsinput .tag [data-role="remove"] { margin-left: 8px; cursor: pointer; }
.bootstrap-tagsinput .tag [data-role="remove"]:after { content: "x"; padding: 0px 2px; }

 */

.px-120 {
    padding: 0 120px
}

.px-110 {
    padding: 0 110px
}

.px-100 {
    padding: 0 100px
}

.px-90 {
    padding: 0 90px
}

.px-80 {
    padding: 0 80px
}

.px-40 {
    padding: 0 40px
}

.py-120 {
    padding: 120px 0
}

.py-110 {
    padding: 110px 0
}

.py-100 {
    padding: 100px 0
}

.py-90 {
    padding: 90px 0
}

.py-80 {
    padding: 80px 0
}

.pt-0 {
    padding-top: 0
}

.pt-10 {
    padding-top: 10px
}

.pt-20 {
    padding-top: 20px
}

.pt-30 {
    padding-top: 30px
}

.pt-40 {
    padding-top: 40px
}

.pt-50 {
    padding-top: 50px
}

.pt-60 {
    padding-top: 60px
}

.pt-70 {
    padding-top: 70px
}

.pt-80 {
    padding-top: 80px
}

.pt-90 {
    padding-top: 90px
}

.pt-100 {
    padding-top: 100px
}

.pt-110 {
    padding-top: 110px
}

.pt-120 {
    padding-top: 120px
}

.pb-0 {
    padding-bottom: 0
}

.pb-10 {
    padding-bottom: 10px
}

.pb-20 {
    padding-bottom: 20px
}

.pb-30 {
    padding-bottom: 30px
}

.pb-40 {
    padding-bottom: 40px
}

.pb-50 {
    padding-bottom: 50px
}

.pb-60 {
    padding-bottom: 60px
}

.pb-70 {
    padding-bottom: 70px
}

.pb-80 {
    padding-bottom: 80px
}

.pb-90 {
    padding-bottom: 90px
}

.pb-100 {
    padding-bottom: 100px
}

.pb-110 {
    padding-bottom: 110px
}

.pb-120 {
    padding-bottom: 120px
}

.mt-0 {
    margin-top: 0
}

.mt-10 {
    margin-top: 10px
}

.mt-20 {
    margin-top: 20px
}

.mt-30 {
    margin-top: 30px
}

.mt-40 {
    margin-top: 40px
}

.mt-50 {
    margin-top: 50px
}

.mt-60 {
    margin-top: 60px
}

.mt-70 {
    margin-top: 70px
}

.mt-80 {
    margin-top: 80px
}

.mt-90 {
    margin-top: 90px
}

.mt-100 {
    margin-top: 100px
}

.mt-120 {
    margin-top: 120px
}

.mb-0 {
    margin-bottom: 0
}

.mb-10 {
    margin-bottom: 10px
}

.mb-20 {
    margin-bottom: 20px
}

.mb-30 {
    margin-bottom: 30px
}

.mb-40 {
    margin-bottom: 40px
}

.mb-50 {
    margin-bottom: 50px
}

.mb-60 {
    margin-bottom: 60px
}

.mb-70 {
    margin-bottom: 70px
}

.mb-80 {
    margin-bottom: 80px
}

.mb-90 {
    margin-bottom: 90px
}

.mb-100 {
    margin-bottom: 100px
}

.mx-120 {
    margin: 0 120px
}

.mx-110 {
    margin: 0 110px
}

.mx-100 {
    margin: 0 100px
}

.mx-90 {
    margin: 0 90px
}

.mx-80 {
    margin: 0 80px
}

.my-120 {
    margin: 120px 0
}

.my-110 {
    margin: 110px 0
}

.my-100 {
    margin: 100px 0
}

.my-90 {
    margin: 90px 0
}

.my-80 {
    margin: 80px 0
}
@media (max-width: 767px) {

    .c-pd {
        padding: 0 2.8rem
    }

    .s-pd {
        padding: 0 4.8rem
    }

    .px-120 {
        padding: 0 48px
    }

    .px-110 {
        padding: 0 44px
    }

    .px-100 {
        padding: 0 40px
    }

    .px-90 {
        padding: 0 36px
    }

    .px-80 {
        padding: 0 32px
    }

    .px-40 {
        padding: 0 28px
    }

    .py-120 {
        padding: 48px 0
    }

    .py-110 {
        padding: 44px 0
    }

    .py-100 {
        padding: 40px 0
    }

    .py-90 {
        padding: 36px 0
    }

    .py-80 {
        padding: 32px 0
    }

    .py-40 {
        padding: 28px 0
    }

    .pt-10 {
        padding-top: 4px
    }

    .pt-20 {
        padding-top: 8px
    }

    .pt-30 {
        padding-top: 12px
    }

    .pt-40 {
        padding-top: 16px
    }

    .pt-50 {
        padding-top: 20px
    }

    .pt-60 {
        padding-top: 24px
    }

    .pt-70 {
        padding-top: 28px
    }

    .pt-80 {
        padding-top: 32px
    }

    .pt-90 {
        padding-top: 36px
    }

    .pt-100 {
        padding-top: 40px
    }

    .pt-110 {
        padding-top: 44px
    }

    .pt-120 {
        padding-top: 48px
    }

    .pb-10 {
        padding-bottom: 4px
    }

    .pb-20 {
        padding-bottom: 8px
    }

    .pb-30 {
        padding-bottom: 12px
    }

    .pb-40 {
        padding-bottom: 16px
    }

    .pb-50 {
        padding-bottom: 20px
    }

    .pb-60 {
        padding-bottom: 24px
    }

    .pb-70 {
        padding-bottom: 28px
    }

    .pb-80 {
        padding-bottom: 32px
    }

    .pb-90 {
        padding-bottom: 36px
    }

    .pb-100 {
        padding-bottom: 40px
    }

    .pb-110 {
        padding-bottom: 44px
    }

    .pb-120 {
        padding-bottom: 48px
    }

    .mt-10 {
        margin-top: 4px
    }

    .mt-20 {
        margin-top: 8px
    }

    .mt-30 {
        margin-top: 12px
    }

    .mt-40 {
        margin-top: 16px
    }

    .mt-50 {
        margin-top: 20px
    }

    .mt-60 {
        margin-top: 24px
    }

    .mt-70 {
        margin-top: 28px
    }

    .mt-80 {
        margin-top: 32px
    }

    .mt-90 {
        margin-top: 36px
    }

    .mt-100 {
        margin-top: 40px
    }

    .mt-120 {
        margin-top: 44px
    }

    .mb-10 {
        margin-bottom: 4px
    }

    .mb-20 {
        margin-bottom: 8px
    }

    .mb-30 {
        margin-bottom: 12px
    }

    .mb-40 {
        margin-bottom: 16px
    }

    .mb-50 {
        margin-bottom: 20px
    }

    .mb-60 {
        margin-bottom: 24px
    }

    .mb-70 {
        margin-bottom: 28px
    }

    .mb-80 {
        margin-bottom: 32px
    }

    .mb-90 {
        margin-bottom: 36px
    }

    .mb-100 {
        margin-bottom: 40px
    }

    .mx-120 {
        margin: 0 48px
    }

    .mx-110 {
        margin: 0 44px
    }

    .mx-100 {
        margin: 0 40px
    }

    .mx-90 {
        margin: 0 36px
    }

    .mx-80 {
        margin: 0 32px
    }

    .my-120 {
        margin: 48px 0
    }

    .my-110 {
        margin: 44px 0
    }

    .my-100 {
        margin: 40px 0
    }

    .my-90 {
        margin: 36px 0
    }

    .my-80 {
        margin: 32px 0
    }
}
.user-track-order .user-track-order-title::-webkit-scrollbar{
    width:0;
}
.user-track-order .track-order-content {
    margin-top: 30px
}

    .user-track-order .track-order-content h5 {
        text-transform: uppercase;
        font-size: 15px;
        letter-spacing: 1px
    }

        .user-track-order .track-order-content h5 span {
            color: var(--bs-dark)
        }

.user-track-order .track-order-info {
    margin-top: 25px
}

    .user-track-order .track-order-info a {
        background: var(--theme-bg-light);
        padding: 10px 20px;
        border-radius: 10px;
        margin: 0 5px 5px 0
    }

        .user-track-order .track-order-info a span {
            font-weight: 500
        }

.user-track-order .track-order-step {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 30px
}

    .user-track-order .track-order-step .step-item {
        text-align: center;
        padding: 10px;
        flex: 1;
        position: relative;
        z-index: 1
    }

        .user-track-order .track-order-step .step-item::before {
            content: "";
            position: absolute;
            width: 100%;
            height: 5px;
            background: var(--bs-dark);
            left: 0;
            top: 50px;
            z-index: -1
        }

        .user-track-order .track-order-step .step-item:first-child:before {
            width: 50%;
            left: unset;
            right: 0
        }

        .user-track-order .track-order-step .step-item:last-child:before {
            width: 50%
        }

        .user-track-order .track-order-step .step-item.completed::before {
            background: var(--theme-color)
        }

    .user-track-order .track-order-step .step-icon {
        width: 80px;
        height: 80px;
        line-height: 80px;
        text-align: center;
        background: var(--bs-dark);
        color: var(--bs-white);
        font-size: 35px;
        border-radius: 50%;
        margin: 0 auto
    }
    .user-track-order .track-order-step .step-icon .an.icon{
        font-size:35px;
    }

    .user-track-order .track-order-step .step-item.completed .step-icon {
        background: var(--theme-color)
    }

    .user-track-order .track-order-step .step-item h6 {
        margin-top: 15px;
        font-size: 14px
    }

.package-info .package-item {
    margin-right: 10px;
}

    .package-info .package-item .package-item-title {
        color: #999;
    }

@media all and (max-width:767px) {
    .user-track-order .track-order-step {
        flex-wrap: nowrap;
        flex-direction: column
    }

        .user-track-order .track-order-step .step-item {
            display: flex;
            text-align: left;
        }

            .user-track-order .track-order-step .step-item::before {
                width: 5px;
                height: 50%;
                left: 38px;
                top: 53px;
            }

            .user-track-order .track-order-step .step-item:first-child:before {
                width: 5px;
                left: 38px;
                right: 0;
            }

            .user-track-order .track-order-step .step-item:last-child:before {
                display: none;
            }

        .user-track-order .track-order-step .step-icon {
            width: 60px;
            height: 60px;
            line-height: 60px;
            margin: 0 10px 0 0;
        }
}
