@charset "utf-8";
#banner{ position: relative; }
#banner_edit{ width:100%; height:600px; margin: 0 auto; position:relative; overflow:hidden;}
#banner_edit .banner_box{ width:100%; height:100%; position:absolute; top:0; left:0;}
#banner_edit .banner_box .banner_title,
#banner_edit .banner_box .banner_brief{ position:absolute; cursor:default; white-space:pre-wrap; text-align:left;}
#banner_edit .banner_box .banner_mainpic{position: absolute;cursor: default;}
#banner_edit .banner_box .banner_list{ width:100%; height:100%; position:relative; overflow:hidden;}
#banner_edit .banner_box .banner_list.mask_layer:after{width: 100%;height: 100%;position: absolute;left: 0;top: 0;content: '';background-color: rgba(0, 0, 0, 0.3);}
#banner_edit .banner_box .banner_list a{ width:100%; height:100%; display:block; cursor:pointer;}
#banner_edit .banner_box .banner_list .banner_position{ width:100%; height:100%; position:absolute; top:0; left:0; text-align:center;}
#banner_edit .banner_box .banner_list .banner_bg{ width:100%; height:100%; background-repeat:no-repeat; background-size:100% 100%;}
#banner_edit .banner_box .banner_list img{ max-width:100%;max-height: 100%;}
#banner_edit .banner_tab{ width:100%; height:15px; position:absolute; bottom:15px; left:0; text-align:center;}
#banner_edit .banner_tab a{ width:10px; height:10px; margin:0 3px; background:#f1f1f1; display:inline-block; border-radius:5px;}
#banner_edit .banner_tab a.on{ background:#f00;}
#banner_edit .banner_loading{ width:100%; height:100%; position:absolute; top:0; left:0; background:url(../themes-v2/default/images/global/loading_oth.gif) #fff center no-repeat;}
/* Banner图效果3 slidetype3 */
#banner_edit.banner_slidetype3 .banner_slide_mask1{ display:none; width:100%; height:100%; background:#fff; position:absolute; left:0; top:0;}
#banner_edit.banner_slidetype3 .banner_slide_mask2{ display:none; width:100%; height:100%; background:#fff; position:absolute; left:-100%; top:0;}
#banner_edit.banner_slidetype3 .banner_slide_mask2:after{ width:100%; height:100%; content:''; position:absolute; left:0; top:0; background:rgba(0,0,0,0.5);}
#banner_edit.banner_slidetype3 .banner_list{ position:absolute; left:0; top:0;}
/* Banner文字效果 */
.current_edit .banner_title,
.current_edit .banner_brief,
.current_edit .banner_mainpic{border: 2px dashed #4a8bf1;cursor: move !important;}
.current_edit .banner_title.hover,
.current_edit .banner_brief.hover,
.current_edit .banner_mainpic.hover{background-color: rgba(0,0,0,0.3);}
.current_edit .banner_list a{cursor: default !important;}

*[visualstatus=current]{ z-index: 999; }