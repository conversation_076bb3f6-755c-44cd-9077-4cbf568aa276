using Aop.Api.Domain;
using Elasticsearch.Net;
using Entitys;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Mxweb;
using Newtonsoft.Json;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using Org.BouncyCastle.Ocsp;
using System.Net.Http;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Web;
using YseStore.Common;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.IService.Products;
using YseStore.IService.Sales;
using YseStore.IService.Shopping;
using YseStore.Model.RequestModels.Sales;
using YseStore.Model.VM;
using YseStore.Model.VM.Cart;
using YseStore.Model.VM.Payment.Payoneer;
using YseStore.Model.VM.Payment.Paypal;
using YseStore.Model.VM.Sales;
using YseStore.Service.Products;

namespace YseStore.Controllers
{
    public class AjaxController : BaseController
    {
        private readonly ISalesCouponService _salesCouponService;
        private readonly IFlashSaleService _flashSaleService;
        private readonly IProductService _productService;
        private readonly IShoppingCartServices _cartServices;
        private readonly IOpreationActivitiesService _opreationActivities;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ICurrencyService _currencyService;

        // private readonly IStringLocalizer<AjaxController> _localizer;
        private readonly ISaleStickerService _saleStickerService;
        public AjaxController(ISalesCouponService salesCouponService, IFlashSaleService flashSaleService, IProductService productService, IStringLocalizer<OrderController> localizer, IShoppingCartServices shoppingCartServices, IOpreationActivitiesService opreationActivities,IHttpContextAccessor httpContext,ISaleStickerService saleStickerService, ICurrencyService currencyService )
        {
            _salesCouponService = salesCouponService;
            _flashSaleService = flashSaleService;
            _productService = productService;
            _cartServices = shoppingCartServices;
            _opreationActivities = opreationActivities;
            _httpContextAccessor = httpContext;
            _saleStickerService = saleStickerService;
            _currencyService = currencyService;
        }
        //public IActionResult AjaxCoupon()
        //{
        //    return PartialView();
        //}
        [HttpPost("/")]
        public async Task<IActionResult> AjaxAction([FromQuery] string do_action, [FromForm] CouponInfoRequest requestdata)
        
        {
            try
            {
                // await _salesCouponService.SalesCouponLog("P7ASQVWU", 153,userId: 88);
                //await _salesCouponService.GiveCouponAsync(18, "", 0, null);
                // await _flashSaleService.GetCartProductsSales(new Entitys.products(),1,11, "10055", 0,40);

                // var resss= await _salesCouponService.CalcDiscount(new List<Entitys.shopping_cart>());
                if (string.IsNullOrEmpty(do_action) )
                {
                    do_action=Request.Form["do_action"];
                }
                switch (do_action)
                {
                    case "cart.ajax_get_coupon_info":


                        requestdata.order_cid = HttpUtility.UrlDecode(requestdata.order_cid.FromBase64());

                        List<int> idList = new List<int>();
                        List<string> ids = requestdata.order_cid.Split(',').ToList();
                        if (ids.Count == 0)
                        {
                            return Redirect("/cart");
                        }
                        foreach (var id in ids)
                        {
                            var s = int.TryParse(id, out int CId);
                            if (s == false || CId == 0)
                            {
                                return Redirect("/cart");
                            }
                            idList.Add(CId);
                        }

                        //获取用户币种
                        var currency = await _currencyService.GetCurrency(CurrentCurrency);

                        //获取后台币种
                        var defaultCurrency = await _currencyService.GetManageDefaultCurrency();


                        var cartData = new WebApiCallBack<CartDto>();
                        if (CurrentUserId > 0)//在线客户
                        {
                            cartData = await _cartServices.GetCartInfos(CurrentUserId, currency, defaultCurrency, idList);
                        }
                        else
                        {
                            cartData = await _cartServices.GetCartInfos(CurrentUserSessionId, currency, defaultCurrency, idList);
                        }

                        var res = await _salesCouponService.CheckCouponAsync(requestdata.Coupon, cartData.data, requestdata.Price, requestdata.order_discount_price, pOrderCid: requestdata.order_cid, userId: CurrentUserId, sessionId: CurrentUserSessionId, currency: CurrentCurrency);
                        if (res.Item1 && res.Item4.Status == 1)//优惠券可用
                        {
                            return Json(new
                            {
                                ret = 1,
                                msg = new
                                {
                                    status = res.Item4.Status,
                                    cid = res.Item4.Cid,
                                    coupon = res.Item4.CouponCode,
                                    type = res.Item4.DiscountType,
                                    discount = res.Item4.Discount,
                                    cutprice = res.Item3,
                                    end = DateTimeHelper.ConvertUnixTimestampToLocalTime((long)res.Item4.EndTime, "yyyy-MM-dd"),
                                    pro_price = res.Item4.FrontendPrice,
                                    pro_price_currency = res.Item4.BackendPrice,
                                    IsAllowDiscount = res.Item4.IsAllowDiscount,
                                    CouponSource = res.Item4.CouponSource,
                                    CouponSourceId = res.Item4.CouponSourceId,
                                    webcutprice = res.Item2
                                }
                            });
                        }
                        return Json(new { ret = 0, msg = new { status = res.Item4.Status, coupon = res.Item4.CouponCode } });
                        case "action.operation_show":
                        var dValues = Request.Form["id[]"];
                        string page = Request.Form["m"];
                        string enterTime = Request.Form["enterWebTime"];
                        long lastWebTime = string.IsNullOrEmpty(enterTime) ? 0 : long.Parse(enterTime);
                        var arrids = dValues.ToString().Split(',').Select(it => int.Parse(it)).ToArray();
                        var resHost = $"{_httpContextAccessor.HttpContext.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host}";
#if DEBUG
                        var themeName = Environment.GetEnvironmentVariable("Theme");
                        var bodyCss = Environment.GetEnvironmentVariable("BodyCss");
                        var bodyId = Environment.GetEnvironmentVariable("BodyId");

#endif
#if !DEBUG
                    var themeName = AppSettings.GetValue("Theme");
                    var bodyCss = AppSettings.GetValue("BodyCss");
                    var bodyId = AppSettings.GetValue("BodyId");
    
#endif
                        var theme = new { Name = themeName, BodyCss = bodyCss, BodyId = bodyId };
                        
                        var showres = await _opreationActivities.ShowActivities(arrids, page, CurrentLang, CurrentUserId, $"{resHost}/static/themes/{theme.Name}", lastWebTime);
                        return Json(new { ret = 1, msg = showres });
                        case "action.operation_activities_click":
                        var oidstr = Request.Form["OId"];
                        if (!string.IsNullOrEmpty(oidstr))
                        {
                            var activity = await _opreationActivities.GetActiveInfoAsync(int.Parse(oidstr));
                            if (activity != null)
                            {
                                await _opreationActivities.UpdateActivityStatistics(activity.OId, 1);
                                if (!string.IsNullOrEmpty(activity.Coupon) && activity.Category == "coupon")//领取优惠券
                                {
                                    var coupon = await _salesCouponService.GetCouponInfoAsync(activity.Coupon);
                                    if (coupon != null)
                                    {
                                        await _salesCouponService.GiveCouponAsync(CurrentUserId, sales_Coupons: new List<Entitys.sales_coupon> { coupon });
                                    }
                                }
                            }
                        }
                        break;
                        case "action.operation_activities_del":
                        var oidstrs = Request.Form["OId"];
                        int oid = int.Parse(oidstrs);
                        // 1. 从数据库获取活动记录
                        var activitiesRow = await _opreationActivities.GetActiveInfoAsync(oid);
                        if (activitiesRow == null)
                            return NotFound(new { message = "Activity not found" });

                        string category = activitiesRow.Category;
                        string showTime = activitiesRow.ShowTime.ToString();
                        int frequency = activitiesRow.Frequency ?? 0;

                        // 2. 计算 Cookie 过期时间
                        long currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        long expiryTime = currentTime + 86400 * 7; // 7天过期

                        if (category == "minor")
                        {
                            expiryTime = currentTime + frequency;
                            if (frequency == 0) expiryTime = 0;
                        }

                        // 3. 读取并更新 Cookie
                        var cookieValue = Request.Cookies["operActive"];
                        Dictionary<int, long> cookieOperActive = new Dictionary<int, long>();

                        if (!string.IsNullOrEmpty(cookieValue))
                        {
                            cookieOperActive = JsonConvert.DeserializeObject<Dictionary<int, long>>(cookieValue);
                        }

                        cookieOperActive[oid] = currentTime;

                        var cookieOptions = new CookieOptions
                        {
                            Path = "/",
                            Expires = expiryTime == 0 ?
                                null :
                                DateTimeOffset.FromUnixTimeSeconds(expiryTime).DateTime
                        };

                        Response.Cookies.Append("operActive",
                            JsonConvert.SerializeObject(cookieOperActive),
                            cookieOptions);

                        // 4. 更新 Session
                        var sessionOperActive = new OperActiveSession();
                        var sessionOperActivestr = HttpContext.Session.GetString("operActive");
                        if (!string.IsNullOrEmpty(sessionOperActivestr))
                        {
                            sessionOperActive = JsonConvert.DeserializeObject<OperActiveSession>(sessionOperActivestr);
                            // 4.1 按OId移除数据
                            sessionOperActive.Data.Activities.Remove(oid);

                            // 4.2 按类别移除
                            if (sessionOperActive.Data.CategoryMap.ContainsKey(category))
                            {
                                var categoryList = sessionOperActive.Data.CategoryMap[category];
                                categoryList.Remove(oid);

                                if (categoryList.Count == 0)
                                {
                                    sessionOperActive.Data.CategoryMap.Remove(category);
                                }
                                else
                                {
                                    sessionOperActive.Data.CategoryMap[category] = categoryList;
                                }
                            }

                            // 4.3 按 ShowTime 移除
                            if (sessionOperActive.Data.OperaData.ContainsKey(activitiesRow.ShowTime))
                            {
                                var showTimeList = sessionOperActive.Data.OperaData[activitiesRow.ShowTime];
                                showTimeList.Remove(oid);

                                if (showTimeList.Count == 0)
                                {
                                    sessionOperActive.Data.OperaData.Remove(activitiesRow.ShowTime);
                                }
                            }
                        }
                        // 保存更新后的 session 
                        HttpContext.Session.SetString("operActive", sessionOperActive.ToJson());

                        return Json(new
                        {
                            ret = 1,
                            msg = new
                            {
                                OId = oid,
                                Category = activitiesRow.Category,
                                Mode = activitiesRow.Mode
                            }
                        });
                        case "action.operation_activities":
                        var oidstrss = Request.Form["OId"];
                        var email = Request.Form["Email"];
                        if (string.IsNullOrEmpty(email) || !Regex.IsMatch(email,
          @"^[a-z0-9]+[a-z0-9_\.'\-]*@[a-z0-9]+[a-z0-9\.\-]*\.(([a-z]{2,6})|([0-9]{1,3}))$", RegexOptions.IgnoreCase))
                        {
                            return Json(new { ret = 0, msg = "Please fill in the email address correctly!" });
                        }
                        if (!string.IsNullOrEmpty(oidstrss))
                        {
                            var activity = await _opreationActivities.GetActiveInfoAsync(int.Parse(oidstrss));
                            if (activity != null)
                            {
                                await _opreationActivities.UpdateActivityStatistics(activity.OId, 1);

                                var currentTimes = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                                if (activity.EndTime < currentTimes)
                                {
                                    return Json(new { ret = 0, msg = "The event does not exist or has expired" });
                                }
                                var reso=  await _opreationActivities.Subscribe(activity, email);
                                return Json(new { ret = reso.Item3, msg = reso.Item2 });
                            }
                        }
                        break;
                    default:
                        break;
                }
                //if (do_action == "cart.ajax_get_coupon_info")//获取优惠券信息
                //{

                //}
                //else if (do_action== "action.operation_show") {
                   
                //}
                //else if (do_action == "action.operation_activities_click")
                //{
                    
                //}
                //else if (do_action == "action.operation_activities_del")
                //{
                    
                //}
                return Json(new { ret = 0, msg = "" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"AjaxAction出错: {ex.Message}");
                return Json(new { ret = 0, msg = "System error, please try again later" });
            }
        
        }

        [HttpPost("/Ajax/AjaxCoupon")]
        public async Task<IActionResult> AjaxCoupon([FromForm] CouponRequest request)
        {
            try
            {
                int userId = CurrentUserId;
                string email = CurrentUserEmail;
                //获取购物车id
                 string cids = HttpUtility.UrlDecode(request.CId.FromBase64());
                request.CId=cids;
                var res = await _salesCouponService.CouponDropdown(request, userId, CurrentUserSessionId, CurrentCurrency);
                if (res.Item1)
                {
                    if (res.Item2)//返回html
                    {
                        return Json(res.Item3);
                    }
                    else //返回json
                    {
                        return Json(new { msg = res.Item3, ret = 1 });
                    }
                }
                return Json(new { msg = "error", ret = 0 });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"AjaxCoupon出错: {ex.Message}");
                return Json(new { ret = 0, msg = "系统错误，请稍后再试" });
            }


        }


        [HttpPost("/ajax/salesticker")]
        public async Task<IActionResult> AjaxSaleSticker()
        {
            try
            {

               var res=   await  _saleStickerService.GetSticker();
                return Json(new { msg = res, ret = 1 });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"AjaxSaleSticker出错: {ex.Message}");
                return Json(new { ret = 0, msg = "系统错误，请稍后再试" });
            }


        }
    }
}

