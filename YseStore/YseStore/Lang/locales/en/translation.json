{"web": {"global": {"help": "Help", "category": "Categories", "all_category": "All Categories", "search_by_keyword": "Search by keyword", "all_products": "All PRODUCTS", "view_all": "VIEW ALL", "view_product": "VIEW PRODUCT", "view_more": "VIEW MORE", "view_more_s": "View More", "view_less": "View Less", "viewMorePro": "VIEW MORE PRODUCTS", "readMore": "READ MORE", "more": "MORE", "shopTheSale": "SHOP THE SALE", "viewDetail": "View Detail", "show_now": "SHOW NOW", "viewAllCollections": "View all Collections", "let_go": "Let's go", "buy_it_now": "BUY IT NOW", "seeAllPro": "SEE ALL PRODUCTS", "default": "<PERSON><PERSON><PERSON>", "delete": "Delete", "edit": "Edit", "cancel": "Cancel", "save": "Save", "back": "Back", "clear": "Clear", "submit": "Submit", "update": "Update", "change": "Change", "i_accept": "I accept", "reject": "Reject", "confirm": "Confirm", "upload": "Upload", "send": "Send", "close": "Close", "sContinue": "Continue", "returnHome": "Return home page", "sReturnHome": "Return home page", "clear_all": "Clear All", "select_all": "Select All", "remove": "Remove", "removeitem": "remove item", "add": "Add", "remove_all": "Remove All", "view_cart": "View Cart", "pleaseSelect": "Please select", "quick_find": "Quick Find", "more_options": "more options", "quickView": "Quick View", "submit_success": "Submit Success", "fullDetail": "Full Detail", "index_view": "VIEW THIS PRODUCTS", "more_products": "MORE PRODUCTS", "seeAll": "SEE ALL", "see_more": "SEE MORE", "related_category": "Related Categories", "no_data": "No data Yet", "pending_five_min": "There is currently no data, please check in 5 minutes later", "load_more": "Load More", "learnMore": "LEARN MORE", "shopAll": "Shop All Deals", "date": "Date", "day": "day", "hours": "hours", "mins": "mins", "secs": "secs", "remark": "Remark", "subtotal": "Subtotal", "grandTotal": "Grand Total", "deleteAmount": "Deleted", "refundAmount": "Refunded", "item": "<PERSON><PERSON>", "price": "Price", "qty": "Quantity", "total": "Total", "action": "Action", "items": "Items", "image": "Image", "user_discount": "Member discount", "full_discount": "Full discount", "recall_discount": "Recall discount", "10_digits": "10 Digits", "8_digits": "8 Digits", "previous": "Previous", "next": "Next", "SecurityCode": "Security Check", "by": "By", "order_live": "Recent Orders", "facebookStr": "Facebook", "twitterStr": "Twitter", "pinterestStr": "Pinterest", "youtubeStr": "YouTube", "googleStr": "Google", "position": "Your position", "home": "Home", "popular_search": "Popular Search", "detail": "Detail", "details": "Details", "collections": "Collections", "backToTop": "Back to top", "shield": "Default Web Site Page", "shieldSorry": "Sorry!", "shieldTitle_0": "If you are the owner of this website, please contact your hosting provider", "shieldTitle_1": "It is possible you have reached this page because", "shieldInfoHD_0": "The IP address has changed.", "shieldInfoBD_0": "The IP address for this domain may have changed recently. Check your DNS settings to verify that the domain is set up correctly. It may take 8-24 hours for DNS changes to propagate. It may be possible to restore access to this site by following these instructions for clearing your dns cache.", "shieldInfoHD_1": "There has been a server misconfiguration.", "shieldInfoBD_1": "Please verify that your hosting provider has the correct IP address configured for your Apache settings and DNS records. A restart of Apache may be required for new settings to take affect.", "shieldInfoHD_2": "The site may have been moved to a different server.", "shieldInfoBD_2": "The URL for this domain may have changed or the hosting provider may have moved the account to a different server.", "blank_hostname_text": "The current visit is a temporary domain name, if you need to debug the website, please <a href=\"https://member.ueeshop.com/login.html\">log in</a> to the store", "contact": "Contact", "no_thanks": "No Thanks", "or": "Or", "ok": "OK", "go": "Go", "goTo": "Go To", "show_more": "SHOW MORE", "gly_tit": "SHOP SOCIAL", "gly_more": "SHOW MORE STYLE", "del_confirm": "Irretrievable after deletion, continue?", "selected": "Please select", "loading": "Loading...", "n_y_0": "No", "n_y_1": "Yes", "set_error": "Setup failed unknown mistakes!", "copy_complete": "It has been copied and can be pasted.", "filter": "Filter", "format_mobilephone": "Please fill in the phone number correctly!", "format_telephone": "Please fill in the telephone number!", "format_fax": "Please fill in the fax number correctly!", "format_email": "Please fill in the email address correctly!", "format_length": "Incorrect length! You need to fill in {Length} digits.", "newsletter_success": "Added to subscribe successful!", "newsletter_exists": "This mailbox already exists subscription!", "newsletter_your_questions": "Please write down your questions...", "newsletter_your_email": "Your email", "ver_click": "Click to verify", "ver_slide": "Slide to verify", "ver_error": "Verify code error!", "drag_picture": "Drag the picture to complete the verify", "ver_successed": "Successful verify", "ver_failed": "Verify failed", "please_verification": "Please complete the image verify", "processing": "Processing, please wait...", "processing_str": "Processing", "select_y_country": "Select you country", "batch_buy_select": "Please select the product you want to buy!", "batch_buy_option": "Please select product Options!", "batch_remove_select": "Please select the item(s) you want to delete!", "batch_remove_success": "Product(s) deleted successfully!", "batch_remove_error": "Product(s) deletion failed!", "form_submit_tip": "Thank you for your submission!", "orderMail": "Order Mail", "orderNumber": "Order Number", "orderNotFind": "Sorry, Could Not Find Order", "orderFindContact": "Please contact us if you are having difficulty finding your order details.", "querying": "Querying...", "total_pages": "Total {num} Pages", "change_currency": "Change Currency", "change_language": "Change Language", "coupon_discount": "Coupon discount", "usage_condition": "Usage condition", "validity_eriod": "Validity period", "comming_soon": "Comming soon", "not_enough_now": "not enough now", "news": "News", "cases": "Cases", "introduction": "Introduction", "low_price": "Low Price", "higt_price": "<PERSON><PERSON>", "download": "Download", "download_protected_tips": "Password Protected File", "download_enter_pwd": "Enter password to unlock and download this file", "coupon_operation_receive_tips": "Expires {Duration} {Time}s after successful redemption"}, "not_fount": {"errorWarning": "Sorry! The page you request can not be found...", "goBack": "Go Back", "homePage": "Home Page"}, "tracking": {"tracking_title": "Order tracking", "tracking_tips": "Please enter the order number displayed on the success page when you place the order to check the order status", "orders_info": "Order Information", "orders_number": "Please enter order number", "orders_detail": "View order details", "pro_list": "Product list", "no_orders": "No order information temporarily", "no_tracking": "No logistics information temporarily"}, "header": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "language": "Language", "chooseCurrency": "<PERSON><PERSON>", "search_history": "Search History", "yLikePro": "You May Also Like", "select_your": "Select Your", "itemes": "<PERSON><PERSON><PERSON>", "cartStr": "CART", "shoppingCartStr": "Shopping Cart", "MyCartStr": "MyCart", "yourCartStr": "Your Cart", "sCreate": "Create an account", "recentlySearch": "Recently Searched", "hotSearch": "Hot Searches"}, "footer": {"contactUs": "Contact Us", "connected": "Stay Connected", "followUs": "Follow Us", "subscribe": "Subscribe", "our_partners": "Our Partners", "getInTouch": "GET IN TOUCH", "submit": "SUBMIT", "links": "Links", "sign_up": "SIGN UP", "email": "Email", "emailAddress": "Your Email Address", "enter_email": "Enter Your Email", "newsletter_title": "Subscribe to Our Newsletter", "newsletter_notes": "Get information of our newest products and promotions", "newsletter": "Newsletter", "newsletter_btn": "Subscribe", "newsletterWTips": "about the latest offers and deals Subscribe today!", "unsubscribedTitle": "You've been unsubscribed!", "unsubscribedTxt": "You won't receive any marketing emails from us. You might still receive emails regarding your account and billing.", "newsletter_tips_0": "Please fill in the email address correctly!", "newsletter_tips_1": "Thank you for subscribing", "newsletter_tips_2": "Your email has been joined in our list.", "customTips": "Enter your e-mail address"}}, "cart": {"global": {"shoppingCart": "Shopping Cart", "yShoppingCart": "Your Shopping Cart", "shoppingBagStr": "SHOPPING BAG", "checkOut": "Check Out", "continue": "Return to Cart", "total_count": "Subtotal", "empty": "Your shopping cart is empty.", "invalid": "Invalid", "re_invalid": "Remove Invalid Item", "free_gift": "Free Gift", "tax_ship": "Taxes and shipping calculated at checkout", "grand_total": "Total", "checkout_str": "Proceed to Checkout", "checked_error": "Please select at least one item!", "pre_order": "Pre-order", "mixed_wholesale": "Mixed wholesale", "addSuccess": "Add to cart successfully.", "viewCartCheckout": "View Cart & Checkout", "cartTotal": "Cart Total", "invalidProduct": "Invalid Product", "clearInvalidProduct": "Clear all invalid products", "moreLike": "More like this"}, "tips": {"less_amount": "The minimum purchase quantity of mixed wholesale is {{amount}}.", "less_quantity": "The minimum purchase quantity of mixed wholesale is {{quantity}} pieces.", "less_tips": "The minimum purchase quantity of mixed wholesale is {{amount}} or {{quantity}} pieces"}}, "blog": {"global": {"blog": "Blog", "searchNote": "Search", "searchBtn": "Search", "latestBlog": "Latest Blog", "allblog": "All Blogs", "popular_blog": "POPULAR BLOG", "archives": "ARCHIVES", "blog_category": "Category", "tags": "TAGS", "by": "By", "comments": "Comments", "leaveAReply": "Leave a Reply", "commentsTips": "Your email address will not be published.Required fields are marked. *", "name": "name", "email": "E-mail", "content": "Content", "verificationCode": "Verification code", "writeReview": "WRITE REVIEW", "recentlyReviews": "Recently Reviews", "codeError": "Verification code error!", "submitSuccess": "Submit Success", "recentPosts": "Recent Posts", "review": "{{count}} reviews", "view": "{{count}} views", "readMore": "Read More"}}, "user": {"global": {"Unknown": "Unknown", "Mr": "Mr", "Ms": "Ms", "sign_up": "Sign Up", "sign_in": "Sign In", "JoinFree": "Join Free", "login_tips": "please log in first", "welcome": "Welcome", "firstname": "First Name", "your_fir_name": "your first name", "lastname": "Last Name", "surname": "SurName", "your_last_name": "your last name", "you_domain_com": "<EMAIL>", "at_6_char": "at least 6 characters", "password": "Password", "confirm_label": "Confirm", "confirm_pwd": "Confirm Password", "email": "Email", "returnHome": "Return to the home page", "productName": "Product Name", "distributorEmail": "Distributor Email", "apply": "Apply", "customer": "Customer", "use": "Use"}, "login": {"log_in": "Log in", "login_brief": "Access your account & orders history.", "remember_me": "Remember me", "forgetpwd": "Forget Password?", "new_customer": "New customer ? <a href=\"/account/sign-up.html\" class=\"FontColor\">Start here.</a>", "new_customer_notes": "New customer ? <a href=\"{{url}}\" class=\"FontColor\">Start here.</a>", "emailAddress": "Your Email Address", "signInWith": "Sign In With", "popups_title": "Sign In", "error_note": "Incorrect email address or password. Please try again.<br> Make sure the Caps Lock is off before you enter password.", "forgot": "Forgot your <a href=\"/account/forgot.html\" class=\"forgot\">password</a>?", "stay_note": "Stay signed in <span>Protect your privacy - sign out when you're done.</span>"}, "register": {"already": "Already have an account ?", "signInNow": "Sign In Now", "use_with": "Use Your Account from", "register_title": "Create Account", "register_now": "Register Now", "incorrect": "The Email address you entered is incorrect.", "createPWD": "Create Your Password", "tryAgain": "Passwords do not match. Please try again.", "createInfo0": "By clicking \"Create my Account\" I agree that:", "createInfo2": "I may receive communication emails from {SiteName} about order and delivery.", "createInfo3": "I may receive new products and promotion emails from {SiteName}.", "createAccount": "Create my Account", "verTitle": "Congratulations, you have successfully completed created an account on {Domain}!", "verInfo": "You can edit your Nickname, <strong>{UserName}</strong> on your Website <a href=\"/account/\" class=\"FontColor\">account page</a>.", "varInfo_0": "Verify your email", "varInfo_1": "Verification of your email address will make your account more secure.", "varInfo_2": "To get more information about Website, please go to", "varInfo_3": "You can also go to", "varInfo_4": "Click the link in the email we sent to", "verifyNow": "Verify Now", "reviewInfo_0": "Pending Approval", "reviewInfo_1": "Your account has been created and is now pending approval.", "reviewInfo_2": "Our admin team may need to contact you for further information to finalise your account application.", "resendEmail": "<PERSON><PERSON><PERSON>", "newBuyerGuide": "New Buyer Guide", "purchaseFlow": "Purchase Flow", "returnPage": "Return to previous page", "homePage": "Go to Homepage", "varComTitle": "Congratulations, <strong class=\"FontColor\">{UserName}</strong>, your email address had been verified.", "varComTitle_1": "Congratulation! Your email address has been verified", "varComTitle_error": "Your email address has not been verified", "varComInfo": "Where would you like to go to next", "reviewInfo_error": "Your account has been created and is now pending approval. Our admin team may need to contact you for further information to finalise your account application."}, "forgot": {"resetPWD": "Reset Your Password", "newPWD": "New Password", "ConfirmPWD": "Confirm Password", "sentEmail": "We have sent an email to the address you have on file with us. Please follow the instructions in this email to reset your password.", "receivedEmail": "Haven't received the email?", "checkEmail": "Check your bulk and junk email folders. If you still can't find the password reset email, please call our Customer Care Team. Thank you!", "continueShopping": "Continue Shopping", "successfully": "You have successfully reset your password.", "signIndex": "Sign In My Account", "enterEmail": "Enter your email address", "enteredEmail": "The Email address you entered is incorrect.", "sentReset": "Before we can reset your password, we require that you enter your email address below. You will then receive an email with instructions to reset your password.", "contactServices": "If you can't remember which email address you registered with or still have problems signing in to your account please contact our Customer Services.", "sendEmail": "Send Email", "matchPWD": "Passwords do not match. Please try again.", "enterPWD": "To reset your password, please enter your new password below."}, "account": {"basicTitle": "Dashboard", "indexTitle": "My Account", "orderTitle": "My Orders", "reviewTitle": "My Reviews", "favoriteTitle": "My Favorite", "couponTitle": "My Coupons", "addressTitle": "Manage Address Book", "settingTitle": "Account <PERSON><PERSON>", "inboxTitle": "My Inbox", "distributionTitle": "Make Money", "productsTitle": "Q & A", "messageTitle": "Message", "signOut": "Sign Out", "logOut": "Log out", "binding_completed": "Congratulations, you have completed {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>} authorization.", "binding_finish": "To finish the login process, please provide email address.", "binding_note": "Please note that all important emails will be sent to this email address.", "binding_tips": "Incorrect email address. Please try again.", "time": "Time", "newMessage": "You have new message(s)", "Content": "Content", "enter_message": "Please enter your message here...", "reply_content": "Reply Content", "addReply": "Add your reply here...", "email_addr": "Email Address", "reply_btn": "Reply", "sLikeProd": "You May Also Like", "recentlyViewed": "Recently viewed", "ship_addr": "Shipping Address", "bill_addr": "Billing Address", "add": "Add New Address", "name": "Name", "nickName": "<PERSON><PERSON><PERSON>", "firstname": "First Name", "lastname": "Last Name", "address1": "Address", "phoneNum": "Phone number", "province_state": "Province/State", "city": "City", "state": "State", "countrySelect": "Country/Region", "cpf_cnpj": "CPF or CNPJ code", "CPF": "CPF (personal order)", "CNPJ": "CNPJ (company order)", "cpf": "CPF", "cnpj": "CNPJ", "per_order": "personal order", "com_order": "company order", "per_id_num": "Personal ID number", "vat_id_num": "VAT ID number", "zip_code": "Zip code", "tax_code": "Tax code", "addr_line_1": "Address", "addr_line_2": "Apartment,suite,street number,etc. (optional)", "discount": "Discount", "status": "Status", "package": "Package", "orders_no": "Orders No:", "orderDate": "Order Date", "orderTotal": "Order Total", "orderStatus": "Order Status", "printOrder": "Print Order", "cancelReason": "Cancel Reason", "cancelOrder": "Confirm cancel the order", "successDel": "You have successfully deleted your order.", "backReturn": "Click <a class=\"blue u\" href=\"/account/orders/\">here</a> to return to My Orders to review your other orders or keep shopping.", "shippedTo": "Shipped To", "shippingMethod": "Shipping method", "total_weight": "Total Weight", "billedTo": "Billed To", "paymentMethod": "Payment method", "trackNo": "Tracking Number", "paymentInfo": "Payment Information", "senderName": "Sender's Name", "referenceNumber": "Reference Number", "country": "Country", "subtotal": "Subtotal", "handingFee": "Handling Fee", "couponSavings": "Coupon savings", "writeReview": "Write a review", "receiving": "Confirm Receiving", "shippedTime": "Shipped Time", "tracking": "Detail Tracking", "shipment": "Shipment", "trackItem": "Shipment {Item}-{Count}", "OrderStatusAry_1": "Awaiting Payment", "OrderStatusAry_2": "Awaiting Confirm Payment", "OrderStatusAry_3": "Payment Wrong", "OrderStatusAry_4": "Awaiting Shipping", "OrderStatusAry_5": "Shipped", "OrderStatusAry_6": "Received", "OrderStatusAry_7": "Cancelled", "changeProfile": "Change your Profile", "MyProfileInfo_1": "From your account dashboard. you can easily check &amp; view your", "MyProfileInfo_2": "recent orders", "MyProfileInfo_3": ", manage your", "MyProfileInfo_4": "shipping and billing addresses", "MyProfileInfo_5": "and", "MyProfileInfo_6": "edit your password and account details.", "MyProfile_Information": "Account Information", "MyProfile_Contact": "Contact Information", "changeEmail": "Change your Email address", "existingPWD": "Existing Password", "newAddress": "New E-mail Address", "changePWD": "Change your password", "rePWD": "Re-enter Password", "error_Email": "Incomplete email address. Please try again.", "error_LoginStatus": "This account has not been approved.<br /> For questions, please contact the administrator.", "error_Password": "Incorrect email address or password. Please try again.<br /> Make sure the Caps Lock is off before you enter password.", "error_Code": "Verification code error.", "error_EmailEntered": "The Email address you entered is incorrect.", "error_Name": "Your name can not be empty.", "error_Exists": "The email address already exists, please change it or sign in to checkout.", "error_EmailBeen": "Sorry, this email address has already been used", "error_EmailSuccess": "Email modified successfully!", "error_PWDWrong": "Sorry, existing password is wrong!", "error_PWDBeen": "Sorry, your passwords do not match, please try again!", "error_PWDSuccess": "Change Password Success!", "error_Forgot": "Sorry, this Email Address is not registered with our online store.", "error_Incomplete": "Incomplete email address. Please try again.", "error_Error": "An unknown error occurred!", "error_pic": "Please upload a picture", "error_file": "Please upload a file", "awaiting_review": "Awaiting Review", "coupons": "Coupons", "noCoupons": "None Coupons", "order_over": "For orders over {OrderPrice}", "order_item": "Order more than {OrderCount} products", "already_received": "Already received", "expired": "Expired", "code": "Code", "order_details": "Order Details", "new_coupon": "You get a new coupon", "DIST_make_money": "Make Money", "DIST_total_comm": "total commission", "DIST_comm_balance": "Current Balance", "DIST_withdraw": "Withdraw", "DIST_referred_mem": "My Distributor", "DIST_how_to_share": "Share", "DIST_copy": "Copy", "DIST_copy_tips": "Copy this link and share on your social media channels", "DIST_click_tips": "Click the buttons below and share to", "DIST_products_tips1": "Like this product?", "DIST_products_tips2": "<a href=\"/account/\">Join us</a> to promote this product via an exclusive coupon code !", "DIST_how_mak_money": "How to Make Money", "DIST_info_title_0": "share link", "DIST_info_title_1": "invite", "DIST_info_title_2": "join", "DIST_info_title_3": "get commission", "DIST_info_txt_0": "Copy & share this link on your social media channels.", "DIST_info_txt_1": "Invite friends to register via your link and complete any order.", "DIST_info_txt_2": "Each of your friends who join us will get coupon from your link.", "DIST_info_txt_3": "The commission obtained can be withdrawn to your account.", "DIST_comm_detail": "Commission Detail", "DIST_record": "Record", "DIST_level": "Level", "DIST_total": "Total", "DIST_amount": "Amount", "DIST_card_num": "<PERSON><PERSON> Email Address", "DIST_card_acc": "Paypal Account Name", "DIST_status": "Status", "DIST_status_0": "Pending Commission", "DIST_status_1": "Settled Commission", "DIST_secondary": "Secondary Distributor", "DIST_tertiary": "Tertiary Distributor", "reg_time": "Registration Time", "withdrawTip0": "No relevant member information!", "withdrawTip1": "The withdrawal amount must be greater than 0!", "withdrawTip2": "The withdrawal amount cannot be greater than the balance!", "friends": "Friends", "order_info": "Order Information", "orderNo": "Order Number", "orderstatus": "Order Status", "address_book": "Address Book", "enjoyDiscount": "enjoy {Discount} off discount", "freeShipping": "free shipping", "enjoyFreeShipping": "enjoy free shipping", "cancel_order": "Cancel Order", "Contact": "Contact", "total_price": "Total Price", "coupon_tips": "Congratulations, you've won the coupon: {CouponNumber}. <br>To learn more, visit My Coupons.", "order_no": "Order No.", "commission": "Commission", "distributor": "Distributor", "order_status": "Order Status", "commission_status": "Commission Status", "order_total": "Grand Total", "send_email_ture": "An email has been sent to you successfully. Please enter your email address and check the verification email.", "send_email_false": "Sorry, you can only click once a minute, please click again after a minute.", "go_to_view": "Go to view", "order_cancel": "Are you sure you want to delete this pending order?", "sure": "Are you sure?", "delete_shipping": "Are you sure you want to delete this address?", "send": "Send", "reg_err_Email": "Please enter an email address.", "reg_err_EmailFormat": "The email you've entered is invalid. Please check your email and try again.", "reg_err_PWDConfirm": "Please re-enter your new password.", "reg_err_PWDNotMatch": "Your passwords do not match, please try again.", "address_tips_PleaseEnter": "Please enter your {Field} code.", "address_tips_taxcode_length": "Your {Field} must contain a minimum of {TaxLength} numbers.", "address_tips_phone": "Please enter your phone number.", "address_tips_phone_format": "Please enter a valid phone number.", "address_tips_phone_length": "Your phone number must be at least 7 digits.", "distibutor_detail": "Distributor Details", "share_store": "Share Store", "share_products": "Share Products", "choose_way_to_share": "Choose way to share", "share_to_social_media": "Share to social media", "copy": "Copy", "copy_success": "Copy successfully", "total_order": "Total Order", "sentMoney": "Sent Money", "contents": "Contents", "MTCN": "MTCN# No.", "maxAmount": "Maximum Discount Price: {{amount}}", "thanks_you": "Thank you!", "review_success": "Your review is successfully submited.", "review_verify": "Your review is successfully submited. We will process it shortly.", "review_coupon": "Your review is successfully submited and the coupon has been sent to your account.", "go_to_account": "Go to Account", "expected_earning": "Expected Earning", "email_account": "Account", "email_name": "Name", "email_distributor": "Distributor", "email_amount": "Amount", "apply_to_be_distributor": "Apply to become a distributor", "pending_review": "Pending review", "thanks_apply_tips": "Thank you for your application. We will review as soon as possible and reply to you via email.", "apply_been_rejected_title": "Application has been rejected", "apply_been_rejected_subtitle": "Sorry, due to the following reason, your application for the distributor is rejected. You can reapply it after modifying your information.", "apply_again": "Apply Again", "fail_reason": "Failure reason", "couponTimes-day": "{{num}}day", "couponTimes-hour": "{{num}}hour"}, "verify": {"activate_account": "Activate the Account", "current_account_at": "Current account {Email}", "link_expired": "The link has expired"}, "orders": {"removed": "deleted", "products_num": "{num} products", "buy_again": "Buy Again", "buy_again_failed": "Failed to add to cart", "buy_again_success": "{{number}} product(s) has been successfully added to cart", "buy_again_cart": "The following products cannot be added to cart"}, "points": {"title": "My Points", "wayEarn": "Ways to Earn", "wayRedeem": "Ways to Redeem", "points": "Points", "numPoints": "{{num}} Points", "expired": "{{points}} points will be expired at {{time}}", "redeem": "Redeem", "all": "All", "earned": "Earned", "used": "Used", "fluctuation": "Points Fluctuation", "source": "Source", "date": "Date", "order": "Place an order", "orderDesc": "{{num}} Ponits for every {{amount}} spent", "coupon": "{{text}} off coupon", "canRedeem": "You can redeem {{num}} points for a coupon:", "suffPoins": "Insufficient points, please earn more.", "reward": "Reward {{points}} points", "maxPoints": "Max {{points}} ponits per oder"}, "pointsLogsType": {"register": "Register an account", "newsletter": "Subscribe newsletter", "placeorder": "Shopping distribution", "return": "Returned points", "adjust": "Distributed by merchant", "expired": "Points expired", "cancel": "Cancelled points", "deduction": "Order discount", "exchange": "Redeem the coupon"}}, "products": {"global": {"prodDetail": "Product Detail", "lowPrice": "<span class=\"g_low_price\">from </span>{{lowPrice}}", "end_in": "End In"}, "lists": {"item_column": "{A}-{B} of {C} Items for \"{K}\"", "sort_by": "Sort by", "sort_by_ary_0": "All", "sort_by_ary_1": "Time (New to Old)", "sort_by_ary_2": "Time (Old to New)", "sort_by_ary_3": "Price (Low to High)", "sort_by_ary_4": "Price (High to Low)", "sort_by_ary_5": "Sales (High to Low)", "sale": "Sale", "ends_in": "Ends in", "coming_soon": "Coming soon"}, "goods": {"buyNow": "Buy it Now", "shopNow": "SHOP NOW", "inquiry_now": "INQUIRY NOW", "addToCart": "Add To Cart", "addToBag": "Add To Bag", "addToFavorites": "Add to Favorites", "quickShop": "Quick Shop", "new": "New", "freeShipping": "Free Shipping", "limited_offer": "Limited Offer!", "sold_number": "{SoldNumber} sold", "sold": "Sold", "qty": "Quantity", "qty_text": "Qty", "soldout": "Sold Out", "inStock": "In Stock", "outStock": "Out of stock", "num_inStock": "{Num} in stock", "availability": "Availability", "itemCode": "Item Code", "available": "{AvailableNum} {AvailablePieces}available", "notice": "Notice", "save": "Savings Price", "customer_review": "Customer Reviews", "reviews": "Reviews", "writeReview": "Write a review", "mayLike": "You May Like", "free_shipping": "Free Shipping", "write_a_review": "Write a review for this product", "your_rating": "Your rating", "write_your_name": "Your name", "write_your_review": "Write your review", "write_review_tip": "your review great: Describe what you liked, what you didn’t like, and other key things shoppers should know (minimum 1 characters)", "your_experience": "Description your experience...", "add_your_images": "Add your image(s)", "name": "Name", "review_content": "Review content", "review_tips_0": "Product comment is only valid for customers that already purchased and received this product.", "picture_tips": "Please only provide JPG/GIF/PNG files.", "description": "Description", "pro_inquiry": "Products Inquiry", "inquiry_title": "Inquiry", "i_number": "Item No.", "helpful": "Was this review helpful?", "units": "Units", "platform_tips": "Or buy on other website", "realOrderInfo": "{UserName} in {Country} purchased", "realOrderSec": "{Time} second ago", "realOrderSecNum": "{Time} seconds ago", "realOrderMin": "{Time} minute ago", "realOrderMinNum": "{Time} minutes ago", "realOrderHour": "{Time} hour ago", "realOrderHourNum": "{Time} hours ago", "realOrderDay": "{Time} day ago", "realOrderDayNum": "{Time} days ago", "realOrderMon": "{Time} month ago", "realOrderMonNum": "{Time} months ago", "new_arrival": "New Arrivals", "hotProd": "Hot Selling Products", "sale": "Sale", "special_offer": "Special Offers", "whats_hot": "What's Hot", "select": "Select {SelectName}", "full_distance": "You'll get {DiscountedPrices} off when you buy another {ConditionalPrice}", "wholesale": "Wholesale", "full_txt_0": "GET {V} OFF ON ORDERS OVER {K}", "full_txt_1": "GET {V} OFF ON ORDERS OVER {K}", "full_txt_2": "SAVE {V} WITH PURCHASE OVER {K} UNITS", "full_txt_3": "{V} OFF WITH PURCHASE OVER {K} UNITS", "full_txt_4": "Save {V} for each {K} spent", "full_txt_5": "Save {V} for each {K} pieces purchased", "full_txt_6": "Get <span>FREE</span> gift with purchase over {K}", "full_txt_7": "Get <span>FREE</span> gift with purchase over {K} units", "full_txt_8": "Get <span>FREE</span> gift for buying every {K}", "full_txt_9": "Get <span>FREE</span> gift for buying every {K} units", "attributes_tips": "Please select the information you want", "more_details": "More Details", "plsChooseOption": "Please choose options.", "total_extras": "Total extras", "full_name": "Full Name", "whatsapp_phone": "Whatsapp/Phone", "i_success_tip": "Submitted. Thanks for your inquiry!", "cod_sevenday": "7-day appreciation period", "cod_cod": "cash on delivery", "cod_freeshipping": "free shipping", "cod_orders_tracking": "Order tracking", "cod_notice": "User notice", "cod_message": "leave a message", "cod_submit_orders": "Submit orders", "cod_successful_purchase": "Purchase", "mapbox_recommended": "Recommended", "mapbox_streets_water_parks": "Streets + Water + Parks", "mapbox_streets_waters": "Streets + Water", "mapbox_streets_parks": "Streets + Parks", "mapbox_streets_only": "Streets only", "mapbox_water_only": "Water only", "mapbox_map_view": "MAP VIEW", "mapbox_jewelry_view": "JEWELRY VIEW", "pro_to_check": "Proceed to Checkout", "re_to_shop": "Return to Shopping", "cart_add": "Adding to cart succeed!", "cart_items": "{CartNum} items in cart.", "product_error": "The product data has been lost, please delete and then re-purchase", "no_review_data": "There are no customer reviews yet", "warning_number": "Purchase fewer than or more than the stock MOQ", "warning_MOQ": "Please enter quantity of {{qty}} or more.", "warning_Max": "Sorry, you can only buy no more than {{qty}}.", "warning_stock": "Sorry, you can only buy no more than {{qty}}.", "select_country": "--Please select your country--", "sign_in": "Please sign in first!", "free": "Free", "via": "via", "unavailable": "Unavailable", "shipEstimated": "Estimated Delivery Time", "freight_country_region": "Country/Region", "freight_shipping": "Shipping", "freight_ship_from": "Ship from", "freight_ship_to": "Ship to", "freight_ship_method": "Shipping method", "freight_not_delivery_to": "<strong>Can not deliver</strong> to {Country}", "freight_delivery_to": "to {Country} via {Ship}", "freight_no_support": "This Supplier/Shipping Company does not deliver to your selected Country/Region.", "freight_estimated_delivery": "Estimated Delivery", "freight_cost": "Cost", "freight_carrier": "Carrier", "freight_memo": "Estimated fee, the actual amount is calculated during checkout.", "review_rating": "Your rating is required.", "review_title": "Your name is required.", "review_max": "Remaining characters:Please don't exceed {5,000} characters.", "arrival_info_0": "Has been successfully submitted to the business, the products cover will immediately notify the customer.", "arrival_info_1": "Please log in to apply for arrival notice.", "arrival_info_2": "This product has applied for the arrival notice!", "shipping_method_tips": "Please select a shipping method!", "checkout": "Checkout", "card_type": "Please select the card type", "card_num": "Card Number can not be null, it must be numeric, the length must be 16 or 15.", "card_month": "Card Expire Month can not be null.", "card_year": "Card Expire Year can not be null.", "card_cvv": "CVV2/CSC is incorrect, it must be numeric, the lenght must be 3!", "card_bank": "Issuing Bank can not be null!", "group": "Group Purchase", "chooseOptions": "Choose Options", "couponDiscount": "{Discount} OFF Discount", "couponPrice": "Save {Price}", "getIt": "Get it", "receiveSucc": "Received successfully", "combination_save": "Save {Price} for buying together", "combinationTitle": "Frequently Bought Together", "sku": "SKU", "selected_list": "Selected list", "num_pieces": "{Num} pieces", "min_scope": "Min. Order: {Min} Pcs", "max_scope": "Limit {Max} per order", "max_qty_tips": "Maximal purchase quantity for this product is {Max}", "no_products": "No Products", "review_thank": "Thanks for your review", "review_manual_thank": "Thank you! Please refresh the page in a few moments to see your review", "video_tips": "Your browser does not support the video tag.", "max": "max", "invalidOptions": "Please choose options you want to purchase", "upload_max_tips": "Sorry, you can only upload the file no larger than 20 MB", "savePrice": "save {Price}", "LoginSeePrice": "Login to see price", "withPhotos": "With Media", "member_free_tips": "Members enjoy free shipping", "add_your_video": "Add your video", "upload_video_tips": "Please only provide MP4 files. Individual video size cannot exceed 20MB.", "upload_video_limit": "Sorry, you can only upload the file no larger than 20MB"}, "app": {"arrival_notice_enter_email": "Please enter your email address", "arrival_notice_enter_email_tips": "The Email address you entered is incorrect.", "arrival_notice_result_text": "We'll email you at <span class=\"fc_red\">{{email}}</span> when the item is available", "guide_title": "Size Guide"}}, "checkout": {"global": {"contents": "Contents", "summary": "Order summary", "totalamount": "Total Amount", "status": "Order Status", "paynow": "Pay Now", "incompatible": "There are {{qty}} kinds of products that do not meet the requirements for wholesale orders"}, "checkout": {"step_ary_0": "Place Order", "step_ary_1": "Pay", "step_ary_2": "Completed", "step_infomation": "Information", "step_shipping": "Shipping", "step_payment": "Payment", "expressCheckout": "Express Checkout", "customerInfo": "Contact Information", "emailNewsOffers": "Email me with news and offers", "paymethod": "Payment method", "secureTips": "All transactions are secure and encrypted.", "products": "Products", "shipTariff": "Tax number for shipping", "moreAddress": "More Address", "CreditOrDebit": "Credit or Debit Cards", "shipsFrom": "Ships From", "shipmethod": "Shipping method", "cost": "Cost", "estimated_delivery": "Estimated Delivery", "carrier": "Carrier", "special": "Special Instructions", "special_df": "Special Instructions for seller", "apply": "Apply", "coupon_code": "Coupon Code", "enter_code": "Enter or select coupon code", "code": "Code", "coupon_error": "The coupon code <strong></strong> is invalid. It either does not exist, has not been activated yet, or has expired.", "subtotal": "Subtotal", "code_save": "Coupon savings", "fee": "Service charge", "shipcharge": "Shipping", "grandTotal": "Total", "place_order": "Complete Order", "shipPhone": "Phone for shipping", "phoneTips": "Please enter your phone number.", "zipTips": "Enter a valid ZIP / postal code for {Country}.", "weight": "Weight", "ship_insur": "Shipping &amp; Insurance", "tax": "Taxes", "discount": "Discount", "coupon_save": "Coupon savings", "return_to_cart": "Return to Cart", "sentMoney": "Sent Money", "MTCN": "MTCN# No.", "bankSlipCode": "Bank Slip Code", "orderInfo": "Order Information", "orderNo": "Order Number", "amount": "Amount", "orgCode": "Card Type", "CardNo": "Card Number", "expirationDate": "Expiration Date", "month": "Month", "year": "Year", "CVV2": "CVV2/CSC", "issuingBank": "Issuing Bank", "yBillAddress": "Your Billing Address", "yShipAddress": "Your shipping address", "pName": "Name", "pAddress": "Address", "pCountry": "Country/Region", "pProvince": "State/Province", "pCity": "City", "pZipCode": "Postal Code", "ptaxCodeValue": "Tax ID", "pPhoneNo": "Mobile phone number", "makePayment": "Make Payment", "EnterCardInfo": "Enter your card information", "required_fields_tips": "Required fields are blank", "already_paid_tips": "Already paid", "abnormal_tips": "Abnormal payment method, it is recommended to change other payment methods", "cancel_tips": "Confirm, you will abandon this payment and cannot return to continue payment.", "continue_str": "Continue Checkout", "coupon_price_tips": "The total amount of the product is lower than the coupon's consumption conditions, the system automatically cancel the coupons now used.", "tips_no_delivery": "The item(s) can't be shipped to {Country}.", "tips_contact_us": "Contact Us: <a href=\"mailto:{Email}\">{Email}</a>", "tips_pay_loading_0": "We're redirecting you to pay now.", "tips_pay_loading_1": "It'll take a few seconds or more.", "coupon_tips_0": "The coupon code <strong>{CouponCode}</strong> is invalid. It does not exist.", "coupon_tips_-1": "The coupon code <strong>{CouponCode}</strong> is invalid. It has not been activated yet.", "coupon_tips_-2": "The coupon code <strong>{CouponCode}</strong> is invalid. It has expired.", "coupon_tips_-3": "The coupon code <strong>{CouponCode}</strong> is invalid. It has already used.", "coupon_tips_-4": "The coupon code <strong>{CouponCode}</strong> is invalid. You could use the coupon when the order amount reach to <strong>{Price}</strong>.", "coupon_tips_-5": "The coupon code <strong>{CouponCode}</strong> is invalid. It's not received by the customer.", "coupon_tips_-6": "The coupon code <strong>{CouponCode}</strong> is invalid. Some products are not included in the offer of this coupon.", "coupon_tips_-7": "The coupon code <strong>{CouponCode}</strong> is invalid. You could use the coupon when the order qty reach to <strong>{Num}</strong>.", "coupon_tips_-8": "This coupon cannot be used in conjunction with other offers or discounts", "coupon_tips_register": "The coupon code <strong>{CouponCode}</strong> is invalid. It is only available for new members.", "coupon_tips_member": "The coupon code <strong>{CouponCode}</strong> is invalid. You may get it if you sign in.", "coupon_tips_review": "The coupon code <strong>{CouponCode}</strong> is invalid. You may get it if you successfully leave a review.", "coupon_tips_confirmReceipt": "The coupon code <strong>{CouponCode}</strong> is invalid. You may get it if you confirm the delivery.", "coupon_tips_orderCompleted": "The coupon code <strong>{CouponCode}</strong> is invalid. You may get it if you confirm the delivery.", "method": "Method", "return_shipping": "Return to shipping", "continue_payment": "Continue to payment", "return_infomation": "Return to infomation", "continue_shipping": "Continue to shipping", "show_summary": "Show order summary", "hide_summary": "Hide order summary", "next_step": "Calculated at next step", "calculatelater": "Calculate later", "default_payment_notes": "after clicking \"Complete Order\", you will be redirected to Credit or Debit Cards to complete your purchase securely", "invalidCountry": "The currently selected country is invalid, please select again", "invalidRegion": "The currently selected region is invalid, please select again", "invalidCart": "The shopping cart product is invalid, please go to the shopping cart to check", "waitingPayment": "Waiting for payment", "addTransferConfirm": "Please add the bank transfer voucher and we will confirm.", "addPaymentProof": "Add payment proof", "uploadPaymentProof": "Upload payment proof", "editPaymentProof": "Edit payment proof", "excheckout_complete_tips": "Please click the PayPal payment button on this page to complete the purchase.", "billAddress": "Billing Address", "billAddressSame": "Same as shipping address", "billAddressDifferent": "Use a different billing address", "confirmPayment": "Confirm Payment", "changePayMethod": "Change payment method", "saved_addresses": "Saved addresses", "use_new_address": "Use a new address", "errorTaxCodeFormat": "Enter a valid {TaxCodeName} code for {CountryName}", "zipVerifying": "The postal code is being verified. Please wait.", "savePaymentMethod": "Securely store payment details for future purchases", "paypalVaultItem": "{{brand}} ending in {{last_digits}} (expires {{expiry}})", "useNewPaymentMethod": "Use a new payment method", "securityCode": "Security code"}, "result": {"error_reason": "Wrong reason: ", "sCreate": "Create an account", "win_coupon": "Create an account and win {CouponPrice} coupon now !", "sCreateTips": "To help you find your order easily.", "sUAccount": "Your account", "consumption": "Requires a minimum purchase of {MinPrice}. And you still need {Price} to meet the requirements.", "quota": "The maximum amount is {<PERSON><PERSON><PERSON>}, you are now over {Price}.", "not_accept": "Sorry, we do not accept payment by this currency!", "paymentInfo": "Once your order has been completed successfully you will receive a confirmation email. Your order details will be securely transmitted. Due to exchange rates, the amount billed can vary slightly. Thank you very much for shopping from our shop", "paySent": "Payment Sent", "successfully": "You have paid the order successfully.", "errorfully": "Your order payment failed.", "sStatusAwait": "Awaiting payment confirmation.", "sStatusOk": "Thank You ! Your payment is completed.", "sStatusThank": "Thank you!", "sOrderNumber": "Your order number is {OrderNum}.", "sAwaitTips1": "If you have paid it, we will change the order status automatically.", "sAwaitTips2": "If you have question, please contact", "sThankTips1": "Thank you for shopping with us! Your order has been received!", "sThankTips2": "Thank you for shopping with us! Your information has been received!", "sCreateMy": "Create my account", "errorTitle": "Oops! Payment Failed", "errorNote": "Payment for Order No. {OrderNum} could not be proceed. Try again.", "error_additem_stock": "The product has been out of stock!", "cancelTitle": "Sorry! Your order has been cancelled.", "cancelNote": "Your order number is {OrderNum}.", "attribute_error": "There was an error in the product information, please remove this product and then re purchase.", "address_error": "Please select \"Country/Region\" and \"State\" first", "shipping_error": "Please choose a delivery method.", "payment_error": "Please choose a payment method.", "product_error": "Your shopping cart is empty.", "low_error": "Your total product has not yet reached the lowest consumption value of the website", "stock_error": "Some products have insufficient inventory, you can not create an order correctly, adjust the purchase quantity.", "no_delivery": "Sorry! No delivery to this Country/Region!", "shippingCompleteError": "Please complete shipping method.", "paymentMin": "The minimum consumption amount of the currently selected payment method is {{MinPrice}}, you are still short of {{Price}}.", "paymentMax": "The maximum limit amount for the currently selected payment method is {{MaxPrice}}, you are now exceeding {{Price}}."}, "points": {"points": "Points", "maximum": "The number you entered exceeds the maximum amount of points allowable.", "maxAva": "Max Available"}}, "protocol": {"global": {"cookies_agreement": "We use cookies to improve your online experience. By continuing browsing this website, we assume you agree our use of cookies."}}, "area": {"country": {"AF": "Afghanistan", "AL": "Albania", "DZ": "Algeria", "AS": "American Samoa", "AD": "Andorra", "AO": "Angola", "AI": "<PERSON><PERSON><PERSON>", "AG": "Antigua and Barbuda", "AR": "Argentina", "AM": "Armenia", "AW": "Aruba", "AU": "Australia", "AT": "Austria", "AZ": "Azerbaijan", "BS": "Bahamas", "BH": "Bahrain", "BD": "Bangladesh", "BB": "Barbados", "BY": "Belarus", "BE": "Belgium", "BZ": "Belize", "BJ": "Benin", "BM": "Bermuda", "BT": "Bhutan", "BO": "Bolivia", "BA": "Bosnia and Herzegowina", "BW": "Botswana", "BV": "Bouvet Island", "BR": "Brazil", "IO": "British Indian Ocean Territory", "BN": "Brunei Darussalam", "BG": "Bulgaria", "BF": "Burkina Faso", "BI": "Burundi", "KH": "Cambodia", "CM": "Cameroon", "CA": "Canada", "CV": "Cape Verde", "KY": "Cayman Islands", "CF": "Central African Republic", "TD": "Chad", "CL": "Chile", "CN": "China", "CX": "Christmas Island", "CC": "Cocos (Keeling) Islands", "CO": "Colombia", "KM": "Comoros", "CG": "Congo", "CK": "Cook Islands", "CR": "Costa Rica", "CI": "Cote D'ivoire", "HR": "Croatia", "CU": "Cuba", "CY": "Cyprus", "CZ": "Czech Republic", "DK": "Denmark", "DJ": "Djibouti", "DO": "Dominican Republic", "TL": "Timor Leste", "EC": "Ecuador", "EG": "Egypt", "SV": "El Salvador", "GQ": "Equatorial Guinea", "ER": "Eritrea", "EE": "Estonia", "ET": "Ethiopia", "FK": "Falkland Islands (Malvinas)", "FO": "Faeroe Islands", "FJ": "Fiji", "FI": "Finland", "FR": "France", "GF": "French Guiana", "PF": "French Polynesia", "TF": "French Southern Territories", "GA": "Gabon", "GM": "Gambia", "GE": "Georgia", "DE": "Germany", "GH": "Ghana", "GI": "Gibraltar", "GR": "Greece", "GL": "Greenland", "GD": "Grenada", "GP": "France, DOM-TOM Guadeloupe", "GU": "U.S. Guam", "GT": "Guatemala", "GN": "Guinea", "GW": "Guinea-bissau", "GY": "Guyana", "HT": "Haiti", "HM": "Heard Island and Mcdonald Islands", "HN": "Honduras", "HK": "Hong Kong, China", "HU": "Hungary", "IS": "Iceland", "IN": "India", "ID": "Indonesia", "IR": "Iran (Islamic Republic of)", "IQ": "Iraq", "IE": "Ireland", "IL": "Israel", "IT": "Italy", "JM": "Jamaica", "JP": "Japan", "JO": "Jordan", "KZ": "Kazakhstan", "KE": "Kenya", "KI": "Kiribati", "KR": "Korea, Republic of", "KP": "Democratic People's Republic of Korea", "KW": "Kuwait", "KG": "Kyrgyzstan", "LA": "Lao People's Democratic Republic", "LV": "Latvia", "LB": "Lebanon", "LS": "Lesotho", "LR": "Liberia", "LY": "Libyan Arab Jam<PERSON>riya", "LI": "Liechtenstein", "LT": "Lithuania", "LU": "Luxembourg", "MO": "Macau, China", "MK": "Macedonia,F.Y.R.O.M", "MG": "Madagascar", "MW": "Malawi", "MY": "Malaysia", "MV": "Maldives", "ML": "Mali", "MT": "Malta", "MH": "Marshall Islands", "MQ": "France, DOM-TOM Martinique", "MR": "Mauritania", "MU": "Mauritius", "YT": "France, DOM-TOM <PERSON>tte", "MX": "Mexico", "FM": "Micronesia, Federated States of", "MD": "Moldova, Republic of", "MC": "Monaco", "MN": "Mongolia", "MS": "Montserrat", "MA": "Morocco", "MZ": "Mozambique", "MM": "Myanmar", "NA": "Namibia", "NR": "Nauru", "NP": "Nepal", "NL": "Netherlands", "NC": "France, DOM-TOM New Caledonia", "NZ": "New Zealand", "NI": "Nicaragua", "NE": "Niger", "NG": "Nigeria", "NU": "Niue", "NF": "Norfolk Island", "MP": "Northern Mariana Islands", "NO": "Norway", "OM": "Oman", "PK": "Pakistan", "PW": "<PERSON><PERSON>", "PS": "Palestine", "PA": "Panama", "PG": "Papua New Guinea", "PY": "Paraguay", "PE": "Peru", "PH": "Philippines", "PN": "Pitcairn", "PL": "Poland", "PT": "Portugal", "PR": "Puerto Rico", "QA": "Qatar", "RE": "France, DOM-TOM Réunion", "RO": "Romania", "RU": "Russia", "RW": "Rwanda", "SH": "St. Helena", "KN": "Saint Kitts and Nevis", "LC": "Saint Lucia", "PM": "France,St.Pierre and Miquelon", "SM": "San Marino", "ST": "Sao Tome and Principe", "SA": "Saudi Arabia", "SN": "Senegal", "RS": "Serbia", "SC": "Seychelles", "SL": "Sierra Leone", "SG": "Singapore", "SK": "Slovakia (Slovak Republic)", "SI": "Slovenia", "SB": "Solomon Islands", "SO": "Somalia", "ZA": "South Africa", "GS": "South Georgia and The South Sandwich Islands", "ES": "Spain", "LK": "Sri Lanka", "SD": "Sudan", "SR": "Suriname", "SJ": "Svalbard and Jan Mayen Islands", "SZ": "Swaziland", "SE": "Sweden", "CH": "Switzerland", "SY": "Syrian Arab Republic", "TW": "Taiwan, China", "TJ": "Tajikistan", "TZ": "Tanzania, United Republic of", "TH": "Thailand", "TG": "Togo", "TK": "Tokelau", "TO": "Tonga", "TT": "Trinidad and Tobago", "TN": "Tunisia", "TR": "Turkey", "TM": "Turkmenistan", "TC": "Turks and Caicos Islands", "TV": "Tuvalu", "UG": "Uganda", "UA": "Ukraine", "AE": "United Arab Emirates", "GB": "United Kingdom", "UM": "United States Minor Outlying Islands", "US": "United States", "UY": "Uruguay", "VI": "Virgin Islands (U.S.)", "UZ": "Uzbekistan", "VU": "Vanuatu", "VE": "Venezuela", "VN": "Vietnam", "WF": "France, DOM-TOM Wallis and Futuna", "EH": "Western Sahara", "WS": "Samoa", "YE": "Yemen", "VA": "Vatican City State (Holy See)", "ZM": "Zambia", "ZW": "Zimbabwe", "VC": "Saint Vincent and the Grenadines", "CD": "Congo, The Democratic Republic", "AX": "Aland Islands", "MF": "Saint <PERSON>", "AN": "Netherlands Antilles", "BQ": "Caribbean Netherlands", "SX": "Sint Maarten", "BL": "<PERSON>", "VG": "British Virgin Islands", "GG": "Guernsey", "ME": "Montenegro", "XK": "Kosovo", "CW": "Curaçao", "IM": "Isle of Man", "AC": "Ascension Island", "DM": "Dominica", "JE": "Jersey", "SS": "South Sudan", "TA": "<PERSON>"}, "AR": {"AUTONOMOUSCITYOFBUENOSAIRES": "Buenos Aires (Ciudad),", "BUENOSAIRES": "Buenos Aires (Provincia),", "CATAMARCA": "Catamarca", "CHACO": "Chaco", "CHUBUT": "<PERSON><PERSON>", "CORRIENTES": "Corrientes", "CORDOVA": "Córdoba", "BETWEENRIVERS": "Entre Ríos", "FORMOSA": "Formosa", "JUJUY": "<PERSON><PERSON><PERSON>", "LAPAMPA": "La Pampa", "LARIOJA": "La Rioja", "MENDOZA": "Mendoza", "MISIONES": "Misiones", "NEUQUEN": "Neuquén", "RIOONEGRO": "Río Negro", "SALTA": "Salta", "SANJUAN": "San Juan", "SANLUIS": "San Luis", "SANTACRUZ": "Santa Cruz", "SANTAFE": "Santa Fe", "SANTIAGODELESTERO": "Santiago del Estero", "TIERRADELFUEGO": "Tierra del Fuego", "TUCUMAN": "Tucumán"}, "AU": {"ACT": "Australian Capital Territory", "NSW": "New South Wales", "NT": "Northern Territory", "QLD": "Queensland", "SA": "South Australia", "TAS": "Tasmania", "VIC": "Victoria", "WA": "Western Australia"}, "AT": {"Oberosterreich": "Oberosterreich", "Salzburg": "Salzburg", "Karnten": "<PERSON><PERSON><PERSON>", "Steiermark": "Steiermark", "Burgenland": "Burgenland", "Voralberg": "<PERSON><PERSON><PERSON>", "Wien": "Wien", "Niedersterreich": "Niedersterreich", "Tirol": "Tirol"}, "BR": {"AC": "Acre", "AL": "Alagoas", "AP": "Amapá", "AM": "Amazonas", "BA": "Bahia", "CE": "Ceará", "DF": "Distrito Federal", "ES": "Espírito Santo", "GO": "Goiás", "MA": "Maranhão", "MT": "<PERSON><PERSON>", "MS": "Mato Grosso do Sul", "MG": "Minas Gerais", "PR": "Paraná", "PB": "Paraíba", "PA": "Pará", "PE": "Pernambuco", "PI": "Piauí", "RN": "Rio Grande do Norte", "RS": "Rio Grande do Sul", "RJ": "Rio de Janeiro", "RO": "Rondônia", "RR": "Roraima", "SC": "Santa Catarina", "SE": "<PERSON><PERSON><PERSON>", "SP": "São Paulo", "TO": "Tocantins"}, "CA": {"AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NL": "Newfoundland and Labrador", "NB": "New Brunswick", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon"}, "CL": {"AP": "Arica y Parinacota", "TA": "Tarapacá", "AN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AT": "Atacama", "CO": "Coquimbo", "VS": "Valparaíso", "RM": "Santiago Metropolitan", "LI": "Libertador General <PERSON>", "ML": "<PERSON><PERSON>", "NB": "<PERSON><PERSON><PERSON>", "BI": "Bío Bío", "AR": "Araucanía", "LR": "Los Ríos", "LL": "Los Lagos", "AI": "<PERSON><PERSON><PERSON>", "MA": "Magallanes Region"}, "CN": {"GD": "guangdong", "SH": "shanghai", "SZ": "<PERSON><PERSON><PERSON>", "BJ": "beijing", "YN": "Yunnan", "NM": "Inner Mongolia", "QH": "Qinghai", "JL": "<PERSON><PERSON>", "SC": "Sichuan", "TJ": "Tianjin", "NX": "Ningxia", "AH": "<PERSON><PERSON>", "SD": "Shandong", "SX": "Shanxi", "HL": "Heilongjiang", "GX": "Guangxi", "XJ": "Xinjiang", "JS": "Jiangsu", "JX": "Jiangxi", "HE": "Hebei", "HA": "<PERSON><PERSON>", "ZJ": "Zhejiang", "HI": "Hainan", "HB": "Hubei", "HN": "Hunan", "GS": "Gansu", "FJ": "Fujian", "YZ": "Tibet", "GZ": "Guizhou", "LN": "Liaoning", "CQ": "Chongqing", "SN": "Shaanxi"}, "CO": {"DC": "Capital District", "AMA": "Amazonas", "ANT": "Antioquia", "ARA": "Arauca", "ATL": "Atlántico", "BOL": "Bolívar", "BOY": "Boyacá", "CAL": "Caldas", "CAQ": "Caquetá", "CAS": "<PERSON><PERSON><PERSON>", "CAU": "Cauca", "CES": "Cesar", "CHO": "<PERSON><PERSON><PERSON>", "CUN": "Córdoba", "GUA": "Cundinamarca", "GUV": "Guaviare", "HUI": "<PERSON><PERSON>", "LAG": "La Guajira", "MAG": "Magdalena", "MET": "Meta", "NAR": "<PERSON><PERSON><PERSON>", "NSA": "Norte de Santander", "PUT": "<PERSON><PERSON><PERSON>", "QUI": "Quind<PERSON>", "RIS": "Risaralda", "SAP": "San Andrés & Providencia", "SAN": "Santander", "SUC": "<PERSON><PERSON>", "TOL": "Tolima", "VAC": "Valle del Cauca", "VAU": "<PERSON><PERSON><PERSON><PERSON>", "VID": "<PERSON><PERSON><PERSON>"}, "EG": {"SHR": "Al Sharqia", "ALX": "Alexandria", "ASN": "<PERSON><PERSON>", "AST": "<PERSON><PERSON><PERSON>", "BH": "<PERSON><PERSON><PERSON>", "BNS": "<PERSON><PERSON>", "C": "Cairo", "DK": "Dakahlia", "DT": "<PERSON><PERSON><PERSON>", "FYM": "Faiyu<PERSON>", "GH": "Gharbia", "GZ": "Giza", "HU": "<PERSON><PERSON><PERSON>", "IS": "Ismailia", "KFS": "<PERSON><PERSON><PERSON>", "LX": "<PERSON><PERSON><PERSON>", "MT": "<PERSON><PERSON><PERSON>", "MN": "<PERSON><PERSON>", "MNF": "<PERSON><PERSON><PERSON>", "WAD": "New Valley", "SIN": "North Sinai", "PTS": "Port Said", "KB": "Qalyubia", "KN": "<PERSON><PERSON>", "BA": "Red Sea", "SHG": "<PERSON><PERSON><PERSON>", "JS": "South Sinai", "SUZ": "Suez"}, "DE": {"Baden-Wrttemberg": "Baden-Wrttemberg", "Bayern": "Bayern", "Berlin": "Berlin", "Brandenburg": "Brandenburg", "Bremen": "Bremen", "Hamburg": "Hamburg", "Hessen": "Hessen", "Mecklenburg-Vorpommern": "Mecklenburg-Vorpommern", "Niedersachsen": "Niedersachsen", "Nordrhein-Westfalen": "Nordrhein-Westfalen", "Rheinland-Pfalz": "Rheinland-Pfalz", "Saarland": "Saarland", "Sachsen": "Sachsen", "Sachsen-Anhalt": "Sachsen-Anhalt", "Schleswig-Holstein": "Schleswig-Holstein", "Thuringen": "Thuringen"}, "GT": {"AVE": "Alta Verapaz", "BVE": "Baja Verapaz", "CMT": "Chimaltenango", "CQM": "<PERSON><PERSON><PERSON><PERSON>", "EPR": "El Progreso", "ESC": "Escuintla", "GUA": "Guatemala", "HUE": "Huehuetenango", "IZA": "<PERSON><PERSON><PERSON>", "JAL": "Jalapa", "JUT": "Ju<PERSON>pa", "PET": "<PERSON><PERSON>", "QUE": "Quetzaltenango", "QUI": "<PERSON><PERSON><PERSON><PERSON>", "RET": "<PERSON><PERSON><PERSON><PERSON>", "SAC": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SMA": "San Marcos", "SRO": "Santa Rosa", "SOL": "Sololá", "SUC": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TOT": "Totonicapán", "ZAC": "Zacapa"}, "IN": {"ANDAMANANDNICOBARISLANDS": "Andaman and Nicobar Islands", "ANDHRAPRADESH": "Andhra Pradesh", "APO": "Army Post Office", "ARUNACHALPRADESH": "Arunachal Pradesh", "ASSAM": "Assam", "BIHAR": "Bihar", "CHANDIGARH": "Chandigarh", "CHHATTISGARH": "Chhattisgarh", "DADRAANDNAGARHAVELI": "Dadra and Nagar Haveli", "DAMANANDDIU": "<PERSON><PERSON> and <PERSON><PERSON>", "DELHI": "Delhi", "GOA": "Goa", "GUJARAT": "Gujarat", "HARYANA": "Haryana", "HIMACHALPRADESH": "Himachal Pradesh", "JAMMUANDKASHMIR": "Jammu and Kashmir", "JHARKHAND": "Jharkhand", "KARNATAKA": "Karnataka", "KERALA": "Kerala", "LAKSHADWEEP": "Lakshadweep", "MADHYAPRADESH": "Madhya Pradesh", "MAHARASHTRA": "Maharashtra", "MANIPUR": "Manipur", "MEGHALAYA": "<PERSON><PERSON><PERSON>", "MIZORAM": "Mizoram", "NAGALAND": "Nagaland", "ODISHA": "Odisha", "PUDUCHERRY": "Puducherry", "PUNJAB": "Punjab", "RAJASTHAN": "Rajasthan", "SIKKIM": "Sikkim", "TAMILNADU": "Tamil Nadu", "TELANGANA": "Telangana", "TRIPURA": "<PERSON>ura", "UTTARPRADESH": "Uttar Pradesh", "UTTARAKHAND": "Uttarakhand", "WESTBENGAL": "West Bengal"}, "ID": {"ID-BA": "Bali", "ID-BB": "Bangka Belitung", "ID-BT": "<PERSON><PERSON>", "ID-BE": "<PERSON><PERSON><PERSON><PERSON>", "ID-YO": "DI Yogyakarta", "ID-JK": "DKI Jakarta", "ID-GO": "Gorontalo", "ID-JA": "Jambi", "ID-JB": "<PERSON>awa Barat", "ID-JT": "Jawa Tengah", "ID-JI": "<PERSON><PERSON>", "ID-KB": "Kalimantan Barat", "ID-KS": "<PERSON><PERSON><PERSON>", "ID-KT": "Kalimantan Tengah", "ID-KI": "<PERSON><PERSON><PERSON>", "ID-KU": "Kalimantan Utara", "ID-KR": "<PERSON><PERSON><PERSON><PERSON>", "ID-LA": "Lampung", "ID-MA": "Maluku", "ID-MU": "Maluku Utara", "ID-AC": "<PERSON><PERSON><PERSON><PERSON>", "ID-NB": "Nusa Tenggara Barat", "ID-NT": "Nusa Tenggara Timur", "ID-PA": "Papua", "ID-PB": "Papua Barat", "ID-RI": "<PERSON><PERSON><PERSON>", "ID-SR": "Sulawesi Barat", "ID-SN": "Sulaw<PERSON>", "ID-ST": "Sulawesi Ten<PERSON>", "ID-SG": "Sulawesi Tenggara", "ID-SA": "Sulawesi Utara", "ID-SB": "Sumatera Barat", "ID-SS": "<PERSON><PERSON><PERSON>", "ID-SU": "<PERSON><PERSON><PERSON>"}, "IE": {"CW": "<PERSON><PERSON>", "CN": "Cavan", "CE": "<PERSON>", "CO": "Cork", "DL": "Donegal", "D": "Dublin", "G": "Galway", "KY": "Kerry", "KE": "Kildare", "KK": "Kilkenny", "LS": "<PERSON><PERSON>", "LM": "Leitrim", "LK": "Limerick", "LD": "<PERSON><PERSON>", "LH": "<PERSON><PERSON>", "MO": "Mayo", "MH": "Meat<PERSON>", "MN": "<PERSON><PERSON>", "OY": "Offaly", "RN": "Roscommon", "SO": "Sligo", "TA": "Tipperary", "WD": "Waterford", "WH": "Westmeath", "WX": "Wexford", "WW": "Wicklow"}, "IT": {"AG": "Agrigento", "AL": "Alessandria", "AN": "Ancona", "AO": "Aosta", "AR": "Arezzo", "AP": "<PERSON><PERSON><PERSON>", "AT": "<PERSON><PERSON>", "AV": "<PERSON><PERSON><PERSON>", "BA": "Bari", "BT": "Barletta-Andria-Trani", "BL": "<PERSON><PERSON>", "BN": "Benevento", "BG": "Bergamo", "BI": "Biella", "BO": "Bologna", "BZ": "Bolzano", "BS": "Brescia", "BR": "Brindisi", "CA": "Cagliari", "CL": "Caltanissetta", "CB": "Campobasso", "CI": "Carbonia-Iglesias", "CE": "Caserta", "CT": "Catania", "CZ": "Cat<PERSON>ro", "CH": "Chieti", "CO": "Como", "CS": "Cosenza", "CR": "Cremona", "KR": "Crotone", "CN": "Cuneo", "EN": "<PERSON><PERSON>", "FM": "Fermo", "FE": "<PERSON><PERSON><PERSON>", "FI": "Firenze", "FG": "Foggia", "FC": "Forlì-Cesena", "FR": "Frosinone", "GE": "<PERSON><PERSON>", "GO": "Gorizia", "GR": "Grosseto", "IM": "Imperia", "IS": "Isernia", "AQ": "L'Aquila", "SP": "La Spezia", "LT": "Latina", "LE": "Lecce", "LC": "<PERSON><PERSON>", "LI": "Livorno", "LO": "<PERSON><PERSON>", "LU": "Lucca", "MC": "Macerata", "MN": "<PERSON><PERSON><PERSON>", "MS": "Massa-Carrara", "MT": "<PERSON><PERSON>", "VS": "Medio Campidano", "ME": "Messina", "MI": "Milano", "MO": "Modena", "MB": "Monza e della Brianza", "NA": "Napoli", "NO": "Novara", "NU": "<PERSON><PERSON><PERSON>", "OG": "Ogliastra", "OT": "Olbia-Tempio", "OR": "Oristano", "PD": "<PERSON><PERSON><PERSON>", "PA": "Palermo", "PR": "Parma", "PV": "Pavia", "PG": "Perugia", "PU": "Pesaro e Urbino", "PE": "Pescara", "PC": "Piacenza", "PI": "Pisa", "PT": "Pistoia", "PN": "Pordenone", "PO": "Prato", "RG": "<PERSON><PERSON><PERSON>", "RA": "<PERSON><PERSON>", "RC": "Reggio Calabria", "RE": "Reggio Emilia", "RI": "Riet<PERSON>", "RN": "<PERSON><PERSON><PERSON>", "RM": "Roma", "RO": "Rovigo", "SA": "Salerno", "SS": "Sassari", "SV": "<PERSON><PERSON><PERSON>", "SI": "Siena", "SR": "Sir<PERSON><PERSON>", "SO": "Sondr<PERSON>", "TA": "<PERSON><PERSON>", "TE": "Teramo", "TR": "<PERSON><PERSON><PERSON>", "TO": "Torino", "TP": "<PERSON><PERSON><PERSON>", "TN": "Trento", "TV": "Treviso", "TS": "Trieste", "UD": "Udine", "VA": "<PERSON><PERSON><PERSON>", "VE": "Venezia", "VB": "Verbano-Cusio-Ossola", "VC": "<PERSON><PERSON><PERSON><PERSON>", "VR": "Verona", "VV": "<PERSON><PERSON><PERSON>", "VI": "Vicenza", "VT": "Viterbo", "PZ": "Potenza"}, "JP": {"AICHI-KEN": "<PERSON><PERSON>", "AKITA-KEN": "<PERSON><PERSON><PERSON>", "AOMORI-KEN": "<PERSON><PERSON><PERSON>", "CHIBA-KEN": "Chiba", "EHIME-KEN": "<PERSON><PERSON><PERSON>", "FUKUI-KEN": "<PERSON><PERSON><PERSON>", "FUKUOKA-KEN": "<PERSON><PERSON><PERSON>", "FUKUSHIMA-KEN": "Fukushima", "GIFU-KEN": "Gifu", "GUNMA-KEN": "<PERSON><PERSON>", "HIROSHIMA-KEN": "Hiroshima", "HOKKAIDO": "Hokkaido", "HYOGO-KEN": "Hyogo", "IBARAKI-KEN": "<PERSON><PERSON><PERSON>", "ISHIKAWA-KEN": "<PERSON><PERSON><PERSON>", "IWATE-KEN": "Iwate", "KAGAWA-KEN": "Kagawa", "KAGOSHIMA-KEN": "Kagoshima", "KANAGAWA-KEN": "Kanagawa", "KOCHI-KEN": "<PERSON><PERSON>", "KUMAMOTO-KEN": "<PERSON><PERSON>", "KYOTO-FU": "Kyoto", "MIE-KEN": "Mie Prefecture", "MIYAGI-KEN": "<PERSON><PERSON><PERSON>", "MIYAZAKI-KEN": "<PERSON><PERSON><PERSON>", "NAGANO-KEN": "Nagano", "NAGASAKI-KEN": "Nagasaki", "NARA-KEN": "<PERSON><PERSON>", "NIIGATA-KEN": "Niigata", "OITA-KEN": "<PERSON><PERSON>", "OKAYAMA-KEN": "<PERSON><PERSON>", "OKINAWA-KEN": "Okinawa", "OSAKA-FU": "Osaka", "SAGA-KEN": "Saga", "SAITAMA-KEN": "<PERSON><PERSON><PERSON>", "SHIGA-KEN": "Shiga", "SHIMANE-KEN": "<PERSON><PERSON><PERSON>", "SHIZUOKA-KEN": "Shizuoka", "TOCHIGI-KEN": "Tochigi", "TOKUSHIMA-KEN": "Tokushima", "TOKYO-TO": "Tokyo", "TOTTORI-KEN": "Tottori", "TOYAMA-KEN": "Toyama", "WAKAYAMA-KEN": "<PERSON><PERSON><PERSON>", "YAMAGATA-KEN": "Yamagata", "YAMAGUCHI-KEN": "<PERSON><PERSON><PERSON>", "YAMANASHI-KEN": "<PERSON><PERSON><PERSON>"}, "KR": {"Busan": "Busan", "NorthChungcheong": "North Chungcheong", "SouthChungcheong": "South Chungcheong", "Daegu": "Daegu", "Daejeon": "Daejeon", "Gangwon": "Gangwon", "GwangjuCity": "Gwangju City", "NorthGyeongsang": "North Gyeongsang", "Gyeonggi": "Gyeonggi", "SouthGyeongsang": "South Gyeongsang", "Incheon": "Incheon", "Jeju": "<PERSON><PERSON>", "NorthJeolla": "North Jeolla", "SouthJeolla": "South Jeolla", "Sejong": "Sejong", "Seoul": "Seoul", "Ulsan": "<PERSON><PERSON><PERSON>"}, "MY": {"JHR": "<PERSON><PERSON>", "KDH": "Kedah", "KTN": "<PERSON><PERSON><PERSON>", "KUL": "Kuala Lumpur", "LBN": "<PERSON><PERSON>", "MLK": "Malacca", "NSN": "<PERSON><PERSON><PERSON>", "PHG": "<PERSON><PERSON>", "PNG": "Penang", "PRK": "<PERSON><PERSON>", "PLS": "<PERSON><PERSON>", "PJY": "<PERSON><PERSON><PERSON>", "SBH": "Sabah", "SWK": "Sarawak", "SGR": "Selangor", "TRG": "Terengganu"}, "MX": {"AGS": "Aguascalientes", "BC": "Baja California", "BCS": "Baja California Sur", "CAMP": "Campeche", "CHIS": "Chiapas", "CDMX": "Ciudad de México", "COAH": "Coahuila", "COL": "Colima", "DF": "Distrito Federal", "DGO": "Durango", "MEX": "Estado de México", "GTO": "Guanajuato", "GRO": "Guerrero", "HGO": "Hidalgo", "JAL": "Jalisco", "MICH": "Michoacán", "MOR": "<PERSON><PERSON>", "NAY": "Nayarit", "NL": "Nuevo León", "OAX": "Oaxaca", "PUE": "Puebla", "QRO": "<PERSON><PERSON><PERSON><PERSON>", "QROO": "Quintana Roo", "SLP": "San Luis Potosí", "SIN": "Sinaloa", "SON": "Sonora", "TAB": "Tabasco", "TAMPS": "Tamaulip<PERSON>", "TLAX": "Tlaxcala", "VER": "Veracruz", "YUC": "Yucatán", "ZAC": "Zacatecas", "CHIH": "Chihuahua"}, "NZ": {"AUK": "Auckland", "BOP": "Bay of Plenty", "CAN": "Canterbury", "GIS": "Gisborne", "HKB": "Hawke’s Bay", "MWT": "Manawatu-Wanganui", "MBH": "Marlborough", "NSN": "<PERSON>", "NTL": "Northland", "OTA": "Otago", "STL": "Southland", "TKI": "<PERSON><PERSON><PERSON>", "TAS": "Tasman", "WKO": "Waikato", "WGN": "Wellington", "WTC": "West Coast"}, "NG": {"AB": "Abia", "FC": "Federal Capital Territory", "AD": "<PERSON><PERSON>", "AK": "Akwa Ibom", "AN": "Anambra", "BA": "<PERSON><PERSON>", "BY": "Bayelsa", "BE": "<PERSON><PERSON>", "BO": "<PERSON><PERSON>", "CR": "Cross River", "DE": "Delta", "EB": "<PERSON><PERSON><PERSON>", "ED": "Edo", "EK": "<PERSON><PERSON><PERSON>", "EN": "Enugu", "GO": "<PERSON><PERSON>", "IM": "Imo", "JI": "Jigawa", "KD": "Ka<PERSON><PERSON>", "KN": "<PERSON><PERSON>", "KT": "<PERSON><PERSON><PERSON>", "KE": "<PERSON><PERSON><PERSON>", "KO": "<PERSON><PERSON>", "KW": "Kwara", "LA": "Lagos", "NA": "Nasarawa", "NI": "Niger", "OG": "<PERSON><PERSON>", "ON": "Ondo", "OS": "<PERSON><PERSON>", "OY": "Oyo", "PL": "Plateau", "RI": "Rivers", "SO": "Sokoto", "TA": "Taraba", "YO": "<PERSON><PERSON>", "ZA": "<PERSON><PERSON><PERSON><PERSON>"}, "PA": {"BocasdelToro": "Bocas del Toro", "Chiriqui": "Chiriquí", "Cocle": "Co<PERSON>lé", "Colon": "Colón", "Darien": "<PERSON><PERSON><PERSON>", "Embera": "Emberá", "Herrera": "<PERSON>", "GunaYala": "<PERSON><PERSON>", "LosSantos": "Los Santos", "Ngöbe-Buglé": "Ngöbe-Buglé", "Panama": "Panamá", "WestPanama": "West Panamá", "Veraguas": "Veraguas"}, "PE": {"AMA": "Amazonas", "ANC": "Ancash", "APU": "Apurímac", "ARE": "Arequipa", "AYA": "Ayacucho", "CAJ": "Cajamarca", "CAL": "El Callao", "CUS": "Cusco", "HUV": "Huancavelica", "HUC": "Huánuco", "ICA": "Ica", "JUN": "Junín", "LAL": "La Libertad", "LAM": "Lambayeque", "LIM": "Lima Region", "LMA": "Lima", "LOR": "Loreto", "MDD": "Madre de Dios", "MOQ": "Moquegua", "PAS": "Pasco", "PIU": "<PERSON><PERSON>", "PUN": "<PERSON><PERSON>", "SAM": "San Martín", "TAC": "Tacna", "TUM": "Tumbes", "UCA": "Ucayali"}, "PH": {"PH-ABR": "Abra", "PH-AGN": "Agusan del Norte", "PH-AGS": "Agusan del Sur", "PH-AKL": "Aklan", "PH-ALB": "Albay", "PH-ANT": "Antique", "PH-APA": "Apayao", "PH-AUR": "Aurora", "PH-BAS": "<PERSON><PERSON>", "PH-BAN": "Bataan", "PH-BTN": "Batanes", "PH-BTG": "Batangas", "PH-BEN": "<PERSON><PERSON><PERSON>", "PH-BIL": "Biliran", "PH-BOH": "<PERSON><PERSON>", "PH-BUK": "Bukidnon", "PH-BUL": "Bulacan", "PH-CAG": "<PERSON><PERSON><PERSON>", "PH-CAN": "Camarines Norte", "PH-CAS": "Camarines Sur", "PH-CAM": "Camiguin", "PH-CAP": "Capiz", "PH-CAT": "Catanduanes", "PH-CAV": "Cavite", "PH-CEB": "Cebu", "PH-NCO": "Cotabato", "PH-DVO": "Davao Occidental", "PH-DAO": "Davao Oriental", "PH-COM": "Compostela Valley", "PH-DAV": "Davao del Norte", "PH-DAS": "Davao del Sur", "PH-DIN": "Dinagat Islands", "PH-EAS": "Eastern Samar", "PH-GUI": "<PERSON><PERSON><PERSON><PERSON>", "PH-IFU": "Ifugao", "PH-ILN": "Ilocos Norte", "PH-ILS": "Ilocos Sur", "PH-ILI": "Iloilo", "PH-ISA": "Isabela", "PH-KAL": "Kalinga", "PH-LUN": "La Union", "PH-LAG": "Laguna", "PH-LAN": "Lanao del Norte", "PH-LAS": "Lanao del Sur", "PH-LEY": "Leyte", "PH-MAG": "Maguindanao", "PH-MAD": "Marinduque", "PH-MAS": "Masbate", "PH-00": "Metro Manila", "PH-MSC": "Misamis Occidental", "PH-MSR": "Misamis Oriental", "PH-MOU": "Mountain", "PH-NEC": "Negros Occidental", "PH-NER": "Negros Oriental", "PH-NSA": "Northern Samar", "PH-NUE": "Nueva Ecija", "PH-NUV": "Nueva Vizcaya", "PH-MDC": "Occidental Mindoro", "PH-MDR": "Oriental Mindoro", "PH-PLW": "Palawan", "PH-PAM": "Pampanga", "PH-PAN": "Pangasinan", "PH-QUE": "Quezon", "PH-QUI": "<PERSON><PERSON><PERSON>", "PH-RIZ": "Rizal", "PH-ROM": "R<PERSON><PERSON>", "PH-WSA": "Samar", "PH-SAR": "Sarangani", "PH-SIG": "Si<PERSON><PERSON><PERSON>", "PH-SOR": "Sorsogon", "PH-SCO": "South Cotabato", "PH-SLE": "Southern Leyte", "PH-SUK": "<PERSON>", "PH-SLU": "Sulu", "PH-SUN": "Surigao del Norte", "PH-SUR": "Surigao del Sur", "PH-TAR": "Tarlac", "PH-TAW": "Tawi-Tawi", "PH-ZMB": "<PERSON><PERSON><PERSON><PERSON>", "PH-ZSI": "Zamboanga Sibugay", "PH-ZAN": "Zamboanga del Norte", "PH-ZAS": "Zamboanga del Sur"}, "PT": {"Azores": "Azores", "Aveiro": "Aveiro", "Beja": "<PERSON><PERSON>", "Braga": "Braga", "Braganza": "Bragança", "CasteloBranco": "<PERSON><PERSON>", "Coimbra": "Coimbra", "Evora": "É<PERSON><PERSON>", "Faro": "Faro", "Guarda": "Guarda", "Leiria": "<PERSON><PERSON><PERSON>", "Lisbon": "Lisbon", "Madeira": "Madeira", "Portalegre": "Portalegre", "Porto": "Porto", "Santarem": "Santa<PERSON><PERSON>", "Setubal": "<PERSON><PERSON><PERSON>", "VianadoCastelo": "Viana do Castelo", "VilaReal": "Vila Real", "Viseu": "<PERSON><PERSON><PERSON>"}, "RO": {"AB": "Alba", "AR": "Arad", "AG": "<PERSON><PERSON><PERSON>", "BC": "<PERSON><PERSON><PERSON><PERSON>", "BH": "<PERSON><PERSON><PERSON>", "BN": "Bistriţa-Năsăud", "BT": "<PERSON><PERSON><PERSON><PERSON>", "BR": "Brăila", "BV": "<PERSON><PERSON><PERSON><PERSON>", "B": "Bucharest", "BZ": "<PERSON><PERSON><PERSON><PERSON>", "CS": "Caraș-Sever<PERSON>", "CJ": "<PERSON><PERSON><PERSON>", "CT": "Constanța", "CV": "<PERSON><PERSON><PERSON>", "CL": "C<PERSON><PERSON><PERSON><PERSON><PERSON>i", "DJ": "<PERSON><PERSON><PERSON>", "DB": "Dâmbovița", "GL": "Gala<PERSON>i", "GR": "<PERSON><PERSON><PERSON><PERSON>", "GJ": "<PERSON><PERSON><PERSON>", "HR": "<PERSON><PERSON><PERSON>", "HD": "Hunedoara", "IL": "Ialomița", "IS": "<PERSON><PERSON><PERSON><PERSON>", "IF": "<PERSON><PERSON><PERSON>", "MM": "Maramureş", "MH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MS": "<PERSON><PERSON><PERSON>", "NT": "Neamţ", "OT": "Olt", "PH": "<PERSON><PERSON><PERSON>", "SJ": "<PERSON><PERSON><PERSON><PERSON>", "SM": "Satu <PERSON>", "SB": "Sibiu", "SV": "Su<PERSON>va", "TR": "Teleorman", "TM": "<PERSON><PERSON><PERSON>", "TL": "<PERSON><PERSON><PERSON>", "VL": "V<PERSON><PERSON><PERSON>", "VS": "<PERSON><PERSON><PERSON><PERSON>", "VN": "Vrancea"}, "RU": {"ALT": "Altai Krai", "AL": "Altai", "AMU": "Amur", "ARK": "Arkhangelsk", "AST": "Astrakhan", "BEL": "Belgorod", "BRY": "Bryansk", "CE": "Chechen", "CHE": "Chelyabinsk", "CHU": "Chukotka Okrug", "CU": "Chuvash", "IRK": "Irkutsk", "IVA": "Ivanovo", "YEV": "Jewish", "KB": "Kabardino-Balkar", "KGD": "Kaliningrad", "KLU": "Kaluga", "KAM": "Kamchatka Krai", "KC": "Karachay-Cherkess", "KEM": "Kemerovo", "KHA": "Khabarovsk Krai", "KHM": "Khanty-Mansi", "KIR": "<PERSON><PERSON>", "KO": "<PERSON><PERSON>", "KOS": "<PERSON><PERSON><PERSON>", "KDA": "Krasnodar Krai", "KYA": "Krasnoyarsk Krai", "KGN": "Kurgan", "KRS": "Kursk", "LEN": "Leningrad", "LIP": "Lipetsk", "MAG": "<PERSON><PERSON><PERSON>", "ME": "Mari El", "MOW": "Moscow", "MOS": "Moscow Province", "MUR": "Murmansk", "NIZ": "Nizhny Novgorod", "NGR": "Novgorod", "NVS": "Novosibirsk", "OMS": "Omsk", "ORE": "Orenburg", "ORL": "Oryol", "PNZ": "<PERSON><PERSON>", "PER": "Perm Krai", "PRI": "Primorsky Krai", "PSK": "Pskov", "AD": "Adygea", "BA": "Bashkortostan", "BU": "Buryat", "DA": "Dagestan", "IN": "Ingushetia", "KL": "Kalmykia", "KR": "Karelia", "KK": "Khakassia", "MO": "Mordovia", "SE": "North Ossetia-Alania", "TA": "Tatarstan", "ROS": "<PERSON><PERSON><PERSON>", "RYA": "R<PERSON><PERSON>", "SPE": "Saint Petersburg", "SA": "Sakha", "SAK": "Sakhalin", "SAM": "Samara", "SAR": "<PERSON><PERSON>", "SMO": "Smolensk", "STA": "Stavropol Krai", "SVE": "Sverdlovsk", "TAM": "Tambov", "TOM": "Tomsk", "TUL": "<PERSON><PERSON>", "TVE": "Tver", "TYU": "Tyumen", "TY": "<PERSON><PERSON>", "UD": "Udmurt", "ULY": "Ulyanovsk", "VLA": "Vladimir", "VGG": "Volgograd", "VLG": "Vologda", "VOR": "Voronezh", "YAN": "Yamalo-Nenets Okrug", "YAR": "Yaroslavl", "ZAB": "Zabaykalsky Krai"}, "ZA": {"EC": "Eastern Cape", "FS": "Free State", "GT": "Gauteng", "NL": "KwaZulu-Natal", "LP": "Limpopo", "MP": "Mpumalanga", "NW": "North West", "NC": "Northern Cape", "WC": "Western Cape"}, "ES": {"Teruel": "Teruel", "Toledo": "Toledo", "Valencia": "Valencia", "Valladolid": "Valladoli<PERSON>", "Vizcaya": "Vizcaya", "Zamora": "Zamora", "Zaragoza": "Zaragoza", "ACorua": "A Corua", "Alava": "Alava", "Albacete": "Albacete", "Alicante": "Alicante", "Almeria": "Almeria", "Asturias": "Asturias", "Avila": "A<PERSON>", "Badajoz": "Badajoz", "Baleares": "<PERSON><PERSON><PERSON>", "Barcelona": "Barcelona", "Burgos": "Burgos", "Caceres": "Caceres", "Cadiz": "Cadiz", "Cantabria": "Cantabria", "Castellon": "<PERSON><PERSON><PERSON>", "Ceuta": "<PERSON><PERSON>", "CiudadReal": "Ciudad Real", "Cordoba": "Cordoba", "Cuenca": "Cuenca", "Girona": "Girona", "Granada": "Granada", "Guadalajara": "Guadalajara", "Guipuzcoa": "Guipuzcoa", "Huelva": "<PERSON><PERSON><PERSON>", "Huesca": "Huesca", "Jaen": "<PERSON><PERSON>", "LaRioja": "La Rioja", "LasPalmas": "Las Palmas", "Leon": "<PERSON>", "Lleida": "Lleida", "Lugo": "Lugo", "Madrid": "Madrid", "Malaga": "Malaga", "Melilla": "Melilla", "Murcia": "Murcia", "Navarra": "<PERSON><PERSON><PERSON>", "Ourense": "Ourense", "Palencia": "<PERSON><PERSON><PERSON>", "Pontevedra": "<PERSON><PERSON><PERSON>", "Salamanca": "Salamanca", "SantaCruzdeTenerife": "Santa Cruz de Tenerife", "Segovia": "Segovia", "Sevilla": "Sevilla", "Soria": "Soria", "Tarragona": "Tarragona", "Andalusia": "Andalusia", "Aragon": "Aragón", "BalearicIslands": "Balearic Islands", "Biscay": "Biscay", "Canarias": "Canarias", "CastillaLaMancha": "Castilla-La Mancha", "CastillayLeon": "Castilla y León", "Cataluna": "Cataluña", "ComunidadForaldeNavarra": "Comunidad Foral de Navarra", "ComunidadValenciana": "Comunidad Valenciana", "Extremadura": "Extremadura", "Galicia": "Galicia", "IllesBalears": "<PERSON><PERSON>"}, "CH": {"Aargau": "Aargau", "AppenzellInnerrhoden": "Appenzell Innerrhoden", "AppenzellAusserrhoden": "<PERSON><PERSON><PERSON><PERSON> Ausserrhoden", "Bern": "Bern", "Basel-Landschaft": "Basel-Landschaft", "Basel-Stadt": "Basel-Stadt", "Freiburg": "Freiburg", "Genf": "Genf", "Glarus": "<PERSON><PERSON><PERSON>", "Graubnden": "<PERSON><PERSON><PERSON><PERSON>", "Jura": "<PERSON><PERSON>", "Luzern": "Luzern", "Neuenburg": "Neuenburg", "Nidwalden": "Nidwalden", "Obwalden": "Obwalden", "StGallen": "St. Gallen", "Schaffhausen": "Sc<PERSON>ffhausen", "Solothurn": "Solothurn", "Schwyz": "Schwyz", "Thurgau": "Thurgau", "Tessin": "<PERSON><PERSON>", "Uri": "<PERSON><PERSON>", "Waadt": "Waadt", "Wallis": "<PERSON>", "Zug": "<PERSON>ug", "Zrich": "<PERSON><PERSON>"}, "TH": {"AmnatCharoen": "<PERSON><PERSON>", "AngThong": "<PERSON>", "Bangkok": "Bangkok", "BuengKan": "<PERSON>ueng Kan", "BuriRam": "<PERSON><PERSON>", "Chachoengsao": "Chachoengsao", "ChaiNat": "<PERSON><PERSON>", "Chaiyaphum": "Chaiyaphum", "Chanthaburi": "<PERSON><PERSON><PERSON><PERSON>", "ChiangMai": "<PERSON>", "ChiangRai": "<PERSON>", "ChonBuri": "<PERSON><PERSON>", "Chumphon": "Chumphon", "Kalasin": "<PERSON><PERSON><PERSON>", "KamphaengPhet": "Kamphaeng Phet", "Kanchanaburi": "Kanchanaburi", "KhonKaen": "<PERSON><PERSON>", "Krabi": "<PERSON><PERSON><PERSON>", "Lampang": "Lampang", "Lamphun": "<PERSON><PERSON><PERSON>", "Loei": "<PERSON><PERSON>", "LopBuri": "<PERSON><PERSON> B<PERSON>", "MaeHongSon": "<PERSON>", "MahaSarakham": "<PERSON><PERSON>", "Mukdahan": "<PERSON><PERSON><PERSON><PERSON>", "NakhonNayok": "Nakhon Nayok", "NakhonPathom": "Nakhon Pathom", "NakhonPhanom": "Nakhon Phanom", "NakhonRatchasima": "<PERSON><PERSON><PERSON>", "NakhonSawan": "<PERSON><PERSON><PERSON>", "NakhonSiThammarat": "Nakhon Si Thammarat", "Nan": "<PERSON>", "Narathiwat": "<PERSON><PERSON><PERSON><PERSON>", "NongBuaLamphu": "<PERSON><PERSON>", "NongKhai": "<PERSON><PERSON>", "Nonthaburi": "Nonthaburi", "PathumThani": "<PERSON><PERSON>", "Pattani": "<PERSON><PERSON>", "PhangNga": "<PERSON><PERSON>", "Phatthalung": "Phatthalung", "Phatthaya": "<PERSON><PERSON><PERSON><PERSON>", "Phayao": "<PERSON><PERSON><PERSON>", "Phetchabun": "Phetchabun", "Phetchaburi": "Phetchaburi", "Phichit": "<PERSON><PERSON><PERSON>", "Phitsanulok": "Phitsanulok", "PhraNakhonSiAyutthaya": "Phra Nakhon Si Ayutthaya", "Phrae": "Phrae", "Phuket": "Phuke<PERSON>", "PrachinBuri": "<PERSON><PERSON><PERSON>", "PrachuapKhiriKhan": "<PERSON><PERSON><PERSON><PERSON>", "Ranong": "<PERSON><PERSON><PERSON>", "Ratchaburi": "Ratchaburi", "Rayong": "<PERSON><PERSON>", "RoiEt": "R<PERSON>", "SaKaeo": "<PERSON>", "SakonNakhon": "Sakon <PERSON>", "SamutPrakan": "<PERSON><PERSON>", "SamutSakhon": "<PERSON><PERSON>", "SamutSongkhram": "<PERSON><PERSON>", "Saraburi": "Saraburi", "Satun": "Satun", "SiSaKet": "<PERSON>", "SingBuri": "<PERSON>", "Songkhla": "<PERSON><PERSON><PERSON>", "Sukhothai": "Sukhothai", "SuphanBuri": "<PERSON><PERSON><PERSON>", "SuratThani": "Surat Thani", "Surin": "Surin", "Tak": "Tak", "Trang": "<PERSON><PERSON>", "Trat": "Trat", "UbonRatchathani": "<PERSON><PERSON>", "UdonThani": "<PERSON><PERSON>", "UthaiThani": "<PERSON><PERSON><PERSON>", "Uttaradit": "<PERSON><PERSON><PERSON>", "Yala": "<PERSON><PERSON>", "Yasothon": "<PERSON><PERSON><PERSON><PERSON>"}, "AE": {"AZ": "Abu Dhabi", "AJ": "<PERSON><PERSON><PERSON>", "DU": "Dubai", "FU": "<PERSON><PERSON><PERSON><PERSON>", "RK": "<PERSON><PERSON> <PERSON>", "SH": "Sharjah", "UQ": "<PERSON><PERSON>"}, "US": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "FL": "Florida", "GA": "Georgia", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PA": "Pennsylvania", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "DC": "District of Columbia", "PR": "Puerto Rico", "RI": "Rhode Island", "AS": "American Samoa", "FM": "Micronesia", "GU": "Guam", "MH": "Marshall Islands", "MP": "Northern Mariana Islands", "PW": "<PERSON><PERSON>", "VI": "U.S. Virgin Islands", "AA": "Armed Forces Americas", "AE": "Armed Forces Europe", "AP": "Armed Forces Pacific"}, "MM": {"AYA": "Ayeyarwady", "BGO": "Bago", "MGY": "<PERSON><PERSON><PERSON>", "MDY": "Mandalay", "SGG": "Sagaing", "TNT": "<PERSON><PERSON><PERSON><PERSON>", "YGN": "Yangon", "CHN": "<PERSON>", "KCN": "<PERSON><PERSON>", "KYR": "<PERSON><PERSON>", "KYN": "<PERSON><PERSON>", "MON": "Mon", "RKE": "<PERSON><PERSON><PERSON>", "SHN": "<PERSON>", "NPT": "Naypyitaw"}, "HK": {"HK": "Hong Kong Island", "KL": "Kowloon", "NT": "New Territories"}, "TW": {"Taipei": "Taipei City", "Keelung": "Keelung City", "NewTaipei": "New Taipei City", "Yilan": "Yilan County", "HsinchuCity": "Hsinchu City", "HsinchuCounty": "Hsinchu County", "Taoyuan": "Taoyuan City", "Miaoli": "Miaoli County", "Taichung": "Taichung City", "Changhua": "Changhua County", "Nantou": "Nantou County", "ChiayiCity": "Chiayi City", "ChiayiCounty": "Chiayi County", "Yunlin": "Yunlin County", "Tainan": "Tainan City", "Kaohsiung": "Kaohsiung City", "Pingtung": "Pingtung County", "Taitung": "Taitung County", "Hualien": "Hualien County", "Penghu": "Penghu County", "Kinmen": "Kinmen County", "Lianjiang": "Lienchiang County"}, "VN": {"AnGiang": "An Giang province", "BaRiaVungTau": "Ba Ria Vung Tau Province", "BacGiang": "Bac Giang province", "BacKan": "Bac Kan Province", "BacLieu": "Bac Lieu Province", "BacNinh": "Bac Ninh province", "BenTre": "Ben Tre Province", "Pacifythe": "Pacify the province", "BinhDuong": "Binh Duong Province", "BinhPhuoc": "<PERSON><PERSON> Ph<PERSON>", "BinhThuan": "Binh Thuan Province", "CaMau": "Ca Mau Province", "CanTho": "Can Tho city", "CaoBang": "Cao Bang Province", "DaNang": "Da Nang city", "DakLak": "Dak Lak Province", "DakNong": "Dak Nong Province", "DienBien": "Dien Bien Province", "Dongnai": "Dongnai province", "DongThap": "Dong Thap Province", "GiaLai": "Gia Lai province", "HaGiang": "Ha Giang Province", "HaNam": "Ha Nam province", "Hanoi": "Hanoi City", "HaTinh": "Ha Tinh province", "HaiDuong": "Hai Duong Province", "HaiPhong": "Hai Phong city", "HauGiang": "Hau Giang province", "HoChiMinh": "Ho Chi Minh City", "HoaBinh": "Hoa Binh province", "HungYen": "Hung Yen province", "KhanhHoa": "Khanh Hoa province", "KienGiang": "Kien Giang Province", "KonTum": "Kon Tum Province", "LaiChau": "Lai Chau Province", "LamDong": "Lam Dong Province", "LangSon": "Lang Son Province", "LaoCai": "Lao Cai Province", "LongAn": "Long An Province", "NamDinh": "<PERSON>", "NgheAn": "Nghe An province", "NinhBinh": "Ninh Binh province", "NinhThuan": "Ninh Thuan Province", "PhuTho": "Phu Tho province", "PhuYen": "Phu Yen Province", "QuangBinh": "Quang Binh Province", "QuangNam": "Quang Nam Province", "QuangNgai": "Quang Ngai province", "QuangNinh": "Quang Ninh Province", "QuangTri": "Quang Tri province", "SocTrang": "Soc Trang Province", "SonLa": "Son La Province", "TayNinh": "Tay Ninh province", "ThaiBinh": "Thai Binh Province", "ThaiNguyen": "Thai Nguyen province", "ThanhHoa": "Thanh Hoa province", "ThuaThienHue": "Thua Thien Hue province", "TienGiang": "Tien Giang Province", "TraVinh": "Tra Vinh province", "TuyenQuang": "Tuyen Quang Province", "VinhLong": "Vinh Long Province", "VinhPhuc": "Vinh Phuc Province", "YenBai": "Yen Bai Province", "ThuDoHaNoi": "<PERSON><PERSON> <PERSON>"}, "NL": {"Drenthe": "<PERSON><PERSON><PERSON>", "Flevoland": "Flevoland", "Friesland": "<PERSON><PERSON><PERSON>", "Gelderland": "Gelderland", "Groningen": "Groningen", "Limburg": "Limburg", "NoordBrabant": "Noord-Brabant", "NoordHolland": "Noord-Holland", "Overijssel": "Overijssel", "Utrecht": "Utrecht", "Zeeland": "Zeeland", "ZuidHolland": "Zuid-Holland"}, "FR": {"Ain": "Ain", "Aisne": "Aisne", "Allier": "<PERSON><PERSON>", "AlpesMaritimes": "Alpes-Maritimes", "AlpesDeHauteProvence": "Alpes-de-Haute-Provence", "Ardennes": "Ardennes", "Ardecha": "<PERSON><PERSON><PERSON><PERSON>", "Ariege": "Ariège", "Aube": "<PERSON><PERSON>", "Aude": "Aude", "AuvergneRhoneAlps": "Auvergne Rhône-Alpes", "Aveyron": "Aveyr<PERSON>", "BasRhin": "Bas-Rhin", "BouchesDuRhone": "Bouches-du-Rhône", "BurgundyFrancheComte": "Bourgogne Franche-Comté", "Bretagne": "Bretagne", "Calvados": "Calvados", "Cantal": "<PERSON><PERSON>", "CentreValDeLoire": "Centre Val de Loire", "Charente": "<PERSON><PERSON><PERSON>", "CharenteMaritime": "Charente-Maritime", "Cher": "Cher", "Correze": "<PERSON><PERSON><PERSON><PERSON>", "Corse": "Corse", "CorseduSud": "Corse-du-Sud", "Creuse": "Creuse", "GoldenCoast": "Côte-d'Or", "CotesDArmor": "Côtes-d'Armor", "TwoSevres": "Deux-Sèvres", "Dordogne": "Dordogne", "Doubs": "<PERSON><PERSON><PERSON>", "Drome": "Dr<PERSON>", "Essonne": "Essonne", "Eure": "<PERSON><PERSON>", "EureEtLoir": "Eure-et-Loir", "Finistere": "Finistère", "Gard": "Gard", "Gers": "<PERSON><PERSON>", "Gironde": "Gironde", "GrandEst": "Grand Est", "Guadeloupe": "Guadeloupe", "Guyane": "<PERSON><PERSON>", "FrenchGuiana": "<PERSON><PERSON> (française)", "HautRhin": "Haut-Rhin", "HauteCorse": "Haute-Corse", "HauteGaronne": "Haute-Garonne", "HauteLoire": "Haute-Loire", "HauteMarne": "Haute-Marne", "HauteSavoie": "Haute-Savoie", "HauteSaone": "Haute-Saône", "HauteVienne": "Haute-Vienne", "HautesAlpes": "Hautes-Alpes", "HautesPyrenees": "Hautes-Pyrénées", "HautsDeFrance": "Hauts-de-France", "HautsDeSeine": "Hauts-de-Seine", "Hérault": "<PERSON><PERSON><PERSON>", "IlleEtVilaine": "Ille-et-Vilaine", "Indre": "Indre", "IndreEtLoire": "Indre-et-Loire", "Isere": "Isère", "Jura": "<PERSON><PERSON>", "LaReunion": "La Réunion", "Landes": "Landes", "LoirEtCher": "Loir-et-Cher", "Loire": "Loire", "LoireAtlantique": "Loire-Atlantique", "Loiret": "Loiret", "Lot": "Lot", "LotEtGaronne": "Lot-et-Garonne", "Lozere": "Lozère", "MaineEtLoire": "Maine-et-Loire", "Manche": "Manche", "Marne": "<PERSON><PERSON>", "Martinique": "Martinique", "Mayenne": "<PERSON><PERSON>", "Mayotte": "Mayotte", "MeurtheEtMoselle": "Meurthe-et-Moselle", "Meuse": "<PERSON><PERSON>", "Morbihan": "<PERSON><PERSON><PERSON><PERSON>", "Moselle": "<PERSON><PERSON><PERSON>", "MetropoleDeLyon": "Métropole de Lyon", "Nievre": "<PERSON><PERSON><PERSON>", "Nord": "Nord", "Normandie": "<PERSON><PERSON>", "NouvelleAquitaine": "Nouvelle-Aquitaine", "Occitanie": "Occitanie", "Oise": "Oise", "Orne": "<PERSON><PERSON>", "Paris": "Paris", "PasDeCalais": "Pas-de-Calais", "PaysDeLaLoire": "Pays de la Loire", "ProvenceAlpesCoteDAzur": "Provence-Alpes-Côte d'Azur", "PuyDeDome": "Puy-de-Dôme", "PyrEnEesAtlantiques": "Pyrénées-Atlantiques", "PyrEnEesOrientales": "Pyrénées-Orientales", "Rhone": "Rhône", "SaintPierreEtMiquelon": "Saint-Pierre-et-Miquelon", "Sarthe": "Sarthe", "Savoie": "Savoie", "SaoneEtLoire": "Saône-et-Loire", "SeineMaritime": "Seine-Maritime", "SeineSaintDenis": "Seine-Saint-Denis", "SeineEtMarne": "Seine-et-Marne", "Somme": "Somme", "Tarn": "Tarn", "TarnEtGaronne": "Tarn-et-Garonne", "TerritoireDeBelfort": "<PERSON><PERSON><PERSON> Belfort", "ValDOise": "Val-d'Oise", "ValDeMarne": "Val-de-Marne", "Var": "Var", "Vaucluse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Vendee": "Vendée", "Vienne": "Vienne", "Vosges": "Vosges", "Yonne": "<PERSON><PERSON>", "Yvelines": "Yvelines", "IleDeFrance": "Île-de-France"}, "SK": {"BanskobystrickyKraj": "Banská Bystrica Region", "BratislavskyKraj": "Bratislava Region", "KosickyKraj": "Kosice region", "NitrianskyKraj": "Nitra region", "PresovskyKraj": "Presov region", "TrencianskyKraj": "Region of Trencin", "TrnavskyKraj": "Trnava region", "ZilinskyKraj": "Zilinsky region"}}, "address": {"shipping": {"address1": "Address", "address2": "Apartment, suite, etc.", "city": "City", "country": "Country/region", "firstName": "First name", "lastName": "Last name", "phone": "Phone", "province": "Region", "zip": "Postal code", "taxId": "Tax ID", "optional": "(optional)", "additional": "Additional Information"}, "AR": {"province": "Province"}, "AU": {"city": "Suburb", "province": "State/territory", "zip": "Postcode"}, "AT": {"address1": "Street and house number", "address2": "Additional address"}, "BR": {"address1": "Street and house number", "province": "State", "taxId": "CPF or CNPJ code", "cpf": "CPF (personal order)", "cnpj": "CNPJ (company order)"}, "CA": {"province": "Province"}, "CN": {"address1": "Full address", "province": "Province"}, "CL": {"taxId": "RUT"}, "CO": {"province": "Province"}, "DK": {"address1": "Street and house number"}, "EG": {"province": "Governorate"}, "DE": {"address1": "Street and house number", "address2": "Additional address"}, "HK": {"city": "District"}, "IN": {"province": "State", "zip": "PIN code"}, "ID": {"province": "Province"}, "IE": {"province": "County"}, "IT": {"address1": "Street and house number", "province": "Province"}, "JP": {"city": "City/ward/town/village", "province": "Prefecture"}, "MY": {"province": "State/territory", "zip": "Postcode"}, "MX": {"address1": "Street and house number", "province": "State"}, "NL": {"address1": "Street and house number", "province": "Province"}, "NG": {"province": "State"}, "NO": {"address1": "Street and house number"}, "RO": {"province": "County"}, "ZA": {"address2": "Suburb", "province": "Province"}, "KR": {"province": "Province"}, "ES": {"address1": "Street and house number", "province": "Province"}, "SE": {"address1": "Street and house number", "city": "City/town"}, "CH": {"address1": "Street and house number", "address2": "Additional address"}, "TH": {"province": "Province", "taxId": "Personal or VAT ID", "personal": "Personal ID number (personal order)", "vatId": "VAT ID number (company order)"}, "AE": {"province": "Emirate"}, "GB": {"zip": "Postcode"}, "US": {"province": "State", "zip": "ZIP code"}, "TW": {"city": "Area", "province": "City/County"}, "VN": {"province": "Province"}, "FR": {"province": "State/Province"}}, "currency": {"global": {"USD": "US Dollar", "EUR": "Euro", "GBP": "Pound Sterling", "CAD": "Canadian dollar", "AUD": "Australian Dollar", "CHF": "Swiss Franc", "HKD": "Hong Kong Dollar", "JPY": "Yen", "RUB": "Russian Ruble", "BRL": "Brazilian Real", "CLP": "Chilean Peso", "NOK": "Norwegian Krone", "DKK": "Danish Krone", "SEK": "Swedish Krona", "KRW": "South Korean Won", "ILS": "New Israeli Shekel", "MXN": "Mexican Peso", "CNY": "Yuan", "SAR": "Saudi Riyal", "SGD": "Singapore Dollar", "NZD": "New Zealand Dollar", "ARS": "Argentine Peso", "INR": "Indian Rupee", "COP": "Colombian Peso", "AED": "UAE Dirham", "AFN": "Afghani", "ALL": "Lek", "AMD": "Armenian Dram", "ANG": "Netherlands Antillean gulden", "AOA": "Kwan<PERSON>", "AWG": "Aruban Guilder/Florin", "AZN": "<PERSON><PERSON>", "BAM": "Konvertibilna Marka", "BBD": "Barbados Dollar", "BDT": "<PERSON><PERSON>", "BGN": "Bulgarian Lev", "BIF": "Burundi Franc", "BMD": "Bermudian Dollar", "BND": "Brunei Dollar", "BOB": "Boliviano", "BOV": "Bolivian M<PERSON>", "BSD": "Bahamian Dollar", "BTN": "Ngultrum", "BWP": "<PERSON><PERSON>", "BYN": "Belarusian Ruble", "BZD": "Belize Dollar", "CDF": "Congolese Franc", "CRC": "Costa Rican Colon", "CUC": "Peso Convertible", "CUP": "Cuban Peso", "CVE": "Cape Verde Escudo", "CZK": "Czech Koruna", "DJF": "Djibouti Franc", "DOP": "Dominican Peso", "DZD": "Algerian Dinar", "EGP": "Egyptian Pound", "ERN": "Nakfa", "ETB": "Ethiopian Birr", "FJD": "Fiji Dollar", "FKP": "Falkland Islands Pound", "GEL": "<PERSON><PERSON>", "GHS": "<PERSON><PERSON>", "GIP": "Gibraltar Pound", "GMD": "<PERSON><PERSON>", "GNF": "Guinea Franc", "GTQ": "Quetzal", "GYD": "Guyana Dollar", "HNL": "<PERSON><PERSON><PERSON>", "HRK": "Croatian Kuna", "HTG": "<PERSON><PERSON><PERSON>", "HUF": "Forint", "IDR": "<PERSON><PERSON><PERSON>", "IRR": "Iranian Rial", "ISK": "Iceland Krona", "JMD": "Jamaican Dollar", "KES": "Kenyan Shilling", "KGS": "Som", "KHR": "Riel", "KMF": "franc comorien", "KPW": "North Korean Won", "KYD": "Cayman Islands Dollar", "KZT": "Tenge", "LAK": "<PERSON><PERSON>", "LBP": "Lebanese Pound", "LKR": "Sri Lanka Rupee", "LRD": "Liberian Dollar", "LSL": "Loti", "MAD": "Moroccan <PERSON><PERSON><PERSON>", "MDL": "Moldovan Leu", "MKD": "<PERSON><PERSON>", "MMK": "Kyat", "MNT": "<PERSON><PERSON><PERSON>", "MOP": "Pataca", "MUR": "Mauritius Rupee", "MVR": "<PERSON><PERSON><PERSON><PERSON>", "MWK": "<PERSON><PERSON><PERSON>", "MYR": "Malaysian Ringgit", "MZN": "Metical", "NAD": "Namibia Dollar", "NGN": "<PERSON><PERSON>", "NIO": "Cordoba Oro", "NPR": "Nepalese Rupee", "PAB": "Balboa", "PEN": "Nuevo Sol", "PGK": "<PERSON><PERSON>", "PHP": "Philippine Peso", "PKR": "Pakistan Rupee", "PLN": "<PERSON><PERSON><PERSON><PERSON>", "PYG": "Guarani", "QAR": "<PERSON><PERSON> R<PERSON>", "RON": "<PERSON><PERSON>", "RSD": "Serbian Dinar", "RWF": "Rwanda Franc", "SBD": "Solomon Islands Dollar", "SCR": "Seychelles Rupee", "SDG": "Sudanese Pound", "SHP": "<PERSON>", "SLL": "Leone", "SOS": "Somali Shilling", "SRD": "Suriname Dollar", "SSP": "South Sudanese pound", "STN": "Dobra", "SYP": "Syrian Pound", "SZL": "<PERSON><PERSON><PERSON>", "THB": "Baht", "TJS": "So<PERSON><PERSON>", "TMT": "Manat", "TOP": "Pa’anga", "TRY": "Turkish Lira", "TTD": "Trinidad and Tobago Dollar", "TWD": "Taiwan Dollar", "TZS": "Tanzanian <PERSON>", "UAH": "Hryvnia", "UGX": "Uganda Shilling", "UYU": "Peso Uruguayo", "UZS": "Uzbekistan Sum", "VES": "Bo<PERSON><PERSON><PERSON>zo<PERSON>", "VND": "<PERSON>", "VUV": "Vatu", "WST": "<PERSON><PERSON>", "XAF": "CFA Franc BCEAO", "XCD": "East Caribbean Dollar", "XOF": "CFA franc BCEAO", "XPF": "CFP Franc", "YER": "Yemeni R<PERSON>", "ZAR": "Rand", "ZMW": "Zambian <PERSON>", "KWD": "<PERSON><PERSON>", "OMR": "Oman Riyal", "BHD": "Bahrain Dinar", "JEP": "Jersey Pound", "JOD": "<PERSON><PERSON>", "LVL": "Latvia lat", "MGA": "<PERSON><PERSON>", "STD": "Dobra", "TND": "Tunisian Dina", "VEF": "<PERSON><PERSON><PERSON><PERSON>"}}, "orders": {"paymentStatus": {"unpaid": "Unpaid", "pending": "Pending", "partially_paid": "Partial payment", "paid": "Already paid", "partially_refunded": "Partial refund", "refunded": "Refunded", "voided": "Abolished", "confirm": "To be confirmed"}, "logs": {"cancelPay": "Cancel Payment"}}, "app": {"form_tool": {"success_content": "Submitted successfully! We'll get back to you shortly."}}}