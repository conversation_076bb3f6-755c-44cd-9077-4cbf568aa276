using Microsoft.Extensions.Caching.Distributed;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common.Cache;


/// <summary>
/// 缓存抽象接口,基于IDistributedCache封装
/// </summary>
public interface ICaching
{
   
    void DelByPattern(string key);
    Task DelByPatternAsync(string key);
    bool Exists(string cacheKey); 
    Task<bool> ExistsAsync(string cacheKey);
 
    T Get<T>(string key);
  

    Task<T> GetAsync<T>(string cacheKey);
    Task<TData> GetFromCacheAsync<TData>(string key, Func<Task<TData>> sourceGetter) where TData : class  ;
    Task<TData> GetFromCacheAsync<TData>(string key, string field, Func<Task<TData>> sourceGetter) where TData : class;
    string GetString(string key);
    Task<string> GetStringAsync(string cacheKey);
    bool HashDel(string key, List<string> hashkey);
    Dictionary<string, string> HashGet(string key);
    string HashGet(string key, string hashkey);
    Dictionary<string, T> HashGet<T>(string key);
    T HashGet<T>(string key, string hashkey);
    List<T> HashGetMore<T>(string key, List<string> hashkey);
    bool HashRemove(string key, string hashkey);
    bool HashSet(string key, string hashkey, string hashvalue);
    bool HashSet<T>(string key, string hashkey, T hashObj);
    bool HashTryGet(string key, out Dictionary<string, string> data);
    List<string> HashGetKeys(string key);
 
    void Set<T>(string cacheKey, T value);
    void Set<T>(string cacheKey, T value, TimeSpan expire);
    Task SetAsync<T>(string cacheKey, T value);
    Task SetAsync<T>(string cacheKey, T value, TimeSpan expire);
    Task SetStringAsync(string cacheKey, string value);
    Task SetStringAsync(string cacheKey, string value, TimeSpan expire);
    bool TryGet<T>(string key, out T value);


    bool EqueuePush<T>(string key, T val);
    bool EqueuePush<T>(string key, IList<T> valueList);
    T EqueuePop<T>(string key, out bool status);
    IList<T> EqueuePop<T>(string key, int length);
}
