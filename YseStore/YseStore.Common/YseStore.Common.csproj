<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="c4nud5x0.vvh~" />
  </ItemGroup>
	<ItemGroup>

		<PackageReference Include="FreeRedis" Version="1.3.6" />

		<PackageReference Include="JsonPath.Net" Version="2.1.1" />
		<PackageReference Include="Magicodes.IE.Excel" Version="2.7.5.2" />
		<PackageReference Include="InitQ" Version="1.0.0.18" />
		<PackageReference Include="log4net" Version="2.0.15" />
		<PackageReference Include="Mapster" Version="7.4.0" />
		<PackageReference Include="Mapster.Core" Version="1.2.1" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.WebSockets" Version="2.3.0" />
		<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
		<PackageReference Include="PinYinConverterCore" Version="1.0.2" />
		<PackageReference Include="MiniProfiler.Shared" Version="4.3.8" />
		<PackageReference Include="RestSharp" Version="112.1.0" />
		<PackageReference Include="RSAExtensions" Version="1.1.1" />
		<PackageReference Include="Serilog.Expressions" Version="4.0.0" />
		<PackageReference Include="Serilog.Sinks.Async" Version="1.5.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="5.0.0" />
		<PackageReference Include="Serilog.Sinks.Elasticsearch" Version="9.0.3" />
		<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
		<PackageReference Include="SkiaSharp" Version="3.119.0" />
		<PackageReference Include="SnowflakeId.AutoRegister" Version="1.0.4" />
		<PackageReference Include="SnowflakeId.AutoRegister.SqlServer" Version="1.0.5" />
		<PackageReference Include="SnowflakeId.AutoRegister.StackExchangeRedis" Version="1.0.2" />
		<PackageReference Include="StackExchange.Redis" Version="2.8.31" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.2.0" />
		<PackageReference Include="Serilog.Sinks.RollingFile" Version="3.3.1-dev-00771" />

		<PackageReference Include="Serilog" Version="3.1.1" />
		<PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
		<PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.6" />
		<PackageReference Include="Yitter.IdGenerator" Version="1.0.14" />
		<PackageReference Include="ZString" Version="2.6.0" />
		<PackageReference Include="ZXing.Net" Version="0.16.10" />
		<PackageReference Include="Fluid.MvcViewEngine" Version="2.24.0" />
	
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\YseStore.Model\YseStore.Model.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="Abstraction\" />
	</ItemGroup>
</Project>
