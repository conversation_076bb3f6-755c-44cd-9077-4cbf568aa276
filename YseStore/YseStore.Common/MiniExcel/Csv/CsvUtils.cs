using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common.MiniExcel.Csv
{
    /// <summary>
    /// CSV文件处理工具类
    /// </summary>
    public static class CsvUtils
    {
        /// <summary>
        /// 预处理CSV文件，处理单元格内的换行符问题
        /// </summary>
        /// <param name="filePath">原始CSV文件路径</param>
        /// <returns>处理后的CSV文件路径</returns>
        public static async Task<string> PreprocessCsvFile(string filePath)
        {
            // 读取原始CSV文件内容
            string fileContent = await File.ReadAllTextAsync(filePath);

            // 检测文件编码
            Encoding encoding = DetectEncoding(filePath);
            if (encoding != Encoding.UTF8)
            {
                // 如果不是UTF8编码，需要转换
                byte[] fileBytes = await File.ReadAllBytesAsync(filePath);
                fileContent = encoding.GetString(fileBytes);
            }

            // 处理CSV引号内的换行符
            string processedContent = ProcessCsvQuotesAndNewlines(fileContent);

            // 如果文件内容有变化，写入临时文件
            if (processedContent != fileContent)
            {
                string tempFilePath = Path.Combine(Path.GetDirectoryName(filePath), 
                    $"temp_{Path.GetFileNameWithoutExtension(filePath)}_{Guid.NewGuid():N}.csv");
                
                await File.WriteAllTextAsync(tempFilePath, processedContent, Encoding.UTF8);
                return tempFilePath;
            }

            // 如果没有变化，返回原始文件路径
            return filePath;
        }

        /// <summary>
        /// 处理CSV中引号内的换行符
        /// </summary>
        /// <param name="csvContent">CSV内容</param>
        /// <returns>处理后的CSV内容</returns>
        public static string ProcessCsvQuotesAndNewlines(string csvContent)
        {
            if (string.IsNullOrEmpty(csvContent))
                return csvContent;

            var result = new StringBuilder();
            bool insideQuotes = false;
            
            // 识别CSV中的换行符模式（\r\n, \n, or \r）
            string newlinePattern = GetNewlinePattern(csvContent);
            
            for (int i = 0; i < csvContent.Length; i++)
            {
                char current = csvContent[i];
                
                // 处理引号
                if (current == '"')
                {
                    // 检查是否为转义的引号（即连续两个引号）
                    if (i + 1 < csvContent.Length && csvContent[i + 1] == '"')
                    {
                        result.Append("\"\"");  // 保留转义的引号
                        i++;  // 跳过下一个引号
                    }
                    else
                    {
                        insideQuotes = !insideQuotes;  // 切换引号状态
                        result.Append('"');
                    }
                }
                // 处理引号内的换行符
                else if (insideQuotes && (current == '\r' || current == '\n'))
                {
                    // 在引号内，将换行符替换为空格
                    if (current == '\r' && i + 1 < csvContent.Length && csvContent[i + 1] == '\n')
                    {
                        result.Append(' ');  // 用空格替换\r\n
                        i++;  // 跳过\n
                    }
                    else
                    {
                        result.Append(' ');  // 用空格替换单个\r或\n
                    }
                }
                // 保留其他字符
                else
                {
                    result.Append(current);
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// 获取CSV内容中使用的换行符模式
        /// </summary>
        private static string GetNewlinePattern(string content)
        {
            if (content.Contains("\r\n"))
                return "\r\n";
            else if (content.Contains('\n'))
                return "\n";
            else if (content.Contains('\r'))
                return "\r";
            
            return Environment.NewLine;  // 默认使用系统换行符
        }

        /// <summary>
        /// 检测文件编码
        /// </summary>
        private static Encoding DetectEncoding(string filePath)
        {
            // 简单的编码检测
            using var reader = new StreamReader(filePath, Encoding.Default, true);
            reader.Peek();  // 触发编码检测
            return reader.CurrentEncoding;
        }
    }
} 