using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace YseStore.Common.Helper
{
    public class DynamicDictionaryConverter : JsonConverter<Dictionary<string, Dictionary<string, decimal>>>
    {
        public override Dictionary<string, Dictionary<string, decimal>> Read(
            ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var result = new Dictionary<string, Dictionary<string, decimal>>();

            if (reader.TokenType != JsonTokenType.StartObject)
                return result;

            while (reader.Read() && reader.TokenType != JsonTokenType.EndObject)
            {
                var outerKey = reader.GetString();
                reader.Read(); // 移动到内层对象

                var innerDict = new Dictionary<string, decimal>();
                while (reader.Read() && reader.TokenType != JsonTokenType.EndObject)
                {
                    var innerKey = reader.GetString();
                    reader.Read(); // 移动到值

                    decimal value = 0;
                    if (reader.TokenType == JsonTokenType.Number)
                    {
                        value = reader.GetDecimal();
                    }
                    else if (reader.TokenType == JsonTokenType.String)
                    {
                        decimal.TryParse(reader.GetString(), out value);
                    }

                    innerDict[innerKey] = value;
                }

                result[outerKey] = innerDict;
            }

            return result;
        }

        public override void Write(Utf8JsonWriter writer, Dictionary<string, Dictionary<string, decimal>> value, JsonSerializerOptions options)
        {
            writer.WriteStartObject();
            foreach (var outer in value)
            {
                writer.WritePropertyName(outer.Key);
                writer.WriteStartObject();
                foreach (var inner in outer.Value)
                {
                    writer.WritePropertyName(inner.Key);
                    writer.WriteNumberValue(inner.Value);
                }
                writer.WriteEndObject();
            }
            writer.WriteEndObject();
        }
    }
}
