using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common;
public class UserExportMenuHelper
{
    public static readonly Dictionary<string, string> AllMenuItems = new()
        {
            { "A", "0" }, { "B", "0" }, { "C", "0" }, { "D", "0" },
            { "E", "0" }, { "F", "0" }, { "G", "0" }, 
        //{ "H", "0" },
            { "I", "0" }, { "J", "0" }, 
        //{ "K", "0" }, 
        { "L", "0" },
            { "M", "0" }, { "N", "0" }, { "O", "0" }, { "P", "0" },
            { "Q", "0" }, { "R", "0" }, { "S", "0" }, { "T", "0" },
            { "U", "0" }, { "V", "0" }, { "W", "0" }, { "X", "0" },
            { "Y", "0" }, { "Z", "0" }
        };
    public static readonly Dictionary<string, string> AllMenuItemsText = new()
        {
                { "A", "姓名" },
                { "B", "邮箱" },
                { "M", "订阅状态" },
                { "C", "累计金额" },
                { "D", "购买次数" },
                { "E", "注册时间" },
                { "F", "注册IP" },

                { "G", "收货姓名" },
                { "N", "收货街道" },
                { "O", "收货寓所" },

                { "P", "收货城市" },
                { "Q", "收货省份" },
                { "R", "收货邮编" },
                { "S", "收货国家" },
                { "I", "收货电话" },

                { "J", "账单姓名" },
                { "T", "账单街道" },
                { "U", "账单寓所" },
                { "V", "账单城市" },
                { "W", "账单省份" },

                { "X", "账单邮编" },
                { "Y", "账单国家" },
                { "L", "账单电话" },

                { "Z", "Nickname" }
        };
    public static readonly Dictionary<string, string[]> FieldRules = new()
    {
        { "A", new[] { "FirstName", "LastName" } },
        { "B", new[] { "Email" } },
        { "M", new[] { "IsNewsletter" } },
        { "C", new[] { "OrderSymbol", "OrderSum" } },
        { "D", new[] { "ConsumptionTime" } },
        { "E", new[] { "RegTime" } },
        { "F", new[] { "RegIp" } },
        { "G", new[] { "AddresFirstName", "AddresLastName" } },
        { "N", new[] { "AddressLine1" } },
        { "O", new[] { "AddressLine2" } },
        { "P", new[] { "City" } },
        { "Q", new[] { "State" } },
        { "R", new[] { "ZipCode" } },
        { "S", new[] { "CountryName" } },
        { "I", new[] { "CountryCode", "PhoneNumber" } },
        { "J", new[] { "BillingFirstName", "BillingLastName" } },
        { "T", new[] { "BillingAddressLine1" } },
        { "U", new[] { "BillingAddressLine2" } },
        { "V", new[] { "BillingCity" } },
        { "W", new[] { "BillingState" } },
        { "X", new[] { "BillingZipCode" } },
        { "Y", new[] { "BillingCountryName" } },
        { "L", new[] { "BillingCountryCode", "BillingPhoneNumber" } },
        { "Z", new[] { "NickName" } }
    };


    //public static readonly Dictionary<string, string> AllMenuItemsField = new()
    //    {
    //            { "A", "FirstName" },
    //            { "B", "Email" },
    //            { "M", "IsNewsletter" },
    //            { "C", "累计金额" },
    //            { "D", "购买次数" },
    //            { "E", "注册时间" },
    //            { "F", "注册IP" },
    //            { "G", "收货姓名" },
    //            { "N", "收货街道" },
    //            { "O", "收货寓所" },

    //            { "P", "收货城市" },
    //            { "Q", "收货省份" },
    //            { "R", "收货邮编" },
    //            { "S", "收货国家" },
    //            { "I", "收货电话" },
    //            { "J", "账单姓名" },
    //            { "T", "账单街道" },
    //            { "U", "账单寓所" },
    //            { "V", "账单城市" },
    //            { "W", "账单省份" },

    //            { "X", "账单邮编" },
    //            { "Y", "账单国家" },
    //            { "L", "账单电话" },
    //            { "Z", "Nickname" }
    //    };
}

