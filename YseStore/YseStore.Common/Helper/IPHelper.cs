using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common;
public static class IpAddressHelper
{
    // 常见局域网 IP 范围
    private static readonly IPAddress[] LanRanges =
    {
        IPAddress.Parse("10.0.0.0"),
        IPAddress.Parse("**********"),
        IPAddress.Parse("***********"),
        IPAddress.Parse("*********")
    };

    public static bool IsLocalNetwork(IPAddress ipAddress)
    {
        // 检查 IPv4 局域网范围
        if (ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
        {
            byte[] ipBytes = ipAddress.GetAddressBytes();

            foreach (var range in LanRanges)
            {
                byte[] rangeBytes = range.GetAddressBytes();

                // 10.x.x.x
                if (ipBytes[0] == 10) return true;

                // 172.16.x.x - 172.31.x.x
                if (ipBytes[0] == 172 && ipBytes[1] >= 16 && ipBytes[1] <= 31) return true;

                // 192.168.x.x
                if (ipBytes[0] == 192 && ipBytes[1] == 168) return true;

                // 127.x.x.x
                if (ipBytes[0] == 127) return true;
            }
        }

        // 检查 IPv6 本地地址 (::1)
        if (ipAddress.Equals(IPAddress.IPv6Loopback)) return true;

        return false;
    }
}

