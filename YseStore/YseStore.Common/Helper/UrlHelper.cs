using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;

namespace YseStore.Common.Helper
{



    public static class UrlHelper
    {
        /// <summary>
        /// 根据数据记录和类型获取网站的对应的Url
        /// </summary>
        /// <param name="row">数据记录</param>
        /// <param name="type">类型</param>
        /// <param name="lang">语言</param>
        /// <param name="queryString">地址栏参数（需要拼接的参数都要传【因为需要进行转义】）</param>
        /// <returns>对应的Url</returns>
        public static string GetUrl(Dictionary<string, object> row, string type = "products_category", string lang = "", string queryString = "")
        {
            // 生成页面地址
            //var c = Config.Params; // Assuming you have a Config class with Params property
            //if (string.IsNullOrEmpty(lang))
            //{
            //    lang = (c.ContainsKey("lang") ? c["lang"]?.ToString() : (c.ContainsKey("manage") && ((Dictionary<string, object>)c["manage"]).ContainsKey("web_lang")
            //           ? ((Dictionary<string, object>)c["manage"])["web_lang"]?.ToString() : "_en")) ?? "_en";
            //}

            string[] ary = type.Split('_');
            int length = ary.Length;
            string pageUrl = row.ContainsKey("PageUrl") ? row["PageUrl"]?.ToString() : "";

            string url;

            if (ary[0] == "article" && length == 1)
            {
                string path = StrToUrl(row.ContainsKey("Title_en") ? row["Title_en"]?.ToString() : "");
                path = !string.IsNullOrEmpty(pageUrl) ? pageUrl : path + "-" + row["AId"];
                url = "/pages/" + path;
                url = row.ContainsKey("Url") && !string.IsNullOrEmpty(row["Url"]?.ToString()) ? row["Url"]?.ToString() : url;
            }
            else if (ary[0] == "help" && length == 1)
            {
                string path = StrToUrl(row.ContainsKey("Title_en") ? row["Title_en"]?.ToString() : "");
                url = "/help/" + path + "-h" + string.Format("{0:D4}", row["AId"]) + ".html";
                url = !string.IsNullOrEmpty(pageUrl) ? "/help/" + pageUrl + ".html" : url;
                url = row.ContainsKey("Url") && !string.IsNullOrEmpty(row["Url"]?.ToString()) ? row["Url"]?.ToString() : url;
            }
            else if (ary[0] == "info" && length == 1)
            {
                string path = StrToUrl(row.ContainsKey("Title_en") ? row["Title_en"]?.ToString() : "");
                path = !string.IsNullOrEmpty(pageUrl) ? pageUrl : path;
                url = "/info/" + path + "-i" + string.Format("{0:D4}", row["InfoId"]) + ".html";
                url = row.ContainsKey("Url") && !string.IsNullOrEmpty(row["Url"]?.ToString()) ? row["Url"]?.ToString() : url;
            }
            else if (ary[0] == "info" && ary[1] == "category")
            {
                string path = StrToUrl(row.ContainsKey("Category_en") ? row["Category_en"]?.ToString() : "");
                url = "/info/" + path + "-c" + string.Format("{0:D4}", row["CateId"]);
            }
            else if (ary[0] == "products" && length == 1)
            {
                // 产品详细页
                string path = StrToUrl(row.ContainsKey("Name_en") ? row["Name_en"]?.ToString() : "");
                path = !string.IsNullOrEmpty(pageUrl) ? pageUrl : path;
                url = "/products/" + path;
            }
            else if (ary[0] == "products" && ary[1] == "category")
            {
                // 产品分类列表页
                string path = StrToUrl(row.ContainsKey("Category_en") ? row["Category_en"]?.ToString() : "");
                path = !string.IsNullOrEmpty(pageUrl) ? pageUrl : path + "-" + row["CateId"];
                url = "/collections/" + path;
            }
            else if (ary[0] == "review")
            {
                url = "/review_p" + string.Format("{0:D4}", row["ProId"]) + "/";
            }
            else if (ary[0] == "write" && ary[1] == "review")
            {
                url = "/review-write/" + string.Format("{0:D4}", row["ProId"]) + ".html";
            }
            else if (ary[0] == "blog" && length == 1)
            {
                string path = StrToUrl(row.ContainsKey("Title_en") ? row["Title_en"]?.ToString() : "");
                path = path + "-" + row["AId"];
                url = "/blog/" + path;
            }
            else if (ary[0] == "blog" && ary[1] == "category")
            {
                string path = StrToUrl(row.ContainsKey("Category_en") ? row["Category_en"]?.ToString() : "");
                path = path + "-" + row["CateId"];
                url = "/blogs/" + path;
            }
            else if (ary[0] == "blog" && ary[1] == "date")
            {
                url = "/blogs/t/" + row.ToString();
            }
            else if (ary[0] == "policies" && length == 1)
            {
                string path = StrToUrl(row.ContainsKey("Name") ? row["Name"]?.ToString() : "");
                path = !string.IsNullOrEmpty(pageUrl) ? pageUrl : path;
                url = "/policies/" + path;
            }
            else if (ary[0] == "blog" && ary[1] == "new" && length == 2)
            {
                string path = StrToUrl(pageUrl);
                url = "/blog/detail/" + path;
            }
            else if (ary[0] == "blog" && ary[1] == "new" && ary[2] == "category")
            {
                string path = StrToUrl(pageUrl);
                url = "/blog/collections/" + path;
            }
            else if (ary[0] == "cases" && length == 1)
            {
                // 案例详细页
                string path = StrToUrl(row.ContainsKey("Title_en") ? row["Title_en"]?.ToString() : "");
                path = !string.IsNullOrEmpty(pageUrl) ? pageUrl : path;
                url = "/cases-detail/" + path;
            }
            else if (ary[0] == "cases" && ary[1] == "category")
            {
                // 案例分类列表页
                string path = StrToUrl(row.ContainsKey("Category_en") ? row["Category_en"]?.ToString() : "");
                path = !string.IsNullOrEmpty(pageUrl) ? pageUrl : path + "-" + row["CateId"];
                url = "/cases/" + path;
            }
            else if (ary[0] == "news" && length == 1)
            {
                string path = StrToUrl(row.ContainsKey("Title_en") ? row["Title_en"]?.ToString() : "");
                path = !string.IsNullOrEmpty(pageUrl) ? pageUrl : path + "-" + row["NewsId"];
                url = "/info-detail/" + path;
            }
            else if (ary[0] == "news" && ary[1] == "category")
            {
                string path = StrToUrl(row.ContainsKey("Category_en") ? row["Category_en"]?.ToString() : "");
                path = !string.IsNullOrEmpty(pageUrl) ? pageUrl : path + "-" + row["CateId"];
                url = "/info/" + path;
            }
            else
            {
                url = "javascript:;";
            }

            // 拼接地址的参数需要urlencode
            if (url != "javascript:;" && !string.IsNullOrEmpty(queryString))
            {
                url = url + "?&" + HttpUtility.UrlEncode(queryString);
            }

            return url;
        }

        /// <summary>
        /// 字符串转换成合法的url路径
        /// </summary>
        /// <param name="str">原字符串</param>
        /// <returns>合法Url字符串</returns>
        public static string StrToUrl(string str)
        {
            if (string.IsNullOrEmpty(str))
                return string.Empty;

            // Convert to lowercase and trim
            string url = str.ToLower().Trim();

            // Replace spaces and forward slashes with hyphens
            url = url.Replace(" ", "-").Replace("/", "-");

            // Remove special characters
            char[] charsToRemove = new char[] {
            '%', '`', '~', '!', '@', '#', '$',
            '^', '&', '*', '(', ')', '_', '=',
            '+', '[', '{', ']', '}', ';', ':',
            '\'', '"', '\\', '|', '<', ',',
            '.', '>', '?', '\r', '\n', '\t'
        };

            foreach (char c in charsToRemove)
            {
                url = url.Replace(c.ToString(), "");
            }

            // Replace multiple consecutive hyphens with single hyphen
            url = Regex.Replace(url, "-{2,}", "-");

            return url;
        }


    }

}
