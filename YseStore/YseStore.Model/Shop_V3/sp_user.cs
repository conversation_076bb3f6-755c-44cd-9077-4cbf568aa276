using System;
using System.ComponentModel.DataAnnotations;


namespace Entitys.Shop_V3
{
    /// <summary>
    /// sp_user
    /// </summary>
    public partial class sp_user
    {
        /// <summary>
        /// sp_user
        /// </summary>
        public sp_user()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string UserName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string NickName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? CreateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Phone { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Photo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PhotoThumb { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? Gender { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Email { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Password { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? PayCredits { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? RankCredits { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? VerifyEmail { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Salt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string RegIp { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string LastVisitIp { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string FirstName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string LastName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int64? SysUserID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? LastVisitTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string CountryCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? Birthday { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? OnlineState { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string State { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? CustomerGroupID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string CustomerGroup { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ZipCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string JobTitle { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Company { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Website { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string City { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Note { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string CountryName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? BuyCount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? LockedStatus { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? IsValidateEmail { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string TaxNumber { get; set; }

        /// <summary>
        /// 0：默认MD5加密,1:SHA256加密 2：MD5加密
        /// </summary>
        public System.Int16? PasswordFormat { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string MiddleName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string StreetAddress { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Fax { get; set; }


        /// <summary>
        /// 默认语言
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Lang { get; set; }

        /// <summary>
        /// 默认币种
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string UserCurrency { get; set; }


        /// <summary>
        /// 0:默认网站注册，1：Google授权，2：领英授权，3：Facebook授权
        /// </summary>
        public System.Int16? AccountType { get; set; }

        /// <summary>
        /// 第三方授权软件id
        /// </summary>
        public string SoftwareAccountID { get; set; }


        /// <summary>
        /// 积分
        /// </summary>
        public System.Int32? Integral { get; set; }


        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string Label { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string UserLabel { get; set; }

        /// <summary>
        /// 国家区域
        /// </summary>
        public System.Int16? CountryRegion { get; set; }


        /// <summary>
        /// 注册链接
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string ReturnUrl { get; set; }

        ///// <summary>
        ///// 发送通知邮件次数
        ///// </summary>
        //public System.Int32? EmailSentCount { get; set; }


        /// <summary>
        /// 国家货币分区
        /// </summary>
        public System.Int16? CountryCurrency { get; set; }
    }



    public class ReturnUrlModel
    {
        public string type { get; set; }
        public string url { get; set; }
        public long id { get; set; }
    }

}