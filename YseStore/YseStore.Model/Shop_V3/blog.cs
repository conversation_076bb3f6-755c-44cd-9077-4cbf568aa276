using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.Shop_V3
{
    /// <summary>
    /// blog
    /// </summary>
    public partial class blog
    {
        /// <summary>
        /// blog
        /// </summary>
        public blog()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Subject { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Content { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Thumbnail { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string Tag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string CreateUser { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? CreateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ModifyUser { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? Type { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string LinkTitle { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? CateId { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string CateName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Summary { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Author { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string OriImageName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ImageURL { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsAllowComment { get; set; }

        /// <summary>
        /// 是否置顶
        /// </summary>
        public System.Int32? IsTop { get; set; }

        /// <summary>
        /// 是否发布
        /// </summary>
        public System.Boolean? IsPublish { get; set; }

        /// <summary>
        /// 是否审批
        /// </summary>
        public System.Boolean? IsChecked { get; set; }

        /// <summary>
        /// 是否放回收站
        /// </summary>
        public System.Boolean? IsDelete { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string CateLinkName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? VisitCount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? CommentCount { get; set; }

        /// <summary>
        /// 发布方式，0：后台发布，1：前台发布
        /// </summary>
        public System.Int16? PublishMode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? UserID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string UserName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string UserEmail { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string UserImage { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Approver { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? ApproverTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? ApproverID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string SEOKeyword { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string SEODescription { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public System.DateTime? PublishTime { get; set; }

        /// <summary>
        /// 是否放回收站
        /// </summary>
        public System.Boolean? IsExclusive { get; set; }


        /// <summary>
        /// 纯文字内容
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ContentNoHtml { get; set; }


        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string CateJson { get; set; }



        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string BlogItemId { get; set; }
    }
}
