using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.Shop_V3
{
    /// <summary>
    /// sp_orders
    /// </summary>
    public partial class sp_orders
    {
        /// <summary>
        /// sp_orders
        /// </summary>
        public sp_orders()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string OrderSN { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string BuyerName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? Uid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string BuyerEmail { get; set; }

        /// <summary>
        /// 0：买家未付款，1：买家已付款，2：卖家正在准备发货，3：卖家已发货，4：已妥投，5：订单已取消，6：已过期
        /// </summary>
        public System.Int16? OrderStatus { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? ProductAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? OrderAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? IsReview { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? CreateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ShipSN { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ShippingServiceName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? ShipTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PayMethodCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PaySN { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PayMethodName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? PayTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string CountryName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string CountryCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Province { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string City { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Address1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Address2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Consignee { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Mobile { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Phone { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ZipCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? ShipFee { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? Discount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? Weight { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string BuyerRemark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string IP { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string ProductList { get; set; }

        /// <summary>
        /// 支付状态 0:未支付，1：已支付，2:paypal支付中
        /// </summary>
        public System.Int16? PaymentStatus { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? LogisticStatus { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string TrackNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string UUID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsLocked { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ShipWebUrl { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? CancelTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? RefundTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? ReturnTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? RefundAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? Flag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ShipCarrierCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ShipCarrier { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string NewShipCarrier { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string OrderCurrency { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? SettlementAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string SettlementCurrency { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string GiftMsgFrom { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string GiftMsgTo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string GiftMessage { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string DiscountMsg { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string CouponCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? PreferentialAmount { get; set; }

        /// <summary>
        /// 收货人公司
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ConsigneeCompany { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ConsigneeFax { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ConsigneePrefix { get; set; }

        /// <summary>
        /// 最近发送通知邮件时间
        /// </summary>
        public System.DateTime? LastEmailSentTime { get; set; }

        /// <summary>
        /// 发送通知邮件次数
        /// </summary>
        public System.Int32? EmailSentCount { get; set; }

        /// <summary>
        /// 订单或付款时间（用于排序）
        /// </summary>
        public System.DateTime? OrderOrPayTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public System.DateTime? UpdateTime { get; set; }


        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PaypalFee { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PaypalFeeCurrency { get; set; }



        /// <summary>
        /// 0:等待同步，1：已同步，2：失败
        /// </summary>
        public System.Int16? SyncRedisStatus { get; set; }

        /// <summary>
        /// paypal订单状态
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PaypalState { get; set; }

        /// <summary>
        /// payer的支付方式
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PayerPaymentMethod { get; set; }

        /// <summary>
        /// paypal的支付方式
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PaypalPaymentMode { get; set; }

        /// <summary>
        /// paypal支付方式的支付状态
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PaypalPaymentModeState { get; set; }

        /// <summary>
        /// 订单评论状态
        /// </summary>
        public System.Int16? OrderCommentStatus { get; set; }


        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string ToPayInfo { get; set; }


        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ShipServiceNameCN { get; set; }



        /// <summary>
        /// 国家区域
        /// </summary>
        public System.Int16? CountryRegion { get; set; }



        /// <summary>
        /// 税号
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Tax { get; set; }
    }
}
