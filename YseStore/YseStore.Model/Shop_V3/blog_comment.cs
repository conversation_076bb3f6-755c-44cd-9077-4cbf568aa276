using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.Shop_V3
{
    /// <summary>
    /// blog_comment
    /// </summary>
    public partial class blog_comment
    {
        /// <summary>
        /// blog_comment
        /// </summary>
        public blog_comment()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Content { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string CreateUser { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? CreateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsReplied { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? ArticleID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? ParentComID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? TopComID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Email { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string WebSite { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsApprove { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsAuthorReply { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string IP { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? Uid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PreUserName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PreUserEmail { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? PreUserID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ArticleLinkTitle { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ArticleSubject { get; set; }
    }
}
