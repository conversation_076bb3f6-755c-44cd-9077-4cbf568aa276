using Entitys;


namespace YseStore.Model.Utils
{
    /// <summary>
    /// 分类树构建工具类
    /// </summary>
    public static class CategoryTreeBuilder
    {
        /// <summary>
        /// 通用分类树构建方法，支持任意层级的分类结构
        /// </summary>
        /// <param name="categories">分类列表</param>
        /// <returns>构建好的分类树</returns>
        public static Dictionary<int, List<products_category>> BuildCategoryTreeGeneric(List<products_category> categories)
        {
            var result = new Dictionary<int, List<products_category>>();
            
            if (categories == null || !categories.Any())
            {
                return result;
            }
            
            try
            {
                // 添加根节点（一级分类）并按MyOrder排序
                var rootCategories = categories
                    .Where(c => string.IsNullOrEmpty(c.UId) || c.UId == "0," || c.UId == "0")
                    .OrderBy(c => c.MyOrder)
                    .ToList();

                if (rootCategories.Any())
                {
                    result.Add(0, rootCategories);
                }
                
                // 构建分类ID到分类对象的映射，加速查找
                var categoryMap = categories.ToDictionary(c => c.CateId, c => c);
                
                // 创建父子关系映射
                foreach (var category in categories)
                {
                    // 跳过无效分类
                    if (category == null || category.CateId <= 0 || string.IsNullOrEmpty(category.UId))
                    {
                        continue;
                    }
                    
                    // 解析UId，查找直接父分类
                    string[] uidParts = category.UId.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    if (uidParts.Length > 0)
                    {
                        // 获取最后一个父分类ID
                        if (int.TryParse(uidParts[uidParts.Length - 1], out int parentId) && parentId > 0)
                        {
                            // 将当前分类添加到父分类的子分类列表中
                            if (!result.ContainsKey(parentId))
                            {
                                result[parentId] = new List<products_category>();
                            }
                            
                            result[parentId].Add(category);
                            
                            // 更新父分类的子分类计数
                            if (categoryMap.ContainsKey(parentId))
                            {
                                categoryMap[parentId].SubCateCount = result[parentId].Count;
                            }
                        }
                    }
                }
                
                // 对所有子分类列表按MyOrder排序
                foreach (var kvp in result.ToList())
                {
                    if (kvp.Value != null && kvp.Value.Count > 1)
                    {
                        // 按MyOrder排序子分类列表
                        result[kvp.Key] = kvp.Value.OrderBy(c => c.MyOrder).ToList();
                    }
                }

                // 计算所有分类的子分类数量（包括间接子分类）
                foreach (var category in categories)
                {
                    if (category == null || category.CateId <= 0)
                    {
                        continue;
                    }

                    // 如果有直接子分类，更新子分类计数
                    if (result.ContainsKey(category.CateId))
                    {
                        category.SubCateCount = CountAllDescendants(category.CateId, result);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不中断执行
                Console.WriteLine($"构建分类树时出错: {ex.Message}");
            }
            
            return result;
        }

        /// <summary>
        /// 递归计算所有后代分类数量
        /// </summary>
        private static int CountAllDescendants(int categoryId, Dictionary<int, List<products_category>> categoryTree)
        {
            if (!categoryTree.ContainsKey(categoryId))
            {
                return 0;
            }
            
            int count = categoryTree[categoryId].Count;
            
            // 递归计算子分类的后代
            foreach (var child in categoryTree[categoryId])
            {
                count += CountAllDescendants(child.CateId, categoryTree);
            }
            
            return count;
        }
    }
} 