using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.VM.Country
{

    public class VM_Country
    {
        public string name { get; set; }
        public string code { get; set; }
        public string tax { get; set; }
        public decimal threshold { get; set; }
        public int phone_code { get; set; }
        public sbyte address_format { get; set; }
        public Dictionary<string, string> labels { get; set; }
        public Dictionary<string, Province> provinces { get; set; }
        public Dictionary<string, object> field { get; set; }
    }

    public class VM_CountryData
    {
        public string? en { get; set; }
        [System.Text.Json.Serialization.JsonPropertyName("zh-cn")]
        public string? zh_cn { get; set; }
    }

    public class Province
    {
        public string code { get; set; }
        public string name { get; set; }
        public string tax { get; set; }
        public int SId { get; set; }
    }


}
