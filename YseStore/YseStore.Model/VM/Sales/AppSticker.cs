using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model
{
    public class StickerData
    {
        public string Name { get; set; }
        public string Position { get; set; }
        public string Products { get; set; } // 逗号分隔的产品ID
        public string Html { get; set; }
        public Dictionary<int, string> DiscountHtml { get; set; } // 折扣商品的HTML
        public int Id { get; set; }
        public string BgImgPath { get; set; }=string.Empty;
    }

    public class ContentData
    {
        public string Style { get; set; } = string.Empty;
        public string TextColor { get; set; } = string.Empty;

        public string BgColor { get; set; } = string.Empty;

        public string BgImgPath { get; set; } = string.Empty;
    }

    public class ScopeData
    {
        public string Type { get; set; }
        public ScopeDataDetail Data { get; set; }
    }

    public class ScopeDataDetail
    {
        public long StartTime { get; set; }
        public long EndTime { get; set; }
        public string specifyType { get; set; }
        public string specifyData { get; set; }
    }

    public class FlashSaleProduct
    {
        public int ProId { get; set; }
        public decimal Price { get; set; }
        public decimal PromotionPrice { get; set; }
    }
    public class FlashSaleSetting
    {
        public List<string> Activities { get; set; } // "ing", "no_start"
    }

    public class WebPackConfig
    {
        public string ProductsSalesTextStyle { get; set; } // "number" 或 "text"
        public string ProductsSalesPosition { get; set; }
        public string ProductsSalesBgStyle { get; set; }
    }
}
