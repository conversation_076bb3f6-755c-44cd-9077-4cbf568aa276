using AutoMapper.Configuration.Annotations;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entitys
{
    internal class VM_Form
    {
    }

    public partial class app_form_tool
    {
        [SugarColumn(IsIgnore = true)]
        public int dataCount { get; set; } = 0; // 表单数据数量

        [SugarColumn(IsIgnore = true)]
        public int noReadCount { get; set; } = 0;//未读数量

        [SugarColumn(IsIgnore = true)]
        public List<app_form_tool_field> fields { get; set; } = new List<app_form_tool_field>(); // 表单字段列表

        [SugarColumn(IsIgnore = true)]
        public string FormHtml { get; set; } = string.Empty; // 表单HTML内容

        [SugarColumn(IsIgnore = true)]
        public FormBtn FormBtn { get; set; }
    }



    public class FormBtn
    {
        public string BtnText { get; set; }
        public string BtnBg { get; set; }
        public string BtnWord { get; set; }

    }



    public partial class app_form_tool_field
    {
        [SugarColumn(IsIgnore = true)]
        public FieldSetting fieldSettingModel { get; set; }


    }


    public class FieldSetting
    {
        public string placeholder { get; set; }
        public string required { get; set; }
        public string format { get; set; }
        public string tip { get; set; }
        public string limit { get; set; }
        public List<string> content { get; set; }
        public string colums { get; set; }
    }

}
