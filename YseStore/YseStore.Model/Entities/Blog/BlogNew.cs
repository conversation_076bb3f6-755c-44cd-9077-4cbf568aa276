using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace YseStore.Model.Entities.Blog
{
    /// <summary>
    /// 博客实体类
    /// </summary>
    [SugarTable("blog_new")]
    public class BlogNew
    {
        /// <summary>
        /// 文章唯一标识符
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int AId { get; set; }

        /// <summary>
        /// 分类ID
        /// </summary>
        public uint? CateId { get; set; }

        /// <summary>
        /// 标题
        /// </summary>
        [SugarColumn(Length = 255)]
        public string Title { get; set; }

        /// <summary>
        /// 图片路径
        /// </summary>
        [SugarColumn(Length = 200)]
        public string PicPath { get; set; }

        /// <summary>
        /// 作者
        /// </summary>
        [SugarColumn(Length = 50)]
        public string Author { get; set; }

        /// <summary>
        /// 发布者
        /// </summary>
        [SugarColumn(Length = 50)]
        public string Posted { get; set; }

        /// <summary>
        /// SEO标题
        /// </summary>
        [SugarColumn(Length = 255)]
        public string SeoTitle_en { get; set; }

        /// <summary>
        /// SEO关键词
        /// </summary>
        [SugarColumn(ColumnDataType = "text")]
        public string SeoKeyword_en { get; set; }

        /// <summary>
        /// SEO描述
        /// </summary>
        [SugarColumn(Length = 500)]
        public string SeoDescription_en { get; set; }

        /// <summary>
        /// 简短介绍
        /// </summary>
        [SugarColumn(Length = 500)]
        public string BriefDescription { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        [SugarColumn(ColumnDataType = "text")]
        public string Tag { get; set; }

        /// <summary>
        /// 是否热门
        /// </summary>
        public bool? IsHot { get; set; }

        /// <summary>
        /// 是否为草稿
        /// </summary>
        public bool IsDraft { get; set; }

        /// <summary>
        /// 自定义地址
        /// </summary>
        [SugarColumn(Length = 500)]
        [Required]
        public string PageUrl { get; set; }

        /// <summary>
        /// 发布时间戳
        /// </summary>
        public int? AccTime { get; set; }

        /// <summary>
        /// 最后修改时间戳
        /// </summary>
        public int ModifyAt { get; set; }

        /// <summary>
        /// 浏览数量
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// 排序值
        /// </summary>
        public short MyOrder { get; set; }

        /// <summary>
        /// 来源（normal默认，old旧版）
        /// </summary>
        [SugarColumn(Length = 10)]
        public string Source { get; set; }

        /// <summary>
        /// 视频路径
        /// </summary>
        [SugarColumn(Length = 255)]
        public string VideoPath { get; set; }

        /// <summary>
        /// 转为前端时间格式 "Aug 12, 2024" 形式
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string? StandardAccTime { get; set; }

        /// <summary>
        /// 评论数
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public long Comments { get; set; }

        /// <summary>
        /// 点赞数
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public long Praise { get; set; }

        /// <summary>
        /// 标签列表名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<string> TagsNameList { get; set; } = new List<string>();

        /// <summary>
        /// 评论列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<BlogReview> Reviews { get; set; } = new List<BlogReview>();
    }
}