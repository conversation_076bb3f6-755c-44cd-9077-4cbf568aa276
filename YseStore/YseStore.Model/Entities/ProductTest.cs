using SqlSugar;

namespace YseStore.Model.Entities
{
    /// <summary>
    /// 产品实体类，对应数据库Product表
    /// </summary>
    [SugarTable("product_test")]
    public class ProductTest : IAutoGenerated
    {
        /// <summary>
        /// 产品ID，主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [SugarColumn(ColumnName = "Name", Length = 100, IsNullable = false)]
        public string Name { get; set; }

        /// <summary>
        /// 产品描述
        /// </summary>
        [SugarColumn(ColumnName = "Description", Length = 1000, IsNullable = true)]
        public string Description { get; set; }

        /// <summary>
        /// 产品价格
        /// </summary>
        [SugarColumn(ColumnName = "Price", IsNullable = false, ColumnDataType = "decimal(18,2)")]
        public decimal Price { get; set; }

        /// <summary>
        /// 产品库存
        /// </summary>
        [SugarColumn(ColumnName = "Stock", IsNullable = false, DefaultValue = "0")]
        public int Stock { get; set; }

        /// <summary>
        /// 产品图片URL
        /// </summary>
        [SugarColumn(ColumnName = "ImageUrl", Length = 200, IsNullable = true)]
        public string ImageUrl { get; set; }

        /// <summary>
        /// 是否热门
        /// </summary>
        [SugarColumn(ColumnName = "IsHot", IsNullable = false, DefaultValue = "0")]
        public bool IsHot { get; set; }

        /// <summary>
        /// 是否新品
        /// </summary>
        [SugarColumn(ColumnName = "IsNew", IsNullable = false, DefaultValue = "0")]
        public bool IsNew { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "CreateTime", IsNullable = false)]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(ColumnName = "UpdateTime", IsNullable = true)]
        public DateTime? UpdateTime { get; set; }
    }
}