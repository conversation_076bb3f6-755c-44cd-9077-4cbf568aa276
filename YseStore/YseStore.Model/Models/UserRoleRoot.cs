using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model 
{
    /// <summary>
    /// 用户跟角色关联表
    /// 父类
    /// </summary>
    public class UserRoleRoot<Tkey> : RootEntityTkey<Tkey> where Tkey : IEquatable<Tkey>
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public Tkey UserId { get; set; }
        /// <summary>
        /// 角色ID
        /// </summary>
        public Tkey RoleId { get; set; }

    }
}
