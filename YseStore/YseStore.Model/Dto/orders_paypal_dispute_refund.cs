using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class orders_paypal_dispute_refund
    {
        /// <summary>
        /// 
        /// </summary>
        public orders_paypal_dispute_refund()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 RId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 DId { get; set; }

        /// <summary>
        /// 0:提议 1:索赔
        /// </summary>
        public System.Boolean Type { get; set; }

        /// <summary>
        /// 0:取消 1:进行中 2:完成
        /// </summary>
        public System.Boolean Status { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Note { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Currency { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal Amount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String OfferType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String ReasonType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String ShippingAddress { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 AccTime { get; set; }
    }
}