using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 快递公司分区
    /// </summary>
    public class shipping_area
    {
        /// <summary>
        /// 快递公司分区
        /// </summary>
        public shipping_area()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.UInt16 AId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.UInt16? SId { get; set; }


        /// <summary>
        /// 配送地区名称 (仅后台可见)
        /// </summary>
        public System.String AreaName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsFreeShipping { get; set; }

        /// <summary>
        /// 免费类型 freight:运费 surcharge:附加费
        /// </summary>
        public System.String FreeType { get; set; }

        /// <summary>
        /// 免费条件
        /// </summary>
        public System.String FreeShippingType { get; set; }

        /// <summary>
        /// 免费条件-最低消费金额
        /// </summary>
        public System.Decimal? FreeShippingPrice { get; set; }

        /// <summary>
        /// 免费条件-最高产品重量
        /// </summary>
        public System.Single? FreeShippingWeight { get; set; }

        /// <summary>
        /// 免费条件-最高产品重量(记录)
        /// </summary>
        public System.Single FreeShippingSaveWeight { get; set; }

        /// <summary>
        /// 免费条件-最低购买数量
        /// </summary>
        public System.Int32 FreeShippingQty { get; set; }

        /// <summary>
        /// 附加费用
        /// </summary>
        public System.Decimal? AffixPrice { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Brief { get; set; }

        /// <summary>
        /// 重量单位
        /// </summary>
        public System.String WeightUnit { get; set; }

        /// <summary>
        /// 是否支持货到付款
        /// </summary>
        public System.Boolean? IsSupportDelivery { get; set; }

        /// <summary>
        /// 是否兼容转移（临时）
        /// </summary>
        public System.Boolean? IsChange { get; set; }

        /// <summary>
        /// 分区配送范围(country:指定国家/地区 zipCode:指定邮政编码)
        /// </summary>
        public System.String DeliveryRange { get; set; }
    }
}