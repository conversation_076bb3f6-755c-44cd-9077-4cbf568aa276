using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class collection_sync_task
    {
        /// <summary>
        /// 
        /// </summary>
        public collection_sync_task()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 TId { get; set; }

        /// <summary>
        /// 任务类型
        /// </summary>
        public System.String Platform { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public System.Int32 TaskId { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        public System.Boolean TaskStatus { get; set; }

        /// <summary>
        /// 任务进度
        /// </summary>
        public System.SByte CompletionRate { get; set; }

        /// <summary>
        /// 任务返回信息
        /// </summary>
        public System.String ReturnData { get; set; }

        /// <summary>
        /// 采集产品ID
        /// </summary>
        public System.String ProData { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public System.Int32 AccTime { get; set; }
    }
}