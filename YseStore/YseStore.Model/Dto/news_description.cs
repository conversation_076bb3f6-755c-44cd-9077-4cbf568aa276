using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class news_description
    {
        /// <summary>
        /// 
        /// </summary>
        public news_description()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 DId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? NewsId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Description_en { get; set; }

        /// <summary>
        /// 是否启用移动端描述 0-不启用 1-启用
        /// </summary>
        public System.Boolean UsedMobile { get; set; }

        /// <summary>
        /// 移动端描述内容
        /// </summary>
        public System.String MobileDescription { get; set; }
    }
}