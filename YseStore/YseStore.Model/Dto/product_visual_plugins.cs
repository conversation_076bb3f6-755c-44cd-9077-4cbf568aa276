using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class product_visual_plugins
    {
        /// <summary>
        /// 
        /// </summary>
        public product_visual_plugins()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 PId { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public System.String Type { get; set; }

        /// <summary>
        /// 风格
        /// </summary>
        public System.String Mode { get; set; }

        /// <summary>
        /// 设置
        /// </summary>
        public System.String Settings { get; set; }

        /// <summary>
        /// 内容模块
        /// </summary>
        public System.String Blocks { get; set; }

        /// <summary>
        /// 插件配置
        /// </summary>
        public System.String Config { get; set; }
    }
}