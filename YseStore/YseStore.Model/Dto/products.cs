using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class products
    {
        /// <summary>
        /// 
        /// </summary>
        public products()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 ProId { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public System.Int32? CateId { get; set; } = 0;

        /// <summary>
        /// 
        /// </summary>
        public System.String Source { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SourceId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Name_en { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public System.String Prefix { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Number { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SKU { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? Price_0 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? Price_1 { get; set; }

        /// <summary>
        /// 显示原价
        /// </summary>
        public System.Decimal ShowPrice_0 { get; set; }

        /// <summary>
        /// 显示价格
        /// </summary>
        public System.Decimal ShowPrice_1 { get; set; }

        /// <summary>
        /// 成本价
        /// </summary>
        public System.Decimal CostPrice { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public System.Int32? StartTime { get; set; } = 0;

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? EndTime { get; set; } = 0;

        /// <summary>
        /// 
        /// </summary>
        public System.String Wholesale { get; set; }

        /// <summary>
        /// 图片ID
        /// </summary>
        public System.String ImageId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String PicPath_0 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String PicPath_1 { get; set; }

        /// <summary>
        /// 首图数据
        /// </summary>
        public System.String FirstImageData { get; set; }

        /// <summary>
        /// alt
        /// </summary>
        public System.String Alt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Single? Weight { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Cubage { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public System.Int32? MOQ { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? MaxOQ { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? Sales { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? Stock { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? WarnStock { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //public System.Boolean? StockOut { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public System.Int16 IsCombination { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? SoldOut { get; set; }


        /// <summary>
        /// 跟踪库存，取值:0#库存为 0 不允许购买 1#库存为0 允许购买2 # 库存为 0 自动下架，库存不为0 自动上架
        /// </summary>
        public byte SoldStatus { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsFreeShipping { get; set; }

        /// <summary>
        /// 是否为虚拟产品
        /// </summary>
        public System.Boolean isVirtual { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 TId { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? Rating { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? TotalRating { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsDefaultReview { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? DefaultReviewRating { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? DefaultReviewTotalRating { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? FavoriteCount { get; set; }


        /// <summary>
        /// 简短介绍
        /// </summary>
        public System.String BriefDescription_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String BriefDescription1_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? EditTime { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public System.Int32 ModifyTime { get; set; }


        /// <summary>
        /// 	内部链接优化
        /// </summary>
        public System.String PageUrl { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public System.String Tags { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String VideoUrl { get; set; }

        /// <summary>
        /// 视频类型
        /// </summary>
        public System.String VideoType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? MyOrder { get; set; }

        /// <summary>
        /// 靠前排序
        /// </summary>
        public System.Boolean TopOrder { get; set; }

        /// <summary>
        /// 是否开启虚拟数据
        /// </summary>
        public System.Boolean IsVirtualData { get; set; }

        /// <summary>
        /// 虚拟销量
        /// </summary>
        public System.Int32 VirtualSales { get; set; }

        /// <summary>
        /// 促销价
        /// </summary>
        public decimal PromotionPrice { get; set; }

        /// <summary>
        /// 几种规格
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? Specification { get; set; } = 0;
        /// <summary>
        /// 重量单位
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string WeightUnit { get; set; }
        [SugarColumn(IsIgnore = true)] public DateTime AccTimeBeiJing { get; set; }
        [SugarColumn(IsIgnore = true)] public DateTime ModifyTimeBeiJing { get; set; }
    }
}