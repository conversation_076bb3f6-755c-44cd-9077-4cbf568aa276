using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class app_googlefeed_combination
    {
        /// <summary>
        /// 
        /// </summary>
        public app_googlefeed_combination()
        {
        }

        /// <summary>
        /// 规格 ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public System.Int32 CId { get; set; }

        /// <summary>
        /// 条码
        /// </summary>
        public System.String GTIN { get; set; }

        /// <summary>
        /// 颜色
        /// </summary>
        public System.String Color { get; set; }

        /// <summary>
        /// 尺寸
        /// </summary>
        public System.String Size { get; set; }

        /// <summary>
        /// 制造商部件号
        /// </summary>
        public System.String MPN { get; set; }
    }
}