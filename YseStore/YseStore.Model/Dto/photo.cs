using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class photo
    {
        /// <summary>
        /// 
        /// </summary>
        public photo()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.UInt32 PId { get; set; }

     
        /// <summary>
        /// 
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String PicPath { get; set; }

        /// <summary>
        /// 图片宽度
        /// </summary>
        public System.Int32 Width { get; set; }

        /// <summary>
        /// 图片高度
        /// </summary>
        public System.Int32 Height { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String IsSystem { get; set; }

        /// <summary>
        /// 图片标签ID
        /// </summary>
        public System.String TagsId { get; set; }

        public string Host { get; set; }

        public long Size { get; set; }
        public string OriName { get; set; }
    }
}