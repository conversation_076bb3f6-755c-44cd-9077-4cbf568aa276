using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// FAQ表
    /// </summary>
    public class faq
    {
        /// <summary>
        /// FAQ表
        /// </summary>
        public faq()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 FId { get; set; }

        /// <summary>
        /// 标题
        /// </summary>
        public System.String Title { get; set; }

        /// <summary>
        /// 适用范围
        /// </summary>
        public System.String Scope { get; set; }

        /// <summary>
        /// 范围值
        /// </summary>
        public System.String Value { get; set; }

        /// <summary>
        /// 展示类型
        /// </summary>
        public System.String ShowType { get; set; }

        /// <summary>
        /// 显示页面
        /// </summary>
        public System.String Pages { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        public System.Int32? AccTime { get; set; }
    }
}