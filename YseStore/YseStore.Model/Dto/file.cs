using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class File
    {
        /// <summary>
        /// 
        /// </summary>
        public File()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.UInt32 FId { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public System.String Path { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>
        public System.String Type { get; set; }

        /// <summary>
        /// 文件标签ID
        /// </summary>
        public System.String TagsId { get; set; }

        /// <summary>
        /// 文件信息
        /// </summary>
        public System.String FileInfo { get; set; }

        public string Host { get; set; }

        public long Size { get; set; }  

        public string OriName { get; set; }
    }
}