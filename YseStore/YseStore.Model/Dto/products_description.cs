using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class products_description
    {
        /// <summary>
        /// 
        /// </summary>
        public products_description()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 DId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? ProId { get; set; }

      

        /// <summary>
        /// 使用状态 0:不使用 1:使用中 2:隐藏
        /// </summary>
        public System.Boolean Status { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Description_en { get; set; }

        /// <summary>
        /// 是否开启可视化
        /// </summary>
        public System.Boolean? Visual { get; set; }

        /// <summary>
        /// 是否启用移动端描述 0-不启用 1-启用
        /// </summary>
        public System.Boolean UsedMobile { get; set; }

        /// <summary>
        /// 移动端描述内容
        /// </summary>
        public System.String MobileDescription { get; set; }
         

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 MyOrder { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 AccTime { get; set; }
    }
}