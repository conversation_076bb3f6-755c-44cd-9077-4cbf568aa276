using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class chat
    {
        /// <summary>
        /// 
        /// </summary>
        public chat()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 CId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? Type { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String PicPath { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Account { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? MyOrder { get; set; }
    }
}