using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 产品询盘
    /// </summary>
    public class products_inquiry
    {
        /// <summary>
        /// 产品询盘
        /// </summary>
        public products_inquiry()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 IId { get; set; }

        /// <summary>
        /// 产品id
        /// </summary>
        public System.Int32? ProId { get; set; }

        /// <summary>
        /// 询盘人名称
        /// </summary>
        public System.String FullName { get; set; }

        /// <summary>
        /// 询盘人邮箱
        /// </summary>
        public System.String Email { get; set; }

        /// <summary>
        /// 询盘人Whatsapp/电话
        /// </summary>
        public System.String Phone { get; set; }

        /// <summary>
        /// 询盘内容
        /// </summary>
        public System.String Content { get; set; }

        /// <summary>
        /// 图片1
        /// </summary>
        public System.String PicPath_0 { get; set; }

        /// <summary>
        /// 图片2
        /// </summary>
        public System.String PicPath_1 { get; set; }

        /// <summary>
        /// 图片3
        /// </summary>
        public System.String PicPath_2 { get; set; }

        /// <summary>
        /// 图片4
        /// </summary>
        public System.String PicPath_3 { get; set; }

        /// <summary>
        /// 图片5
        /// </summary>
        public System.String PicPath_4 { get; set; }

        /// <summary>
        /// 询盘时间
        /// </summary>
        public System.Int32? AccTime { get; set; }

        /// <summary>
        /// 询盘ip
        /// </summary>
        public System.String Ip { get; set; }

        /// <summary>
        /// 是否已读
        /// </summary>
        public System.Boolean? IsRead { get; set; }
    }
}