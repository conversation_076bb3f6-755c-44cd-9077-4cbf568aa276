using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 运费分区的国家ID
    /// </summary>
    public class shipping_country
    {
        /// <summary>
        /// 运费分区的国家ID
        /// </summary>
        public shipping_country()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.UInt32 Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.UInt16? SId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.UInt16? AId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.UInt16? CId { get; set; }

        /// <summary>
        /// 省份ID
        /// </summary>
        public System.Int16 StatesSId { get; set; }

     
        /// <summary>
        /// 分区配送范围(country:指定国家/地区 zipCode:指定邮政编码)
        /// </summary>
        public System.String DeliveryRange { get; set; }

        /// <summary>
        /// 配送范围数据
        /// </summary>
        public System.String DeliveryData { get; set; }
    }
}