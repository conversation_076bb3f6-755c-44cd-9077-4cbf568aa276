using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class app_collection_task
    {
        /// <summary>
        /// 
        /// </summary>
        public app_collection_task()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 TId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Url { get; set; }

        /// <summary>
        /// 状态 0-待采集 1-采集中 2-已采集
        /// </summary>
        public System.Boolean? Status { get; set; }

        /// <summary>
        /// 采集时间
        /// </summary>
        public System.Int32? AccTime { get; set; }
    }
}