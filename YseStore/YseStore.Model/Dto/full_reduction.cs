using SqlSugar;
using YseStore.Model.Enums;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class full_reduction
    {
        /// <summary>
        /// 
        /// </summary>
        public full_reduction()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 FId { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 优惠类型 0-满额减钱 1-满额打折 2-满件减钱 3-满件打折 4-每满额减钱 5-每满件减钱 6-满额赠礼 7-满件赠礼 8-每满额赠礼 9-每满件赠礼
        /// </summary>
        public DiscountTypeEnum Type { get; set; }

        /// <summary>
        /// 优惠规则
        /// </summary>
        public System.String Rule { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public System.Int32? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public System.Int32? EndTime { get; set; }

        /// <summary>
        /// 指定类型 0-所有产品 1-指定产品 2-指定分类
        /// </summary>
        public UseProductsEnum? UseProducts { get; set; }

        /// <summary>
        /// 指定类型的值
        /// </summary>
        public System.String UseProductsValue { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }
    }
}