using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class manage_operation_log
    {
        /// <summary>
        /// 
        /// </summary>
        public manage_operation_log()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 LId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? UserId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String UserName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Module { get; set; }

        /// <summary>
        /// 子模块
        /// </summary>
        public System.String Action { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Ip { get; set; }

        /// <summary>
        /// 终端信息
        /// </summary>
        public System.String Terminal { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Log { get; set; }

        /// <summary>
        /// 日志详细
        /// </summary>
        public System.String LogDesc { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Data { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }
    }
}