using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 评论回复
    /// </summary>
    public class products_review_reply
    {
        /// <summary>
        /// 评论回复
        /// </summary>
        public products_review_reply()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 ReId { get; set; }

        /// <summary>
        /// 评论id
        /// </summary>
        public System.Int32? RId { get; set; }

        /// <summary>
        /// 会员Id（如果是管理员就是管理员id）
        /// </summary>
        public System.Int32? UserId { get; set; }

        /// <summary>
        /// 评论身份
        /// </summary>
        public System.String Identity { get; set; }

        /// <summary>
        /// 回复人名称
        /// </summary>
        public System.String ResponderName { get; set; }

        /// <summary>
        /// 回复内容
        /// </summary>
        public System.String Content { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        public System.Int32? AccTime { get; set; }

        /// <summary>
        /// 被回复人的名称
        /// </summary>
        public string ReplyToName { get; set; }

        /// <summary>
        /// 被回复的回复ID
        /// </summary>
        public int? ReplyToId { get; set; }
    }
}