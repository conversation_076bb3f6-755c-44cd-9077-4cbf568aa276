using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.Dto
{
    /// <summary>
    /// 钱海手续费表
    /// </summary>
    public class paymethod_fee_oceanpayment
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public int Id { get; set; }

        /// <summary>
        /// 卡种
        /// </summary>
        public string CardType { get; set; }

        /// <summary>
        /// 卡种名称
        /// </summary>
        [SugarColumn(IsJson =true)]
        public List<string> CardCode { get; set; }

        /// <summary>
        /// 费率
        /// </summary>
        public decimal Rate { get; set; }

        /// <summary>
        /// 单笔手续费金额
        /// </summary>
        public decimal Fee { get; set; }

        /// <summary>
        /// 币种
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        [SugarColumn(IsJson = true)]
        public List<string> CountryCode { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public string Type { get; set; }
    }
}
