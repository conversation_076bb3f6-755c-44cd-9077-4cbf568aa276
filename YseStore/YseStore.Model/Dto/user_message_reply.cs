using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class user_message_reply
    {
        /// <summary>
        /// 
        /// </summary>
        public user_message_reply()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 RId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? MId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? UserId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Content { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String PicPath { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }
    }
}