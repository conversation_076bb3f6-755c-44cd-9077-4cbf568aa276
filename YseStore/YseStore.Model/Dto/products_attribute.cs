using SqlSugar;

namespace Entitys;

/// <summary>
/// 
/// </summary>
public class products_attribute
{
    /// <summary>
    /// 
    /// </summary>
    public products_attribute()
    {
    }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public System.Int32 AttrId { get; set; }

    /// <summary>
    /// 属性名称
    /// </summary>
    public System.String Name_en { get; set; }


    /// <summary>
    /// 产品ID
    /// </summary>
    public System.Int32 ProId { get; set; }

    /// <summary>
    /// 属性位置
    /// </summary>
    public System.SByte Position { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Type { get; set; }

    /// <summary>
    /// 属性选项
    /// </summary>
    public System.String Options { get; set; }

    /// <summary>
    /// 选项数据
    /// </summary>
    public System.String OptionsData { get; set; }

    /// <summary>
    /// 是否为主属性(针对关联主图)
    /// </summary>
    public System.Boolean IsMain { get; set; }
     

    /// <summary>
    /// 
    /// </summary>
    public System.String Description_en { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Byte? MyOrder { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32 AccTime { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public System.String Source { get; set; }

    /// <summary>
    /// 来源ID
    /// </summary>
    public System.String SourceId { get; set; }
}