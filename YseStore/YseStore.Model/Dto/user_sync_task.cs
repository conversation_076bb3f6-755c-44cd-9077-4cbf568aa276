using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class user_sync_task
    {
        /// <summary>
        /// 
        /// </summary>
        public user_sync_task()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 TId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Platform { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? TaskId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? TaskStatus { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.SByte? CompletionRate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String ReturnData { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }
    }
}