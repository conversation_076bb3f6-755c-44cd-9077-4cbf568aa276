using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class orders_label_status
    {
        /// <summary>
        /// 
        /// </summary>
        public orders_label_status()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 SId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 OrderId { get; set; }

        /// <summary>
        /// 临时订单号
        /// </summary>
        public System.Int32 TempOrderId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean GoodsType { get; set; }

        /// <summary>
        /// 是否已生成标签
        /// </summary>
        public System.Boolean Status { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 AccTime { get; set; }
    }
}