using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class orders_payment_info
    {
        /// <summary>
        /// 
        /// </summary>
        public orders_payment_info()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 InfoId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? OrderId { get; set; }

        /// <summary>
        /// 临时订单ID
        /// </summary>
        public System.Int32 TempOrderId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Account { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String FirstName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String LastName { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        public System.Decimal? SentMoney { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String MTCNNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Currency { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Country { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SentTime { get; set; }

        /// <summary>
        /// 交易号
        /// </summary>
        public System.String BankTransactionNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Contents { get; set; }

        /// <summary>
        /// 图片
        /// </summary>
        public System.String PicPath_0 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }
    }
}