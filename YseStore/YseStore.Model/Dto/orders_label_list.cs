using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class orders_label_list
    {
        /// <summary>
        /// 
        /// </summary>
        public orders_label_list()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 LId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String OrderId { get; set; }

        /// <summary>
        /// 亚翔供应链物流代码
        /// </summary>
        public System.String ProductCode { get; set; }

        /// <summary>
        /// 亚翔供应链物流名称
        /// </summary>
        public System.String ProductName { get; set; }

        /// <summary>
        /// 亚翔供应链物流运费
        /// </summary>
        public System.Decimal? Amount { get; set; }

        /// <summary>
        /// 亚翔供应链物流标签
        /// </summary>
        public System.String LabelUrl { get; set; }

        /// <summary>
        /// 亚翔供应链物流客户单号
        /// </summary>
        public System.String BusinessNo { get; set; }

        /// <summary>
        /// 亚翔供应链物流PDF文件
        /// </summary>
        public System.String FilePath { get; set; }

        /// <summary>
        /// 物流产品类型：1： 普货， 2：敏感， 3：带电
        /// </summary>
        public System.SByte? GoodsType { get; set; }
    }
}