using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class auth_item_child
    {
        /// <summary>
        /// 
        /// </summary>
        public auth_item_child()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public System.String parent { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public System.String child { get; set; }
    }
}