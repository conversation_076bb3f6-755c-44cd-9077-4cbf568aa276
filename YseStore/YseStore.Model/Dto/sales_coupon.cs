using SqlSugar;
using YseStore.Model.Enums;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class sales_coupon
    {
        /// <summary>
        /// 
        /// </summary>
        public sales_coupon()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 CId { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public System.String CouponNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? Discount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? Money { get; set; }

        /// <summary>
        /// 优惠券类型 (0: 折扣券 1:现金券)
        /// </summary>
        public CouponTypeEnum? CouponType { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public System.Int32? StartTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? EndTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public System.String UserId { get; set; }

      

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? UseNum { get; set; }

        /// <summary>
        /// 不限使用次数
        /// </summary>
        public System.Boolean? UnLmUseNum { get; set; }

        /// <summary>
        /// 领取数量
        /// </summary>
        public System.Int32 Qty { get; set; }

        /// <summary>
        /// 不限领取数量
        /// </summary>
        public System.Boolean UnLmQty { get; set; }

    
      
         

        /// <summary>
        /// 优惠券类型的值
        /// </summary>
        public System.Decimal CouponTypeValue { get; set; }

        /// <summary>
        /// 使用要求 (0:最低消费金额 1:最低购买数量)
        /// </summary>
        public ConditionTypeEnum ConditionType { get; set; }

        /// <summary>
        /// 最低消费金额
        /// </summary>
        public System.Decimal ConditionPrice { get; set; }

        /// <summary>
        /// 最低购买数量
        /// </summary>
        public System.Int32 ConditionQty { get; set; }

        /// <summary>
        /// 指定用户 (0:所有人可用 1:全部会员 2:部分会员 3:新注册会员)
        /// </summary>
        public System.Boolean UseGroup { get; set; }

        /// <summary>
        /// 指定用户的值
        /// </summary>
        public System.String UseGroupValue { get; set; }

        /// <summary>
        /// 指定产品 (0:全部产品 1:指定产品 2:指定分类 3:指定标签)
        /// </summary>
        public ProductScope UseProducts { get; set; }

        /// <summary>
        /// 指定产品值
        /// </summary>
        public System.String UseProductsValue { get; set; }

        /// <summary>
        /// 是否失效 1是 0否
        /// </summary>
        public System.Int32 IsExpired { get; set; }

        /// <summary>
        /// 投放方式 (manual:手动 auto:自动)
        /// </summary>
        public System.String DeliveryMethod { get; set; }

        /// <summary>
        /// 赠送方式(register:完成会员注册 member:登录成功 review:评论成功)
        /// </summary>
        public System.String GiftMethod { get; set; }

        /// <summary>
        /// 指定顾客 (all:所有人 group:顾客组 individual:个别顾客)
        /// </summary>
        public System.String UseCustomer { get; set; }

        /// <summary>
        /// 指定顾客的值
        /// </summary>
        public System.String UseCustomerValue { get; set; }

        /// <summary>
        /// 是否显示在产品详细页
        /// </summary>
        public System.Boolean IsDetailShow { get; set; }

        /// <summary>
        /// 是否允许与满减折扣、会员折扣叠加使用
        /// </summary>
        public System.Boolean IsAllowDiscount { get; set; }

        /// <summary>
        /// 是否限制最高抵扣金额
        /// </summary>
        public System.Boolean IsMaxAmount { get; set; }

        /// <summary>
        /// 最高抵扣金额
        /// </summary>
        public System.Decimal MaxAmount { get; set; }

        /// <summary>
        /// 取消订单是否返还优惠券
        /// </summary>
        public System.Boolean OrderCancelReturn { get; set; }

        /// <summary>
        /// 有效期 (fixed:固定时间 receive:领取时间)
        /// </summary>
        public System.String ValidityType { get; set; }

        /// <summary>
        /// 有效时长
        /// </summary>
        public System.Int32 Duration { get; set; }

        /// <summary>
        /// 时长类型 (day:天 hour:小时)
        /// </summary>
        public System.String TimeType { get; set; }

        /// <summary>
        /// 是否允许与限时促销、批发叠加使用
        /// </summary>
        public System.Boolean IsAllowPromotion { get; set; }

        /// <summary>
        /// 手动投放发送邮件次数
        /// </summary>
        public System.Boolean? ManualSendEmailCount { get; set; }

        /// <summary>
        /// 手动投放发送邮件时间
        /// </summary>
        public System.Int32? ManualSendEmailTime { get; set; }

        /// <summary>
        /// 来源类型 (points:积分 system:系统)
        /// </summary>
        public System.String CouponSource { get; set; }

        /// <summary>
        /// 来源类型ID
        /// </summary>
        public System.Int32 CouponSourceId { get; set; }
    }
}