using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class info_category
    {
        /// <summary>
        /// 
        /// </summary>
        public info_category()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 CateId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Category_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String UId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? Dept { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? SubCateCount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SeoTitle_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SeoKeyword_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SeoDescription_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? MyOrder { get; set; }
    }
}