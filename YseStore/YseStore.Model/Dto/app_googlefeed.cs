using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class app_googlefeed
    {
        /// <summary>
        /// 
        /// </summary>
        public app_googlefeed()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 GId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 ProId { get; set; }

        /// <summary>
        /// 全球贸易项目代码(Global Trade Item Number)
        /// </summary>
        public System.String GTIN { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String MPN { get; set; }

        /// <summary>
        /// shipping price [配送价格]
        /// </summary>
        public System.Decimal ShippingPrice { get; set; }

        /// <summary>
        /// tax rate [税率]
        /// </summary>
        public System.Decimal TaxRate { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public System.String Brand { get; set; }

        /// <summary>
        /// 自定义标签
        /// </summary>
        public System.String Label { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public System.String Sex { get; set; }

        /// <summary>
        /// 年龄段
        /// </summary>
        public System.String Age { get; set; }

        /// <summary>
        /// 颜色
        /// </summary>
        public System.String Color { get; set; }

        /// <summary>
        /// 尺寸
        /// </summary>
        public System.String Size { get; set; }

        /// <summary>
        /// 亮点
        /// </summary>
        public System.String Highlight { get; set; }

        /// <summary>
        /// 同步时间
        /// </summary>
        public System.Int32 SyncUpdateTime { get; set; }

        /// <summary>
        /// 谷歌分类
        /// </summary>
        public System.String GoogleProductCategory { get; set; }

        /// <summary>
        /// 自定义标签0
        /// </summary>
        public System.String CustomLabel0 { get; set; }

        /// <summary>
        /// 标签1
        /// </summary>
        public System.String CustomLabel1 { get; set; }

        /// <summary>
        /// 标签2
        /// </summary>
        public System.String CustomLabel2 { get; set; }

        /// <summary>
        /// 标签3
        /// </summary>
        public System.String CustomLabel3 { get; set; }

        /// <summary>
        /// 标签4
        /// </summary>
        public System.String CustomLabel4 { get; set; }

        /// <summary>
        /// 年龄阶层
        /// </summary>
        public System.String AgeGroup { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public System.String Gender { get; set; }

        /// <summary>
        /// 产品状况
        /// </summary>
        public System.String KeyCondition { get; set; }

        /// <summary>
        /// 材料
        /// </summary>
        public System.String Material { get; set; }

        /// <summary>
        /// 尺码类型
        /// </summary>
        public System.String SizeType { get; set; }

        /// <summary>
        /// 尺码制
        /// </summary>
        public System.String SizeSystem { get; set; }

        /// <summary>
        /// 颜色关联属性
        /// </summary>
        public System.Int32 ColorAttr { get; set; }

        /// <summary>
        /// 尺寸关联属性
        /// </summary>
        public System.Int32 SizeAttr { get; set; }
    }
}