using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class cases
    {
        /// <summary>
        /// 
        /// </summary>
        public cases()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 CasesId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? CateId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Title_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String BriefDescription_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String PicPath { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? EditTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SeoTitle_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SeoKeyword_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SeoDescription_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String PageUrl { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? MyOrder { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }
    }
}