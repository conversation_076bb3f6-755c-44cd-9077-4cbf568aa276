using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// FAQ内容表
    /// </summary>
    public class faq_content
    {
        /// <summary>
        /// FAQ内容表
        /// </summary>
        public faq_content()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 CId { get; set; }

        /// <summary>
        /// faq表的主键
        /// </summary>
        public System.Int32? FId { get; set; }

        /// <summary>
        /// 标题
        /// </summary>
        public System.String Title { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public System.String Content { get; set; }

        /// <summary>
        /// 自定义排序
        /// </summary>
        public System.Int32? MyOrder { get; set; }
    }
}