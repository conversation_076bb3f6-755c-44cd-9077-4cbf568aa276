using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class products_platform
    {
        /// <summary>
        /// 
        /// </summary>
        public products_platform()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 PId { get; set; }

        /// <summary>
        /// 产品Id
        /// </summary>
        public System.Int32? ProId { get; set; }

        /// <summary>
        /// JSON形式的数据
        /// </summary>
        public System.String Data { get; set; }
    }
}