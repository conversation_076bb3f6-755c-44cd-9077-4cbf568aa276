using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class products_development
    {
        /// <summary>
        /// 
        /// </summary>
        public products_development()
        {
        }

        /// <summary>
        ///  产品ID，主键  --> 就是一个普通的主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 ProId { get; set; }

        /// <summary>
        /// 关联产品ID --> 关联主图的产品ID
        /// </summary>
        public System.Int32 RelevancyProId { get; set; }

        /// <summary>
        /// 评论库导入功能
        /// </summary>
        public System.Boolean IsReview { get; set; }

        /// <summary>
        /// 视频放第一位
        /// </summary>
        public System.Boolean VideoFirst { get; set; }

        /// <summary>
        /// (已弃用)产品销量
        /// </summary>
        public System.Int32 Sales { get; set; }

        /// <summary>
        /// 收藏数
        /// </summary>
        public System.Int32 FavoriteCount { get; set; }

        /// <summary>
        /// 默认评论平均分
        /// </summary>
        public System.Decimal DefaultReviewRating { get; set; }

        /// <summary>
        /// 默认评论人数
        /// </summary>
        public System.Int32 DefaultReviewTotalRating { get; set; }

        /// <summary>
        /// 自定义可视化
        /// </summary>
        public System.Boolean? IsVisual { get; set; }

        /// <summary>
        /// (已弃用)是否开启自定义的评论数据
        /// </summary>
        public System.SByte IsVirtual { get; set; }

        /// <summary>
        /// 批发价类型price:固定价格 discount:折扣
        /// </summary>
        public System.String WholesaleType { get; set; }

        /// <summary>
        /// 批发条件
        /// </summary>
        public System.String WholesaleConditions { get; set; }

        /// <summary>
        /// 批发价区间
        /// </summary>
        public System.String Wholesale { get; set; }

        /// <summary>
        /// 起订量
        /// </summary>
        public System.Int32 MOQ { get; set; }

        /// <summary>
        /// 最大购买量
        /// </summary>
        public System.Int32 MaxOQ { get; set; }

        /// <summary>
        /// 仓库选项
        /// </summary>
        public System.String Warehouse { get; set; }

        /// <summary>
        /// 是否操作了默认选项
        /// </summary>
        public System.Boolean? IsDefautlSettings { get; set; }

        /// <summary>
        /// 销售说明
        /// </summary>
        public System.String ProductsIntroduction { get; set; }

        /// <summary>
        /// 销售类型
        /// </summary>
        public System.String ProductsType { get; set; }

        /// <summary>
        /// 主图关联方式
        /// </summary>
        public System.String RelateMethod { get; set; }

        /// <summary>
        /// 扩展
        /// </summary>
        public System.String ProductsExpand { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public System.String SupplierName { get; set; }

        /// <summary>
        /// 采购链接
        /// </summary>
        public System.String PurchaseLinks { get; set; }

        /// <summary>
        /// 是否开启批发价(0停用,1开启)
        /// </summary>
        public System.Boolean IsWholesale { get; set; }

        /// <summary>
        /// 批发销售方式
        /// </summary>
        public System.String WholesaleMethod { get; set; }

        /// <summary>
        /// 批发销售方式件数(按批卖)
        /// </summary>
        public System.Int16 WholesaleMethodPieces { get; set; }

        /// <summary>
        /// 定价方式应用范围 product 单个产品, style 单个属性
        /// </summary>
        public System.String WholesalePricingScope { get; set; }
    }
}