using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class auth_item
    {
        /// <summary>
        /// 
        /// </summary>
        public auth_item()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public System.String name { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16 type { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String description { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String rule_name { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Byte[] data { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? created_at { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? updated_at { get; set; }

        public string path { get; set; }

        public int order { get; set; }
    }
}