using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class products_upload
    {
        /// <summary>
        /// 
        /// </summary>
        public products_upload()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 id { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public System.String name { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public System.String status { get; set; }

        /// <summary>
        /// 成功数量
        /// </summary>
        public System.Int32? success { get; set; }

        /// <summary>
        /// 失败数量
        /// </summary>
        public System.Int32? fail { get; set; }

        /// <summary>
        /// 总数量
        /// </summary>
        public System.Int32? total { get; set; }

        /// <summary>
        /// 进度
        /// </summary>
        public System.Int32? progress { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public System.Int64? taskId { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        public System.String reason { get; set; }

        /// <summary>
        /// 导入成功的产品ID
        /// </summary>
        public System.String proids { get; set; }

        /// <summary>
        /// 扩展信息
        /// </summary>
        public System.String extend { get; set; }

        /// <summary>
        /// 程序状态
        /// </summary>
        public System.String activity { get; set; }

        /// <summary>
        /// 最后一次处理时间
        /// </summary>
        public System.Int32 lastTime { get; set; }

        /// <summary>
        /// 最后一次请求消息队列的数据
        /// </summary>
        public System.String lastQueue { get; set; }
    }
}