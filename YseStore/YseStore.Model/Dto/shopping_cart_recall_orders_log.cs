using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class shopping_cart_recall_orders_log
    {
        /// <summary>
        /// 
        /// </summary>
        public shopping_cart_recall_orders_log()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 LId { get; set; }

        /// <summary>
        /// 召回类型
        /// </summary>
        public System.String Type { get; set; }

        /// <summary>
        /// 邮件编号
        /// </summary>
        public System.String MailID { get; set; }

        /// <summary>
        /// 订单ID
        /// </summary>
        public System.Int32? OrderId { get; set; }

        /// <summary>
        /// 订单价格
        /// </summary>
        public System.Decimal TotalPrice { get; set; }

        /// <summary>
        /// 记录时间
        /// </summary>
        public System.Int32? AccTime { get; set; }
    }
}