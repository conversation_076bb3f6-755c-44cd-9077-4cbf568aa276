using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class member_points_orders
    {
        /// <summary>
        /// 
        /// </summary>
        public member_points_orders()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 LId { get; set; }

        /// <summary>
        /// 会员ID
        /// </summary>
        public System.Int32 UserId { get; set; }

        /// <summary>
        /// 订单ID
        /// </summary>
        public System.Int32 OrderId { get; set; }

        /// <summary>
        /// 临时订单ID
        /// </summary>
        public System.Int32 TempOrderId { get; set; }

        /// <summary>
        /// 使用积分
        /// </summary>
        public System.Int32 Points { get; set; }

        /// <summary>
        /// 积分金额
        /// </summary>
        public System.Decimal PointsAmount { get; set; }

        /// <summary>
        /// 使用状态 (used:已使用 returned:已退还 unreturn:过期不退还)
        /// </summary>
        public System.String Status { get; set; }

        /// <summary>
        /// 使用时间
        /// </summary>
        public System.Int32 UsedAt { get; set; }

        /// <summary>
        /// 退还时间
        /// </summary>
        public System.Int32 ReturnedAt { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public System.Int32 ExpirTime { get; set; }
    }
}