using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class app_config
    {
        /// <summary>
        /// 
        /// </summary>
        public app_config()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 CId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String ClassName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String ConfigData { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }
    }
}