using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class shipping_carrier
    {
        /// <summary>
        /// 
        /// </summary>
        public shipping_carrier()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 CId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String name { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String data { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? MyOrder { get; set; }

        /// <summary>
        /// 显示名称
        /// </summary>
        public System.String Title { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public System.Boolean IsUsed { get; set; }

        /// <summary>
        /// 是否是系统自带
        /// </summary>
        public System.Boolean IsSystem { get; set; }
    }
}