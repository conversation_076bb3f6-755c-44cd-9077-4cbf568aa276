using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.Enums;

namespace YseStore.Model.Response.Sales
{
    public class MyCouponResponse
    {
        public System.Int32 CId { get; set; }

        /// <summary>
        /// 优惠码
        /// </summary>
        public System.String CouponNumber { get; set; }
        /// <summary>
        /// 优惠券类型 (0: 折扣券 1:现金券)
        /// </summary>
        public CouponTypeEnum? CouponType { get; set; }

        /// <summary>
        /// 使用要求 (0:最低消费金额 1:最低购买数量)
        /// </summary>
        public ConditionTypeEnum ConditionType { get; set; }

        /// <summary>
        /// 优惠券类型的值
        /// </summary>
        public System.Decimal CouponTypeValue { get; set; }

        /// <summary>
        /// 是否限制最高抵扣金额
        /// </summary>
        public System.Boolean IsMaxAmount { get; set; }

        /// <summary>
        /// 最高抵扣金额
        /// </summary>
        public System.Decimal MaxAmount { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public System.Int32? StartTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public System.Int32? EndTime { get; set; }
        /// <summary>
        /// 最低消费金额
        /// </summary>
        public System.Decimal ConditionPrice { get; set; }

        /// <summary>
        /// 最低购买数量
        /// </summary>
        public System.Int32 ConditionQty { get; set; }
        public string Rules { get; set; }
        //{
        //    get
        //    {
        //        var ruleBuilder = new StringBuilder();

        //        // 使用要求条件
        //        ruleBuilder.Append(ConditionType == ConditionTypeEnum.MinAmount ?
        //            $"满${ConditionPrice}，" :
        //            $"满{ConditionQty}件可用，");

        //        // 优惠类型逻辑
        //        if (CouponType == CouponTypeEnum.Discount) // 折扣券
        //        {
        //            ruleBuilder.Append($"{100 - CouponTypeValue} % off");
        //            if (IsMaxAmount && MaxAmount > 0) // 如果有最高抵扣金额
        //            {
        //                ruleBuilder.Append($"，最高抵扣{MaxAmount}元");
        //            }
        //        }
        //        else // 现金券
        //        {
        //            ruleBuilder.Append($"减${CouponTypeValue}");
        //        }

        //        //// 有效期展示逻辑
        //        //ruleBuilder.Append(ValidityType == ValidityTypeEnum.Fixed.ToString().ToLower() ?
        //        //    $"，有效期至{EndTime:yyyy-MM-dd HH:mm}" :
        //        //    $"，领取后{Duration}{TimeType}内有效");

        //        return ruleBuilder.ToString();
        //    }
        //}

        public string  Time
        {
            get
            {


                if (StartTime == null || EndTime == null) return string.Empty;


                return $"{ConvertUnixTimestampToLocalTime((long)StartTime)}~{ConvertUnixTimestampToLocalTime((long)EndTime)}"; 
               
            }
        }
        /// <summary>
        /// 有效期 (fixed:固定时间 receive:领取时间)
        /// </summary>
        public System.String ValidityType { get; set; }
        public System.SByte IsExpired { get; set; }
        public string ActiveStatus
        {
            get
            {
                if (StartTime == null || EndTime == null) return string.Empty;
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                // 主逻辑判断
                if (ValidityType == ValidityTypeEnum.Receive.ToString().ToLower() ||
                    (StartTime < currentTime && currentTime < EndTime))
                {

                    return "Already received";
                }
                else if (StartTime > currentTime)
                {
                    return "Already received";
                }
                else
                {
                    return "Expired";
                }
            }
        }

        public  string ConvertUnixTimestampToLocalTime( long timestamp, string format = "yyyy-MM-dd HH:mm:ss")
        {
            // 自动判断时间戳单位（秒或毫秒）
            bool isMilliseconds = timestamp > 9_999_999_999L; // 10位最大秒级时间戳为9999999999（2286年）

            DateTimeOffset dateTimeOffset = isMilliseconds ?
                DateTimeOffset.FromUnixTimeMilliseconds(timestamp) :
                DateTimeOffset.FromUnixTimeSeconds(timestamp);

            return dateTimeOffset.ToLocalTime().ToString(format);
        }
    }
}
