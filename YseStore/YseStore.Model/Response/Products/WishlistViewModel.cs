
namespace YseStore.Model.Response.Products
{
    /// <summary>
    /// 收藏列表页面视图模型
    /// </summary>
    public class WishlistViewModel
    {
        /// <summary>
        /// 收藏ID
        /// </summary>
        public int FavoriteId { get; set; }
        
        /// <summary>
        /// 产品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品页面URL
        /// </summary>
        public string PageUrl { get; set; }
        
        /// <summary>
        /// 产品图片路径
        /// </summary>
        public string PicPath { get; set; }
        
        /// <summary>
        /// 产品当前价格
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 产品原价（如果有折扣）
        /// </summary>
        public decimal? OriginalPrice { get; set; }

        /// <summary>
        /// 格式化后的当前价格（包含币种符号）
        /// </summary>
        public string PriceFormat { get; set; }

        /// <summary>
        /// 格式化后的原价（包含币种符号）
        /// </summary>
        public string OriginalPriceFormat { get; set; }

        /// <summary>
        /// 促销价格
        /// </summary>
        public decimal PromotionPrice { get; set; }

        /// <summary>
        /// 格式化后的促销价格（包含币种符号）
        /// </summary>
        public string PromotionPriceFormat { get; set; }
        
        /// <summary>
        /// 产品评分
        /// </summary>
        public decimal Rating { get; set; }
        
        /// <summary>
        /// 产品是否有库存
        /// </summary>
        public bool IsInStock { get; set; }
        
        /// <summary>
        /// 产品类型标签（New, Hot, Out Of Stock, n% Off 等）
        /// </summary>
        public string TypeLabel { get; set; }
        
        /// <summary>
        /// 折扣百分比
        /// </summary>
        public int? DiscountPercent { get; set; }
    }
} 