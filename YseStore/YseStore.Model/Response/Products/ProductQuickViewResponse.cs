using Entitys;

namespace YseStore.Model.Response.Products
{
    /// <summary>
    /// 产品快速预览响应模型
    /// </summary>
    public class ProductQuickViewResponse
    {
        /// <summary>
        /// 产品ID
        /// </summary>
        public int ProductId { get; set; }
        public List<products_images> ProductImages { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        public string PromotionPriceFormat { get; set; }
        
        /// <summary>
        /// 产品图片路径
        /// </summary>
        public string PicPath { get; set; }
        
        /// <summary>
        /// 产品价格
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 格式化后的产品价格
        /// </summary>
        public string PriceFormat { get; set; }

        /// <summary>
        /// 原价
        /// </summary>
        public decimal? OriginalPrice { get; set; }

        /// <summary>
        /// 格式化后的原价
        /// </summary>
        public string OriginalPriceFormat { get; set; }
        
        /// <summary>
        /// 产品评分
        /// </summary>
        public decimal? Rating { get; set; }
        
        /// <summary>
        /// 评论数量
        /// </summary>
        public int ReviewCount { get; set; }
        
        /// <summary>
        /// 品牌名称
        /// </summary>
        public string BrandName { get; set; }
        
        /// <summary>
        /// 分类名称
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// 是否有库存
        /// </summary>
        public bool IsInStock { get; set; }
        
        /// <summary>
        /// 产品编码
        /// </summary>
        public string SKU { get; set; }
        
        /// <summary>
        /// 产品简短描述
        /// </summary>
        public string BriefDescription { get; set; }
        
        /// <summary>
        /// 产品详情链接
        /// </summary>
        public string DetailUrl => $"/shop/single/{ProductId}";

        /// <summary>
        /// 规格模式
        /// </summary>
        public int IsCombination { get; set; }

        /// <summary>
        /// 可选仓库列表
        /// </summary>
        public List<shipping_overseas> OptionalWarehouses { get; set; } = new List<shipping_overseas>();

        /// <summary>
        /// 产品变体列表
        /// </summary>
        public List<products_selected_attribute_combination> ProductVariants { get; set; } =
            new List<products_selected_attribute_combination>();

        /// <summary>
        /// 动态产品属性选项（根据Name_en动态设置键名）
        /// 键为属性名称（Name_en），值为该属性的选项列表
        /// </summary>
        public Dictionary<string, List<AttributeOption>> DynamicAttributes { get; set; } =
            new Dictionary<string, List<AttributeOption>>();

        /// <summary>
        /// 是否已收藏
        /// </summary>
        public bool IsFavorited { get; set; }
    }
} 