using System;

namespace YseStore.Model.Response.Set
{
    /// <summary>
    /// 配送地区与快递信息关联响应类
    /// </summary>
    public class ShippingAreaWithShippingResponse
    {
        /// <summary>
        /// 快递公司分区ID
        /// </summary>
        public ushort AId { get; set; }

        /// <summary>
        /// 快递公司ID
        /// </summary>
        public ushort? SId { get; set; }

        /// <summary>
        /// 配送地区名称 (仅后台可见)
        /// </summary>
        public string AreaName { get; set; }

        /// <summary>
        /// 是否免运费
        /// </summary>
        public bool? IsFreeShipping { get; set; }

        /// <summary>
        /// 免费类型 freight:运费 surcharge:附加费
        /// </summary>
        public string FreeType { get; set; }

        /// <summary>
        /// 免费条件
        /// </summary>
        public string FreeShippingType { get; set; }

        /// <summary>
        /// 免费条件-最低消费金额
        /// </summary>
        public decimal? FreeShippingPrice { get; set; }

        /// <summary>
        /// 免费条件-最高产品重量
        /// </summary>
        public float? FreeShippingWeight { get; set; }

        /// <summary>
        /// 免费条件-最高产品重量(记录)
        /// </summary>
        public float FreeShippingSaveWeight { get; set; }

        /// <summary>
        /// 免费条件-最低购买数量
        /// </summary>
        public int FreeShippingQty { get; set; }

        /// <summary>
        /// 附加费用
        /// </summary>
        public decimal? AffixPrice { get; set; }

        /// <summary>
        /// 预计到达时间描述
        /// </summary>
        public string Brief { get; set; }

        /// <summary>
        /// 重量单位
        /// </summary>
        public string WeightUnit { get; set; }

        /// <summary>
        /// 是否支持货到付款
        /// </summary>
        public bool? IsSupportDelivery { get; set; }

        /// <summary>
        /// 是否兼容转移（临时）
        /// </summary>
        public bool? IsChange { get; set; }

        /// <summary>
        /// 分区配送范围(country:指定国家/地区 zipCode:指定邮政编码)
        /// </summary>
        public string DeliveryRange { get; set; }

        /// <summary>
        /// 计费方式 (来自shipping表)
        /// </summary>
        public byte? IsWeightArea { get; set; }

        /// <summary>
        /// 快递公司名称 (来自shipping表)
        /// </summary>
        public string Express { get; set; }

        /// <summary>
        /// 快递公司Logo (来自shipping表)
        /// </summary>
        public string Logo { get; set; }

        /// <summary>
        /// 是否可用 (来自shipping表)
        /// </summary>
        public bool? IsGet { get; set; }

        /// <summary>
        /// 是否使用 (来自shipping表)
        /// </summary>
        public byte? IsUsed { get; set; }

        /// <summary>
        /// 是否为API物流 (来自shipping表)
        /// </summary>
        public bool? IsAPI { get; set; }

        /// <summary>
        /// 限制条件 (商品重量 商品数量 商品总价) (来自shipping表)
        /// </summary>
        public int? UseCondition { get; set; }

        /// <summary>
        /// 最小重量 (来自shipping表)
        /// </summary>
        public decimal? MinWeight { get; set; }

        /// <summary>
        /// 最大重量 (来自shipping表)
        /// </summary>
        public decimal? MaxWeight { get; set; }

        /// <summary>
        /// 运费调整 (正数是增加运费 负数是减少运费) (来自shipping表)
        /// </summary>
        public decimal FreightRate { get; set; }

        /// <summary>
        /// 运费模板ID (来自shipping表)
        /// </summary>
        public string TId { get; set; }

        /// <summary>
        /// 产品类型：1普货 2敏感 3带电 (来自shipping表)
        /// </summary>
        public string GoodsType { get; set; }

        /// <summary>
        /// 仓库ID (来自shipping表)
        /// </summary>
        public string OvId { get; set; }

        /// <summary>
        /// 重量类型 (来自shipping表)
        /// </summary>
        public sbyte? WeightType { get; set; }
    }
}
