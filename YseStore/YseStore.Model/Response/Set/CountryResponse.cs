using System.Collections.Generic;

namespace YseStore.Model.Response.Set
{
    /// <summary>
    /// 国家响应类
    /// </summary>
    public class CountryResponse
    {
        /// <summary>
        /// 国家ID
        /// </summary>
        public int CId { get; set; }

        /// <summary>
        /// 国家名称
        /// </summary>
        public string Country { get; set; }

        /// <summary>
        /// 国家代码
        /// </summary>
        public string Acronym { get; set; }

        /// <summary>
        /// 国旗路径
        /// </summary>
        public string FlagPath { get; set; }

        /// <summary>
        /// 是否有州/省
        /// </summary>
        public bool HasStates { get; set; }

        /// <summary>
        /// 州/省列表
        /// </summary>
        public List<StateResponse> States { get; set; } = new List<StateResponse>();
    }
} 