using Entitys;
using Newtonsoft.Json;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace YseStore.Model;

public class UserResponse
{

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public System.Int32 UserId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean? IsChecked { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Language { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String FacebookId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String TwitterId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String GoogleId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String PaypalId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String VKId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String InstagramId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean? Gender { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32 CountryId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String FirstName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String LastName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Email { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Password { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? SalesId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? Level { get; set; }

    /// <summary>
    /// 会员等级升级类型
    /// </summary>
    public System.String LevelUpdateType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean? IsLocked { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.SByte? Age { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String NickName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Telephone { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Fax { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Birthday { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String DelBirthday { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Facebook { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Company { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Other { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? RegTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String RegIp { get; set; }
    public System.String RegIpName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? LastLoginTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String LastLoginIp { get; set; }
    public System.String LastLoginIpName { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public System.Int32? LoginTimes { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal? Consumption { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Byte? Status { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Remark { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String DISTUId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int16 DISTDept { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal DISTBalance { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal DISTTotalBalance { get; set; }

    /// <summary>
    /// 0：会员可正常登录，1: 屏蔽会员登录
    /// </summary>
    public System.Boolean Locked { get; set; }

    /// <summary>
    /// 消费次数
    /// </summary>
    public System.Int16? ConsumptionTime { get; set; }

    /// <summary>
    /// 是否注册1:注册,0:未注册
    /// </summary>
    public System.Boolean? IsRegistered { get; set; }

    /// <summary>
    /// 邮件订阅： 1 订阅 0 不订阅
    /// </summary>
    public System.SByte IsNewsletter { get; set; }

    /// <summary>
    /// 召回订阅: 1订阅 0不订阅
    /// </summary>
    public System.Boolean? IsSubscribe { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int RefererId { get; set; }

    /// <summary>
    /// 来源名称
    /// </summary>
    public System.String RefererName { get; set; }

    /// <summary>
    /// 会员标签
    /// </summary>
    public System.String Tags { get; set; }

    /// <summary>
    /// 是否免征税 0未免税 1已免税
    /// </summary>
    public System.Boolean IsTaxExempt { get; set; }

    /// <summary>
    /// 最后操作站内信的时间戳(用于后台排序)
    /// </summary>
    public System.Int32 MessageTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Source { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String SourceId { get; set; }

    /// <summary>
    /// 审核状态 pending-待审核 passed-已通过 rejected-已拒绝
    /// </summary>
    public System.String ReviewStatus { get; set; }

    /// <summary>
    /// 拒绝原因
    /// </summary>
    public System.String RejectReason { get; set; }

    /// <summary>
    /// 免运费
    /// </summary>
    public System.Boolean IsFreeShipping { get; set; }

    /// <summary>
    /// 积分余额
    /// </summary>
    public System.Int32 Points { get; set; }

    /// <summary>
    /// 累计总积分
    /// </summary>
    public System.Int32 TotalPoints { get; set; }
    /// <summary>
    /// 币种
    /// </summary>
    public System.String Currency { get; set; }


    public System.Int32 BuyCount { get; set; }

    public System.Int32 OrdersCount { get; set; }
    public System.Int32 PaymentOrderCount { get; set; }
    public string LastLoginIpCountry { get; set; }
    public string LastLoginIpCountryStr { get; set; }

    /// <summary>
    /// 0:默认网站注册，1：Google授权，2：领英授权，3：Facebook授权
    /// </summary>
    public System.Int16? AccountType { get; set; }

    /// <summary>
    /// 第三方授权软件id
    /// </summary>
    public string SoftwareAccountID { get; set; }


    /// <summary>
    /// 注册链接
    /// </summary>
    public string RegisterUrl { get; set; }

    /// <summary>
    /// 订单总额
    /// </summary>
    public decimal OrderSum { get; set; }
    /// <summary>
    /// 订单总额符号
    /// </summary>
    public string OrderSymbol { get; set; }

    public List<user_label_collection> tagsList { get; set; }

    public static explicit operator UserResponse(user data)
    {
        var json = JsonConvert.SerializeObject(data);
        var obj = JsonConvert.DeserializeObject<UserResponse>(json);
        
        if (obj == null) return null;;
        return obj;

    

    }
}


