using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.Enums
{
    public enum ValidityTypeEnum
    {
        [Description("fixed")]
        Fixed,
        [Description("receive")]
        Receive
    }
    //(manual:手动 auto:自动
    public enum DeliveryMethodEnum
    {

        [Description("手动投放")]
        manual,
        [Description("自动投放")]
        auto
    }
    public enum ActiveStatusEnum
    {
        [Description("未开始")]
        未开始,
        [Description("进行中")]
        进行中,
        [Description("已结束")]
        已结束
    }

    public enum ProductScope 
    {
        AllProducts = 0,
        SpecificProducts = 1,
        SpecificCategory = 2
    }
    public enum CouponTypeEnum
    {
        [Description("折扣券")]
        Discount = 0,
        [Description("现金券")]
        Cash = 1
    }

    public enum ConditionTypeEnum
    {
        //最低消费金额 1:最低购买数量
        [Description("最低消费金额")]
        MinAmount = 0,
        [Description("最低购买数量")]
        MinQuantity = 1
    }

    public enum GroupEnum
    {
        
        [Description("会员")]
        member,
        [Description("已购客户")]
        purchased,
        [Description("复购客户")]
        repurchase,
    }

    public enum UseCustomerEnum
    {

        [Description("所有人")]
        all,
        [Description("客户组")]
        group,
        [Description("客户标签")]
        member_tag,
        [Description("个别客户")]
        individual
    }
    public class UseCustomerConst
    {
        public const string ALL = "all";
        public const string GROUP = "group";
        public const string MEMBER_TAG = "member_tag";
        public const string INDIVIDUAL = "individual";
    }
    /// <summary>
    /// register:完成会员注册 member:登录成功 review:评论成功
    /// </summary>
    public class GiftMethodConst
    {
        public const string Register = "register";
        public const string Member = "member";
        public const string Review = "review";
        public const string ConfirmReceipt = "confirmReceipt";
        public const string OrderCompleted = "orderCompleted";
        

    }
    /// <summary>
    /// 优惠类型 0-满额减钱 1-满额打折 2-满件减钱 3-满件打折 4-每满额减钱 5-每满件减钱 6-满额赠礼 7-满件赠礼 8-每满额赠礼 9-每满件赠礼
    /// </summary>
    public enum DiscountTypeEnum
    {
        [Description("满额减钱")]
        FullAmountReduceMoney = 0,
        [Description("满额打折")]
        FullAmountDiscount = 1,
        [Description("满件减钱")]
        FullQuantityReduceMoney = 2,
        [Description("满件打折")]
        FullQuantityDiscount = 3,
        [Description("每满额减钱")]
        EveryFullAmountReduceMoney = 4,
        [Description("每满件减钱")]
        EveryFullQuantityReduceMoney = 5,
        [Description("满额赠礼")]
        FullAmountGift = 6,
        [Description("满件赠礼")]
        FullQuantityGift = 7,
        [Description("每满额赠礼")]
        EveryFullAmountGift = 8,
        [Description("每满件赠礼")]
        EveryFullQuantityGift = 9,
        [Description("满额减钱+赠礼")]
        FullAmountDiscountGift = 10,
        [Description("每满额减钱+赠礼")]
        EveryFullAmountDiscountGift = 11,
        [Description("满件减钱+赠礼")]
        FullQuantityFreeGift = 12,
        [Description("每满件减钱+赠礼")]
        EveryFullQuantityFreeGift = 13,
        [Description("满额打折+赠礼")]
        FullAmountPurchaseGift = 14,
        [Description("满件打折+赠礼")]
        FullQuantityPurchaseGift = 15,
    }
    /// <summary>
    /// 指定类型 0-所有产品 1-指定产品 2-指定分类
    /// </summary>
    public enum UseProductsEnum
    { 
        [Description("所有产品")]
        AllProducts = 0,
        [Description("指定产品")]
        SpecificProducts = 1,
        [Description("指定分类")]
        SpecificCategory = 2
    }
    public enum ActConditionEnum
    {
        [Description("满额减")]
        FullAmountReduce = 0,
        [Description("满件减")]
        FullQuantityReduce = 1,
    }
    public enum ActTypeEnum
    {
        [Description("减金额")]
        AmountReduce = 0,
        [Description("打折")]
        Discount = 1,
        [Description("赠品")]
        Gift = 2
    }
    public enum ActRuleEnum
    {
        [Description("阶梯优惠")]
        TieredDiscount = 0,
        [Description("循环优惠")]
        LoopDiscount = 1
    }
    /// <summary>
    ///  0:固定折扣 1:现金减价 2:一口价
    /// </summary>
    public enum OfferTypeEnum
    {
        [Description("固定折扣")]
        Fixed = 0,
        [Description("现金减价")]
        Cash = 1,
        [Description("一口价")]
        OnePrice = 2
    }
    public enum OperateCategoryEnum
    {
        [Description("用户订阅")]
        newsletter ,
        [Description("注册会员")]
        user ,
        [Description("购物车挽留购买")]
        retain,
        [Description("普通弹窗")]
        discount,
        [Description("头部公告")]
        head,
        [Description("未成年提醒")]
        minor,
        [Description("优惠券投放")]
        coupon,
    }



}
