namespace YseStore.Model.RequestModels.Set
{
    /// <summary>
    /// 配送地区请求模型
    /// </summary>
    public class ShippingAreaRequest
    {
        /// <summary>
        /// 地区名称
        /// </summary>
        public string AreaName { get; set; }

        /// <summary>
        /// 运费
        /// </summary>
        public string? FixedPrice { get; set; }

        public string AId { get; set; }

        /// <summary>
        /// 预计到达时间
        /// </summary>
        public string Brief { get; set; }

        /// <summary>
        /// 重量单位
        /// </summary>
        public string WeightUnit { get; set; }

        /// <summary>
        /// 是否免运费
        /// </summary>
        public string IsFreeShipping { get; set; } = String.Empty;

        /// <summary>
        /// 配送范围类型
        /// </summary>
        public string DeliveryRange { get; set; }

        /// <summary>
        /// 附加费用
        /// </summary>
        public Decimal? AffixPrice { get; set; } = 0;

        /// <summary>
        /// 免运费价格
        /// </summary>
        public string FreeShippingPrice { get; set; } = String.Empty;

        /// <summary>
        /// 免运费数量
        /// </summary>
        public string FreeShippingQty { get; set; } = String.Empty;

        public string IsSupportDelivery { get; set; } = String.Empty;

        /// <summary>
        /// 免运费类型
        /// </summary>
        public string FreeShippingType { get; set; } = String.Empty;

        /// <summary>
        /// 免运费重量
        /// </summary>
        public string FreeShippingWeight { get; set; }

        /// <summary>
        /// 费用类型
        /// </summary>
        public List<string> FreeType { get; set; }

        /// <summary>
        /// 模型
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// 标识ID
        /// </summary>
        public string SId { get; set; }

        /// <summary>
        /// 重量单位
        /// </summary>
        public string WeightUnit1 { get; set; } = String.Empty;

        /// <summary>
        /// 位置
        /// </summary>
        public string LdContains { get; set; } = String.Empty;

        /// <summary>
        /// 价格列表
        /// </summary>
        public PriceListItem PriceList { get; set; } = new PriceListItem();

        /// <summary>
        /// 计算方式
        /// </summary>
        public string Calculation { get; set; } = String.Empty;

        /// <summary>
        /// 地区ID列表
        /// </summary>
        public List<string> CId { get; set; } = new List<string>();

        /// <summary>
        /// 重量区域
        /// </summary>
        public List<WeightAreaItem> WeightArea { get; set; } = new List<WeightAreaItem>();

        /// <summary>
        /// 状态对应的地区ID
        /// </summary>
        public dynamic StatesSId { get; set; } = String.Empty;

        /// <summary>
        /// 配送数据
        /// </summary>
        public dynamic? DeliveryData { get; set; }
    }

    /// <summary>
    /// 价格列表项
    /// </summary>
    public class PriceListItem
    {
        /// <summary>
        /// 计算方式
        /// </summary>
        public string[] Calculation { get; set; } = new string[0];

        /// <summary>
        /// 数据
        /// </summary>
        public string[] Data { get; set; } = new string[0];

        /// <summary>
        /// 价格ID
        /// </summary>
        public string[] Id { get; set; } = new string[0];

        /// <summary>
        /// 重量区域
        /// </summary>
        public string[] WeightArea { get; set; } = new string[0];
    }


    /// <summary>
    /// 重量区域项
    /// </summary>
    public class WeightAreaItem
    {
        /// <summary>
        /// 起始重量
        /// </summary>
        public string StartWeight { get; set; }

        /// <summary>
        /// 结束重量
        /// </summary>
        public string EndWeight { get; set; }
    }
}