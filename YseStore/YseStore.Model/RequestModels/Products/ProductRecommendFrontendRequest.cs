namespace YseStore.Model.RequestModels.Products
{
    /// <summary>
    /// 前端推荐产品查询请求参数
    /// </summary>
    public class ProductRecommendFrontendRequest
    {
        /// <summary>
        /// 当前页面类型
        /// </summary>
        public string CurrentPage { get; set; } = string.Empty;

        /// <summary>
        /// 展示类型
        /// </summary>
        public string ShowType { get; set; } = string.Empty;

        /// <summary>
        /// 当前币种
        /// </summary>
        public string CurrentCurrency { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; } = 0;

        /// <summary>
        /// 分类ID（可选）
        /// </summary>
        public int CategoryId { get; set; } = 0;

        /// <summary>
        /// 产品ID（可选）
        /// </summary>
        public int ProductId { get; set; } = 0;
    }
}
