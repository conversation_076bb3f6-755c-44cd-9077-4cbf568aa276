using System;

namespace YseStore.Model.RequestModels.Products
{
    /// <summary>
    /// 产品评论回复请求模型
    /// </summary>
    public class ProductReviewReplyRequest
    {
        /// <summary>
        /// 回复者姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 回复内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 是否为管理员回复
        /// </summary>
        public bool IsAdmin { get; set; } = false;
        
        /// <summary>
        /// 验证码验证参数
        /// </summary>
        public string CaptchaVerifyParam { get; set; }

        /// <summary>
        /// 被回复的回复ID（用于嵌套回复）
        /// </summary>
        public int? ReplyToId { get; set; }

        /// <summary>
        /// 被回复人的名称（用于嵌套回复）
        /// </summary>
        public string ReplyToName { get; set; }
    }
} 