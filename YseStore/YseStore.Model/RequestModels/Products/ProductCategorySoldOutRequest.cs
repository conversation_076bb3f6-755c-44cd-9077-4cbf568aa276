namespace YseStore.Model.RequestModels.Products
{
    /// <summary>
    /// 产品分类下架请求参数
    /// </summary>
    public class ProductCategorySoldOutRequest
    {
        /// <summary>
        /// 分类ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 是否下架 (true: 下架, false: 上架)
        /// </summary>
        public bool SoldOut { get; set; } = true;
        
        /// <summary>
        /// 是否下架属于此分类和子分类的产品
        /// </summary>
        public bool SoldProduct { get; set; } = false;
        
        /// <summary>
        /// 产品范围 (only: 仅属于此分类及其子分类的产品)
        /// </summary>
        public string ProductRange { get; set; } = "all";
        
        /// <summary>
        /// 是否同步删除对应导航与子导航
        /// </summary>
        public bool SoldNav { get; set; } = true;
        
        /// <summary>
        /// 导航处理方式
        /// </summary>
        public int OutWithNavRadio { get; set; } = 0;
    }

    /// <summary>
    /// 产品转移请求参数
    /// </summary>
    public class ProductTransferRequest
    {
        /// <summary>
        /// 当前分类ID
        /// </summary>
        public int CateId { get; set; }

        /// <summary>
        /// 目标分类ID
        /// </summary>
        public int TransferCateId { get; set; }

        /// <summary>
        /// 目标分类类型
        /// </summary>
        public string TransferCateIdType { get; set; } = string.Empty;
    }
}