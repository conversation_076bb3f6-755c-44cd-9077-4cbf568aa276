namespace YseStore.Model.RequestModels.Products
{
    /// <summary>
    /// 推荐产品查询请求参数
    /// </summary>
    public class ProductRecommendQueryRequest
    {
        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 搜索关键词（标题）
        /// </summary>
        public string Keyword { get; set; } = string.Empty;

        /// <summary>
        /// 推荐类型筛选
        /// </summary>
        public string ProductsType { get; set; } = string.Empty;

        /// <summary>
        /// 配置方式筛选
        /// </summary>
        public string ProductsScope { get; set; } = string.Empty;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string OrderByFileds { get; set; } = "AccTime DESC";

        /// <summary>
        /// 是否启用分页
        /// </summary>
        public bool IsPaging { get; set; } = true;

        /// <summary>
        /// 查询类型：recommend(推荐产品页面) 或 recommend_set(推荐集页面)
        /// </summary>
        public string QueryType { get; set; } = "recommend";
    }

    /// <summary>
    /// 推荐产品更新请求参数
    /// </summary>
    public class ProductRecommendUpdateRequest
    {
        /// <summary>
        /// 推荐产品ID（0表示新增）
        /// </summary>
        public int Id { get; set; } = 0;

        /// <summary>
        /// 展示类型：auto(自动添加) 或 manual(手动添加)
        /// </summary>
        public string ShowType { get; set; } = "auto";

        /// <summary>
        /// 显示位置
        /// </summary>

        public string ShowPosition { get; set; } = string.Empty;

        /// <summary>
        /// 风格
        /// </summary>
        public string Mode { get; set; } = string.Empty;

        /// <summary>
        /// 显示页面数组
        /// </summary>
        public string[]? Page { get; set; } = new string[0];

        /// <summary>
        /// 推荐产品类型：browse, new, sales, hot, special, related
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 产品范围：category, tags, products（仅当Type为special或related时使用）
        /// </summary>
        public string? ProductsScope { get; set; } = string.Empty;

        /// <summary>
        /// 产品范围值（JSON字符串）
        /// </summary>
        public string? ProductsValue { get; set; } = string.Empty;

        /// <summary>
        /// 产品信息（隐藏字段，JSON字符串）
        /// </summary>
        public string? Products { get; set; } = string.Empty;

        /// <summary>
        /// 产品类型（隐藏字段）
        /// </summary>
        public string? ProductsType { get; set; } = string.Empty;

        /// <summary>
        /// 排除产品值（JSON字符串）
        /// </summary>
        public string? ExcludeValue { get; set; } = string.Empty;

        /// <summary>
        /// 排除产品信息（隐藏字段，JSON字符串）
        /// </summary>
        public string? Exclude { get; set; } = string.Empty;

        /// <summary>
        /// 排除产品类型（隐藏字段）
        /// </summary>
        public string? ExcludeType { get; set; } = string.Empty;

        /// <summary>
        /// 扩展数据，包含标题等信息
        /// </summary>
        public ProductRecommendDataRequest Data { get; set; } = new ProductRecommendDataRequest();

        /// <summary>
        /// 显示数量（1-20）
        /// </summary>
        public int Quantity { get; set; } = 10;
    }

    /// <summary>
    /// 推荐产品扩展数据
    /// </summary>
    public class ProductRecommendDataRequest
    {
        /// <summary>
        /// 主标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 副标题
        /// </summary>
        public string SubTitle { get; set; } = string.Empty;
    }
}